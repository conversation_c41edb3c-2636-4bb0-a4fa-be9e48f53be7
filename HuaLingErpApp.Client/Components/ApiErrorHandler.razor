@using Microsoft.AspNetCore.Components
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@implements IDisposable

@ChildContent

@code {
    [Parameter] public RenderFragment? ChildContent { get; set; }

    private static event Action<string>? OnApiError;
    private bool hasShownAuthError = false;

    protected override void OnInitialized() {
        OnApiError += HandleApiError;
    }

    private async void HandleApiError(string errorType) {
        if (errorType == "authentication" && !hasShownAuthError) {
            hasShownAuthError = true;

            try {
                // Show a friendly message before redirecting
                await JSRuntime.InvokeVoidAsync("alert", "Your session has expired. Please log in again.");

                // Redirect to login
                await InvokeAsync(() => Navigation.NavigateTo("/login", true));
            }
            catch {
                // Fallback if JavaScript fails
                Navigation.NavigateTo("/login", true);
            }
        }
    }

    public static void NotifyAuthenticationError() {
        OnApiError?.Invoke("authentication");
    }

    public void Dispose() {
        OnApiError -= HandleApiError;
    }

}
