@using Microsoft.AspNetCore.Components.Authorization
@using System.Globalization
@inherits LayoutComponentBase
@implements IDisposable

<CascadingAuthenticationState>
    <BootstrapBlazorRoot>
        <Layout SideWidth="0" IsPage="true" ShowGotoTop="true" ShowCollapseBar="true"
                IsFullSide="@IsFullSide" IsFixedHeader="@IsFixedHeader" IsFixedFooter="@IsFixedFooter"
                ShowFooter="@ShowFooter"
                TabDefaultUrl="/"
                Menus="@Menus" UseTabSet="@UseTabSet"
                AdditionalAssemblies="new[] { GetType().Assembly, typeof(Client.Pages.Counter).Assembly }"
                class="@Theme">
            <Header>
                <span class="ms-3 flex-sm-fill d-none d-sm-block">HuaLing ERP System</span>
                <div class="flex-fill d-sm-none"></div>
                <Widget></Widget>
                <Logout ImageUrl="images/Argo-C.png" DisplayName="@CurrentDisplayName" UserName="@CurrentUserName">
                    <LinkTemplate>
                        <a href="/profile/change-password"><i class="fa-solid fa-user"></i>Profile</a>
                        <a href="/profile/change-password"><i class="fa-solid fa-cog"></i>Settings</a>
                        <a href="/api/auth/logout"><i class="fa-solid fa-sign-out-alt"></i>Logout</a>
                    </LinkTemplate>
                </Logout>
                <div class="layout-drawer" @onclick="@(e => IsOpen = !IsOpen)"><i class="fa fa-palette"></i></div>
            </Header>
            <Side>
                <div class="layout-banner">
                    <img class="layout-logo" src="favicon.png"/>
                    <div class="layout-title">
                        <span>HuaLing ERP</span>
                    </div>
                </div>
            </Side>
            <Main>
                <CascadingValue Value="this" IsFixed="true">
                    @Body
                </CascadingValue>
            </Main>
            <Footer>
                <div class="text-center flex-fill">
                    <span>&copy; 2025 HuaLing ERP System. All rights reserved.</span>
                </div>
            </Footer>
            <NotFound>
                <p>Sorry, there's nothing at this address.</p>
            </NotFound>
        </Layout>

        <Drawer Placement="Placement.Right" @bind-IsOpen="@IsOpen" IsBackdrop="true">
            <div class="layout-drawer-body">
                <div class="btn btn-info w-100" @onclick="@(e => IsOpen = false)">Close</div>
                <GroupBox Title="Layout Settings">
                    <div class="row">
                        <div class="col-6">
                            <div class="layout-item @(IsFullSide ? "active d-flex" : "d-flex")"
                                 @onclick="@(e => IsFullSide = true)" data-toggle="tooltip" title="Left-Right Layout">
                                <div class="layout-left d-flex flex-column">
                                    <div class="layout-left-header"></div>
                                    <div class="layout-left-body flex-fill"></div>
                                </div>
                                <div class="layout-right d-flex flex-column flex-fill">
                                    <div class="layout-right-header"></div>
                                    <div class="layout-right-body flex-fill"></div>
                                    <div class="layout-right-footer"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="layout-item flex-column @(IsFullSide ? "d-flex" : "active d-flex")"
                                 @onclick="@(e => IsFullSide = false)" data-toggle="tooltip" title="Top-Bottom Layout">
                                <div class="layout-top">
                                </div>
                                <div class="layout-body d-flex flex-fill">
                                    <div class="layout-left">
                                    </div>
                                    <div class="layout-right flex-fill">
                                    </div>
                                </div>
                                <div class="layout-right-footer">
                                </div>
                            </div>
                        </div>
                    </div>
                </GroupBox>

                <GroupBox Title="固定调整">
                    <div class="row">
                        <div class="col-6 d-flex align-items-center">
                            <Switch @bind-Value="@IsFixedHeader" OnColor="@Color.Success"
                                    OffColor="@Color.Secondary"></Switch>
                        </div>
                        <div class="col-6 text-right">
                            <span class="cell-label">固定页头</span>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-6 d-flex align-items-center">
                            <Switch @bind-Value="@IsFixedFooter" OnColor="@Color.Success"
                                    OffColor="@Color.Secondary"></Switch>
                        </div>
                        <div class="col-6 text-right">
                            <span class="cell-label">固定页脚</span>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-6 d-flex align-items-center">
                            <Switch @bind-Value="@ShowFooter" OnColor="@Color.Success"
                                    OffColor="@Color.Primary"></Switch>
                        </div>
                        <div class="col-6 text-right">
                            <span class="cell-label">显示页脚</span>
                        </div>
                    </div>
                </GroupBox>

                <GroupBox Title="更多设置">
                    <div class="row">
                        <div class="col-6 d-flex align-items-center">
                            <Switch @bind-Value="@UseTabSet" OnColor="@Color.Success"
                                    OffColor="@Color.Primary"></Switch>
                        </div>
                        <div class="col-6 text-right">
                            <span class="cell-label">@(UseTabSet ? "Multi-Tab" : "Single Page")</span>
                        </div>
                    </div>
                </GroupBox>
            </div>
        </Drawer>

        <div id="blazor-error-ui">
            An unhandled error has occurred.
            <a href="" class="reload">Reload</a>
            <a class="dismiss">🗙</a>
        </div>
    </BootstrapBlazorRoot>
</CascadingAuthenticationState>