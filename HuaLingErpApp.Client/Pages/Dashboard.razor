@page "/"
@using System.Security.Claims
@inject AuthenticationStateProvider AuthStateProvider
@inject ApiService Api

<SecurePage RequiredRoles="Administrator,Manager,User,Operator" PageName="Dashboard">
    <PageTitle>Dashboard - HuaLing ERP</PageTitle>

    <div class="container-fluid">
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-3">Welcome to HuaLing ERP System</h1>
                <p class="text-muted">Hello @currentUserName, welcome back to your dashboard.</p>
            </div>
        </div>

        <div class="row">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    System Status
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">Online</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-server fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    Your Role
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    @(string.Join(", ", currentUserRoles))
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-user-shield fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    Last Login
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    @lastLoginDisplay
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    Session Time
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                    @sessionTime
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-hourglass-half fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <a href="/procurement/purchaseorders" class="btn btn-outline-primary btn-block">
                                    <i class="fas fa-file-invoice-dollar"></i>
                                    Purchase Orders
                                </a>
                            </div>
                            <div class="col-md-6 mb-3">
                                <a href="/procurement/purchaseorderlines" class="btn btn-outline-success btn-block">
                                    <i class="fas fa-list"></i>
                                    Order Lines
                                </a>
                            </div>
                            @if (currentUserRoles.Contains("Administrator")) {
                                <div class="col-md-6 mb-3">
                                    <a href="/admin/users" class="btn btn-outline-warning btn-block">
                                        <i class="fas fa-users"></i>
                                        User Management
                                    </a>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <a href="/hangfire" class="btn btn-outline-info btn-block">
                                        <i class="fas fa-tasks"></i>
                                        System Jobs
                                    </a>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6 mb-4">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">System Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <p><strong>Application:</strong> HuaLing ERP</p>
                                <p><strong>Version:</strong> 1.0.0</p>
                                <p><strong>Environment:</strong> @environment</p>
                            </div>
                            <div class="col-sm-6">
                                <p><strong>Server Time:</strong> @DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</p>
                                <p><strong>Framework:</strong> .NET 9.0</p>
                                <p><strong>UI Framework:</strong> Bootstrap Blazor</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Getting Started</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <h6>For Administrators:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> Manage user accounts</li>
                                    <li><i class="fas fa-check text-success"></i> Configure system settings</li>
                                    <li><i class="fas fa-check text-success"></i> Monitor system performance</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6>For Managers:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> Review purchase orders</li>
                                    <li><i class="fas fa-check text-success"></i> Approve transactions</li>
                                    <li><i class="fas fa-check text-success"></i> Generate reports</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <h6>For Operators:</h6>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-check text-success"></i> Create purchase orders</li>
                                    <li><i class="fas fa-check text-success"></i> Process receipts</li>
                                    <li><i class="fas fa-check text-success"></i> Update inventory</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }

        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }

        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }

        .border-left-warning {
            border-left: 0.25rem solid #f6c23e !important;
        }

        .text-gray-800 {
            color: #5a5c69 !important;
        }

        .text-gray-300 {
            color: #dddfeb !important;
        }

        .btn-block {
            width: 100%;
        }
    </style>
</SecurePage>

@code {
    private string currentUserName = "Guest";
    private List<string> currentUserRoles = new();
    private string lastLoginDisplay = "N/A";
    private string sessionTime = "N/A";
    private string environment = "Development";
    private DateTime sessionStartTime = DateTime.Now;
    private System.Threading.Timer? timer;

    protected override async Task OnInitializedAsync() {
        await UpdateUserInfo();

        // Start timer to update session time
        timer = new System.Threading.Timer(UpdateSessionTime, null, TimeSpan.Zero, TimeSpan.FromSeconds(1));
    }

    private async Task UpdateUserInfo() {
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        var user = authState.User;

        if (user.Identity?.IsAuthenticated == true) {
            currentUserName = user.Identity.Name ?? "Unknown";
            currentUserRoles = user.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();

            // 获取用户详细信息包括上次登录时间
            try {
                var response = await Api.GetAsync<HttpResponseModel<UserInfo>>("api/auth/me");
                if (response != null && response.IsSuccess) {
                    var result = response.Data;

                    if (result?.LastLoginTime.HasValue == true) {
                        var lastLogin = result.LastLoginTime.Value;
                        var now = DateTime.Now;
                        var timeDiff = now - lastLogin;

                        if (timeDiff.TotalMinutes < 1) {
                            lastLoginDisplay = "Just now";
                        }
                        else if (timeDiff.TotalHours < 1) {
                            lastLoginDisplay = $"{(int)timeDiff.TotalMinutes} minutes ago";
                        }
                        else if (timeDiff.TotalDays < 1) {
                            lastLoginDisplay = $"{(int)timeDiff.TotalHours} hours ago";
                        }
                        else if (timeDiff.TotalDays < 7) {
                            lastLoginDisplay = $"{(int)timeDiff.TotalDays} days ago";
                        }
                        else {
                            lastLoginDisplay = lastLogin.ToString("yyyy-MM-dd");
                        }
                    }
                    else {
                        lastLoginDisplay = "First login";
                    }
                }
            }
            catch (Exception ex) {
                // 如果获取失败，显示默认值
                lastLoginDisplay = "N/A";
            }
        }
        else {
            lastLoginDisplay = "N/A";
        }
    }

    private void UpdateSessionTime(object? state) {
        var elapsed = DateTime.Now - sessionStartTime;
        sessionTime = $"{elapsed.Hours:D2}:{elapsed.Minutes:D2}:{elapsed.Seconds:D2}";
        InvokeAsync(StateHasChanged);
    }

    public void Dispose() {
        timer?.Dispose();
    }

}


