@page "/finance/charts"
@using HuaLingErpApp.Shared

<SecurePage RequiredRoles="Administrator,Manager" PageName="Chart Management">
@if (isReady) {
    <Table TItem="Chart"
           IsPagination="true" PageItemsSource="[20, 25, 30]"
           IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
           ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
           AddModalTitle="Add New Chart" EditModalTitle="Edit Chart"
           SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
           OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
           OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
           DoubleClickToEdit="true"
           ShowSkeleton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.Id" Text="Chart Code" Sortable="true" Searchable="true"
                         Align="Alignment.Center">
            </TableColumn>
            <TableColumn @bind-Field="@context.Name" Text="Name" Sortable="true" Searchable="true"
                         Align="Alignment.Center">
            </TableColumn>
            <TableColumn @bind-Field="@context.AccessUnit1" Text="Access Unit 1" Sortable="true" Searchable="true"
                         Align="Alignment.Center">
            </TableColumn>
            <TableColumn @bind-Field="@context.AccessUnit2" Text="Access Unit 2" Sortable="true" Searchable="true"
                         Align="Alignment.Center">
            </TableColumn>
        </TableColumns>
        <EmptyTemplate>
            <div class="text-center py-5">
                <div class="text-muted">
                    <i class="fas fa-chart-bar fa-3x mb-3"></i>
                    <h5>No charts found</h5>
                    <p>No charts match your search criteria. Try adjusting your search or add a new chart.</p>
                </div>
            </div>
        </EmptyTemplate>
    </Table>
}
</SecurePage>

@code {
    private List<Chart> ChartItems { get; set; } = new();

    private Chart SearchModel { get; set; } = new();

    private string relativePath = "api/charts";
    private bool isReady;

    protected override async Task OnParametersSetAsync()
    {
        // Check authentication state, only load data if authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely()
    {
        try
        {
            await LoadChartsAsync();
        }
        catch (Exception ex)
        {
            // Only show error message if authenticated
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                await MessageService.Show(new MessageOption
                {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally
        {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadChartsAsync()
    {
        try
        {
            var response = await Api.GetAsync<HttpResponseModel<List<Chart>>>(relativePath);
            if (response?.IsSuccess == true && response.Data != null)
            {
                ChartItems = response.Data;
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Failed to load charts: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private Task<QueryData<Chart>> OnQueryAsync(QueryPageOptions options)
    {
        var items = ChartItems.Where(options.ToFilterFunc<Chart>());
        if (!string.IsNullOrEmpty(options.SearchText))
        {
            // Use Linq to process
            items = ChartItems.Where(i =>
                !string.IsNullOrEmpty(i.Id) && i.Id.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Name) && i.Name.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.AccessUnit1) && i.AccessUnit1.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.AccessUnit2) && i.AccessUnit2.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.CreatedBy) && i.CreatedBy.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
            );
        }

        // Use Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName))
        {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<Chart>()
        {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<Chart> OnAddAsync()
    {
        return await Task.FromResult(new Chart()
        {
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(Chart item, ItemChangedType changedType)
    {
        try
        {
            if (changedType == ItemChangedType.Add)
            {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode)
                {
                    await MessageService.Show(new MessageOption
                    {
                        Content = "Chart added successfully",
                        Color = Color.Success
                    });
                    await LoadChartsAsync();
                    return true;
                }
                else
                {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption
                    {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else
            {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode)
                {
                    await MessageService.Show(new MessageOption
                    {
                        Content = "Chart updated successfully",
                        Color = Color.Success
                    });
                    await LoadChartsAsync();
                    return true;
                }
                else
                {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption
                    {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Operation failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<Chart> items)
    {
        try
        {
            var itemList = items.ToList();
            if (!itemList.Any())
            {
                await MessageService.Show(new MessageOption
                {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1)
            {
                // Single delete
                response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
            }
            else
            {
                // Batch delete
                response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode)
            {
                await MessageService.Show(new MessageOption
                {
                    Content = "Chart(s) deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadChartsAsync();
                return true;
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Delete operation failed: {ex.Message}",
                Color = Color.Danger
            });
        }
        return false;
    }

    private Task OnResetSearchAsync(Chart searchModel)
    {
        searchModel.Id = string.Empty;
        searchModel.Name = string.Empty;
        searchModel.AccessUnit1 = string.Empty;
        searchModel.AccessUnit2 = string.Empty;
        searchModel.CreatedBy = string.Empty;
        return Task.CompletedTask;
    }

}
