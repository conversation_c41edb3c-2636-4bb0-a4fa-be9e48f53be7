@page "/finance/currencyrates"
@using HuaLingErpApp.Shared

<SecurePage RequiredRoles="Administrator,Manager" PageName="Currency Rate Management">
@if (isReady) {
    <Table TItem="CurrencyRate"
           IsPagination="true" PageItemsSource="[20, 25, 30]"
           IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
           ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
           AddModalTitle="Add New Currency Rate" EditModalTitle="Edit Currency Rate"
           SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
           OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
           OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
           DoubleClickToEdit="true"
           ShowSkeleton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.FromCurrCode" Text="From Currency" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="120">
                <EditTemplate Context="columnContext" >
                    <div class="col-12 col-sm-6 col-md-6">
                        <Select TValue="string" ShowRequired="true" @bind-Value="@columnContext.FromCurrCode" 
                                Items="CurrCodeItems"
                                ShowSearch="true"
                                IsPopover="true"
                                PlaceHolder="Select FromCurrCode..." 
                                DisplayText="FromCurrCode">
                            <Options>
                                <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                              Active="true"></SelectOption>
                            </Options>
                        </Select>
                    </div>
                </EditTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.ToCurrCode" Text="To Currency" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="120">
                <EditTemplate Context="columnContext" >
                    <div class="col-12 col-sm-6 col-md-6">
                        <Select TValue="string" ShowRequired="true" @bind-Value="@columnContext.ToCurrCode" 
                                Items="CurrCodeItems"
                                ShowSearch="true"
                                IsPopover="true"
                                PlaceHolder="Select ToCurrCode..." 
                                DisplayText="ToCurrCode">
                            <Options>
                                <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                              Active="true"></SelectOption>
                            </Options>
                        </Select>
                    </div>
                </EditTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.EffDate" Text="Effective Date" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="140" FormatString="yyyy-MM-dd">
            </TableColumn>
            <TableColumn @bind-Field="@context.Rate" Text="Exchange Rate" Sortable="true" Searchable="true"
                         Align="Alignment.Right" Width="150" FormatString="F7">
            </TableColumn>
        </TableColumns>
        <EmptyTemplate>
            <div class="text-center py-5">
                <div class="text-muted">
                    <i class="fas fa-exchange-alt fa-3x mb-3"></i>
                    <h5>No currency rates found</h5>
                    <p>No currency rates match your search criteria. Try adjusting your search or add a new currency rate.</p>
                </div>
            </div>
        </EmptyTemplate>
    </Table>
}
</SecurePage>

@code {
    private List<CurrencyRate> Items { get; set; } = new();
    private CurrencyRate SearchModel { get; set; } = new();
    public List<CurrencyCode> CurrCodes { get; set; } = new();

    private string relativePath = "api/currencyrates";
    private bool isReady = false;
    private IEnumerable<SelectedItem> CurrCodeItems => CurrCodes
        .Select(c => new SelectedItem(c.Id, $"{c.Id}"));


    protected override async Task OnParametersSetAsync()
    {
        // Check authentication state, only load data if authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely()
    {
        try
        {
            await LoadCurrencyRatesAsync();
            await LoadCurrencyCodesAsync();
        }
        catch (Exception ex)
        {
            // Only show error message if authenticated
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                await MessageService.Show(new MessageOption
                {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally
        {
            isReady = true;
            StateHasChanged();
        }
    }
    private async Task LoadCurrencyCodesAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<CurrencyCode>>>("api/currencycode");
            if (response?.IsSuccess == true && response.Data != null) {
                CurrCodes = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load Currency Codes: {ex.Message}",
                Color = Color.Danger
            });
        }
    }
    private async Task LoadCurrencyRatesAsync()
    {
        try
        {
            var response = await Api.GetAsync<HttpResponseModel<List<CurrencyRate>>>(relativePath);
            if (response?.IsSuccess == true && response.Data != null)
            {
                Items = response.Data;
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Failed to load currency rates: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private Task<QueryData<CurrencyRate>> OnQueryAsync(QueryPageOptions options)
    {
        var items = Items.Where(options.ToFilterFunc<CurrencyRate>());
        
        if (!string.IsNullOrEmpty(options.SearchText))
        {
            items = Items.Where(i =>
                (!string.IsNullOrEmpty(i.FromCurrCode) && i.FromCurrCode.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase))
                ||
                (!string.IsNullOrEmpty(i.ToCurrCode) && i.ToCurrCode.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase))
                ||
                (!string.IsNullOrEmpty(i.CreatedBy) && i.CreatedBy.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase))
            );
        }

        // Use Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName))
        {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        
        return Task.FromResult(new QueryData<CurrencyRate>()
        {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<CurrencyRate> OnAddAsync()
    {
        return await Task.FromResult(new CurrencyRate()
        {
            EffDate = DateTime.Today,
            Rate = 1.0m
        });
    }

    private async Task<bool> OnSaveAsync(CurrencyRate item, ItemChangedType changedType)
    {
        try
        {
            if (changedType == ItemChangedType.Add)
            {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode)
                {
                    await MessageService.Show(new MessageOption
                    {
                        Content = "Currency rate added successfully",
                        Color = Color.Success
                    });
                    await LoadCurrencyRatesAsync();
                    return true;
                }
                else
                {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption
                    {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else
            {
                var url = $"{relativePath}/{item.FromCurrCode}/{item.ToCurrCode}/{item.EffDate:yyyy-MM-dd}";
                var response = await Api.PutAsync(url, item);
                if (response.IsSuccessStatusCode)
                {
                    await MessageService.Show(new MessageOption
                    {
                        Content = "Currency rate updated successfully",
                        Color = Color.Success
                    });
                    await LoadCurrencyRatesAsync();
                    return true;
                }
                else
                {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption
                    {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Operation failed: {ex.Message}",
                Color = Color.Danger
            });
        }
        return false;
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<CurrencyRate> items)
    {
        try
        {
            var itemList = items.ToList();
            if (!itemList.Any())
            {
                await MessageService.Show(new MessageOption
                {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            HttpResponseMessage response;

            if (itemList.Count == 1)
            {
                // Single delete
                var item = itemList[0];
                var url = $"{relativePath}/{item.FromCurrCode}/{item.ToCurrCode}/{item.EffDate:yyyy-MM-dd}";
                response = await Api.DeleteAsync(url);
            }
            else
            {
                // Batch delete
                response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", itemList);
            }

            if (response.IsSuccessStatusCode)
            {
                await MessageService.Show(new MessageOption
                {
                    Content = "Currency rate(s) deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadCurrencyRatesAsync();
                return true;
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Delete operation failed: {ex.Message}",
                Color = Color.Danger
            });
        }
        return false;
    }

    private Task OnResetSearchAsync(CurrencyRate searchModel)
    {
        searchModel.FromCurrCode = string.Empty;
        searchModel.ToCurrCode = string.Empty;
        searchModel.EffDate = null;
        searchModel.Rate = null;
        searchModel.CreatedBy = string.Empty;
        return Task.CompletedTask;
    }
}
