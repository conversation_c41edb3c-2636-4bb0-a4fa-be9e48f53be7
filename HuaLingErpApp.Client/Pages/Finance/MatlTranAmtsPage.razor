@page "/finance/matltranamt"
@using Console = System.Console
@using System.Text
@inject ITableExport TableExport 
@inject ToastService Toast
@inject ClipboardService ClipboardService 
<SecurePage RequiredRoles="Administrator,Manager,User" PageName="Material Transaction Amounts">
@if (isReady) {
    
    <!-- Data Table -->
    <Table TItem="MatlTranAmt"
           IsPagination="true" PageItemsSource="[20, 25, 30, 50]"
           IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
           ShowToolbar="true" ShowSearch="true" ShowExtendButtons="false"
           SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
           OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
           ShowSkeleton="true"
           ShowAddButton="false" ShowEditButton="false" ShowDeleteButton="false"
           IsMultipleSelect="false" ShowExportButton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.TransNum" Text="Trans Number" Sortable="true" Searchable="true"
                         Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.TransSeq" Text="Trans Seq" Sortable="true" Searchable="true"
                         Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.Amt" Text="Amount" Sortable="true" 
                         Align="Alignment.Center" />
            <TableColumn @bind-Field="@context.Acct" Text="Account" Sortable="true" Searchable="true"
                         Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.AcctUnit1" Text="Account Unit 1" Sortable="true" Searchable="true"
                         Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.AcctUnit2" Text="Account Unit 2" Sortable="true" Searchable="true"
                         Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.IncludeInInventoryBalCalc" Text="Include in Inventory Calc" Sortable="true"
                         Align="Alignment.Center">
                <Template Context="value">
                    @switch (value.Value) {
                        case true:
                            <i class="fas fa-check text-success"></i>
                            break;
                        case false:
                            <i class="fas fa-times text-danger"></i>
                            break;
                        default:
                            <span class="text-muted">-</span>
                            break;
                    }
                </Template>
            </TableColumn>
        </TableColumns>
        <ExportButtonDropdownTemplate Context="ExportContext">
            <div class="dropdown-item" @onclick="() => ExcelExportAsync(ExportContext)">
                <i class="fa-regular fa-file-excel"></i>
                <span>Export Current Page Data to Excel</span>
            </div>
            <div class="dropdown-item" @onclick="() => ExcelExportAllAsync(ExportContext)">
                <i class="fa-regular fa-file-excel"></i>
                <span>Export All Data to Excel</span>
            </div>
            <div class="dropdown-item" @onclick="() => CsvExportAsync(ExportContext)">
                <i class="fa-regular fa-file-excel"></i>
                <span>MS-CSV</span>
            </div>
            <div class="dropdown-item" @onclick="() => ClipBoardExportAsync(ExportContext)">
                <i class="fa-regular fa-file-excel"></i>
                <span>Export Current Page to Clipboard</span>
            </div>
            <div class="dropdown-item" @onclick="() => ClipBoardExportAllAsync(ExportContext)">
                <i class="fa-regular fa-file-excel"></i>
                <span>Export All Pages to Clipboard</span>
            </div>
        </ExportButtonDropdownTemplate>
        <EmptyTemplate>
            <div class="text-center py-5">
                <div class="text-muted">
                    <i class="fas fa-receipt fa-3x mb-3"></i>
                    <h5>No transaction amounts found</h5>
                    <p>No material transaction amounts match your search criteria. Try adjusting your search filters.</p>
                </div>
            </div>
        </EmptyTemplate>
    </Table>
}
</SecurePage>

@code {
    private List<MatlTranAmt> Items { get; set; } = new();
    private MatlTranAmt SearchModel { get; set; } = new();

    private string relativePath = "api/matltranamt";
    private bool isReady = false;

    protected override async Task OnInitializedAsync() {
        // Check authentication state, only load data if authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely() {
        try {
            await LoadData();
        }
        catch (Exception ex) {
            // Only show an error message if authenticated
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadData() {
        var response = await Api.GetAsync<HttpResponseModel<List<MatlTranAmt>>>(relativePath);
        if (response?.IsSuccess == true && response.Data != null) {
            Items = response.Data;
        }
        else {
            await MessageService.Show(new MessageOption {
                Content = response?.Message ?? "Failed to load material transaction amounts",
                Color = Color.Danger
            });
        }
    }
    
    private Task<QueryData<MatlTranAmt>> OnQueryAsync(QueryPageOptions options) {
        IEnumerable<MatlTranAmt> items = Items;
        if (!string.IsNullOrEmpty(options.SearchText))
        {
            items = items.Where(i => 
                (i.TransNum.ToString()?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                || 
                (i.TransSeq.ToString()?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                || 
                (i.Acct?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false));
        }
        // Apply search
        if (SearchModel.TransNum > 0) {
            items = items.Where(i => i.TransNum == SearchModel.TransNum);
        }
        if (SearchModel.TransSeq > 0) {
            items = items.Where(i => i.TransSeq == SearchModel.TransSeq);
        }
        if (!string.IsNullOrEmpty(SearchModel.Acct)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.Acct) && i.Acct.Contains(SearchModel.Acct, StringComparison.OrdinalIgnoreCase));
        }
        if (!string.IsNullOrEmpty(SearchModel.AcctUnit1)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.AcctUnit1) && i.AcctUnit1.Contains(SearchModel.AcctUnit1, StringComparison.OrdinalIgnoreCase));
        }
        if (!string.IsNullOrEmpty(SearchModel.AcctUnit2)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.AcctUnit2) && i.AcctUnit2.Contains(SearchModel.AcctUnit2, StringComparison.OrdinalIgnoreCase));
        }

        // Apply sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set the total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<MatlTranAmt>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private Task OnResetSearchAsync(MatlTranAmt searchModel) {
        searchModel.TransNum = null;
        searchModel.TransSeq = null;
        searchModel.Acct = "";
        searchModel.AcctUnit1 = "";
        searchModel.AcctUnit2 = "";
        return Task.CompletedTask;
    }
                private async Task ExcelExportAsync(ITableExportContext<MatlTranAmt> context)
        {
            // 自定义导出模板导出当前页面数据为 Excel 方法
            // 使用 BootstrapBlazor 内置服务 ITableExcelExport 实例方法 ExportAsync 进行导出操作
            // 导出数据使用 context 传递来的 Rows/Columns 即为当前页数据
            var ret = await TableExport.ExportExcelAsync(context.Rows, context.Columns, $"Test_{DateTime.Now:yyyyMMddHHmmss}.xlsx");

            // 返回 true 时自动弹出提示框
            await ShowToast(ret);
        }

      
        private async Task ExcelExportAllAsync(ITableExportContext<MatlTranAmt> context)
        {
            // 自定义导出模板导出当前页面数据为 Excel 方法
            // 使用 BootstrapBlazor 内置服务 ITableExport 实例方法 ExportExcelAsync 进行导出操作
            // 通过 context 参数的查询条件
            var option = context.BuildQueryPageOptions();

            // 通过内置扩展方法 GetFilterFunc 过滤数据
            // EFCore 可使用 GetFilterLambda 获得表达式直接给 Where 方法使用
            var data = Items.Where(option.ToFilterFunc<MatlTranAmt>());

            // 导出符合条件的所有数据 data
            var ret = await TableExport.ExportExcelAsync(data, context.Columns, $"Test_{DateTime.Now:yyyyMMddHHmmss}.xlsx");

            // 返回 true 时自动弹出提示框
            await ShowToast(ret);
        }

        private async Task CsvExportAsync(ITableExportContext<MatlTranAmt> context)
        {
            // 自定义导出模板导出当前页面数据为 Csv 方法
            // 使用 BootstrapBlazor 内置服务 ITableExcelExport 实例方法 ExportCsvAsync 进行导出操作
            // 导出数据使用 context 传递来的 Rows/Columns 即为当前页数据
            var ret = await TableExport.ExportCsvAsync(context.Rows, context.Columns, $"Test_{DateTime.Now:yyyyMMddHHmmss}.csv");

            // 返回 true 时自动弹出提示框
            await ShowToast(ret);
        }
        private async Task ClipBoardExportAllAsync(ITableExportContext<MatlTranAmt> context)
        {
            // 自定义导出当前页面数据到剪切板,可以直接粘贴到 Excel 中
            // 使用 BootstrapBlazor 内置服务 ClipboardService 实例方法 Copy 进行导出操作
            // 通过 context 参数的查询条件
            var option = context.BuildQueryPageOptions();

            // 通过内置扩展方法 GetFilterFunc 过滤数据
            // EFCore 可使用 GetFilterLambda 获得表达式直接给 Where 方法使用
            var data = Items.Where(option.ToFilterFunc<MatlTranAmt>());

            // 导出符合条件的所有数据 data
            await ExportToClipBoard(context.Columns, data);

            // 返回 true 时自动弹出提示框
            await ShowToast(true);
        }

        private async Task ExportToClipBoard(IEnumerable<ITableColumn> columns, IEnumerable<MatlTranAmt> rows)
        {
            var sb = new StringBuilder();

            // 导出表格 Header
            var titles = columns.Select(x => x.GetDisplayName());
            sb.AppendJoin('\t', titles).AppendLine();

            // 导出表格 Row
            var fieldNames = columns.Select(x => x.GetFieldName());
            foreach (var row in rows)
            {
                var values = fieldNames.Select(x => row.GetType().GetProperty(x)?.GetValue(row)).ToArray();
                sb.AppendJoin('\t', values).AppendLine();
            }

            var result = sb.ToString();
            await ClipboardService.Copy(result);
        }
        private async Task ClipBoardExportAsync(ITableExportContext<MatlTranAmt> context)
        {
            // 自定义导出当前页面数据到剪切板,可以直接粘贴到 Excel 中
            // 使用 BootstrapBlazor 内置服务 ClipboardService 实例方法 Copy 进行导出操作
            // 导出数据使用 context 传递来的 Rows/Columns 即为当前页数据
            await ExportToClipBoard(context.Columns, context.Rows);

            // 返回 true 时自动弹出提示框
            await ShowToast(true);
        }
        private async Task ShowToast(bool result)
        {
            if (result)
            {
                await Toast.Success("Data Export", "Data exported successfully, closing in 4 seconds");
            }
            else
            {
                await Toast.Error("Data Export", "Failed to export data, closing in 4 seconds");
            }
        }
}
