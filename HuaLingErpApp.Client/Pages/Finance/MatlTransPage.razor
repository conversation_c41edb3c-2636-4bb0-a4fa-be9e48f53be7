@page "/finance/matltrans"
@using System.Text
@inject ITableExport TableExport
@inject ToastService Toast
@inject ClipboardService ClipboardService
<SecurePage RequiredRoles="Administrator,Manager,User" PageName="Material Transactions">
    @if (_isReady) {
        <!-- Data Table -->
        <Table @ref="_tableRef" TItem="MatlTran"
               IsPagination="true" PageItemsSource="[20, 25, 30, 50]"
               IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
               ShowToolbar="true" ShowSearch="true" ShowExtendButtons="false"
               SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
               OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
               ShowSkeleton="true"
               ShowAddButton="false" ShowEditButton="false" ShowDeleteButton="false"
               IsMultipleSelect="false" ShowExportButton="true">
            <ToolbarTemplate>
                <div class="d-flex align-items-center gap-3 flex-wrap">
                    <div class="d-flex align-items-center">
                        <span class="me-2">Date Range:</span>
                    </div>
                    <div style="min-width: 300px;">
                        <DateTimeRange @bind-Value="@DateRangeValue"
                                       OnConfirm="@OnDateRangeConfirm"
                                       OnClearValue="@OnDateRangeClear"
                                       ShowSidebar="true"
                                       ShowSelectedValue="true"
                                       DateFormat="yyyy-MM-dd"
                                       ShowToday="true"/>
                    </div>


                    @if (DateRangeValue.Start != DateTime.MinValue || DateRangeValue.End != DateTime.MinValue) {
                        <div class="ms-auto">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Filtered: @(DateRangeValue.Start != DateTime.MinValue ? DateRangeValue.Start.ToString("yyyy-MM-dd") : "beginning")
                                to
                                @(DateRangeValue.End != DateTime.MinValue ? DateRangeValue.End.ToString("yyyy-MM-dd") : "end")
                            </small>
                        </div>
                    }
                </div>
            </ToolbarTemplate>
            <TableColumns>
                <TableColumn @bind-Field="@context.TransNum" Text="Trans #" Sortable="true" Searchable="true"
                             Align="Alignment.Center" Width="100"/>
                <TableColumn @bind-Field="@context.Item" Text="Item" Sortable="true" Searchable="true"
                             Align="Alignment.Left" Width="150"/>
                <TableColumn @bind-Field="@context.Qty" Text="Quantity" Sortable="true"
                             Align="Alignment.Right" Width="100">
                    <Template Context="value">
                        @if (value.Value.HasValue) {
                            @value.Value.Value.ToString("N2")
                        }
                        else {
                            <span class="text-muted">-</span>
                        }
                    </Template>
                </TableColumn>
                <TableColumn @bind-Field="@context.TransDate" Text="Trans Date" Sortable="true"
                             Align="Alignment.Center" Width="140" FormatString="yyyy-MM-dd">
                    <Template Context="value">
                        @if (value.Value.HasValue) {
                            @value.Value.Value.ToString("yyyy-MM-dd")
                        }
                        else {
                            <span class="text-muted">-</span>
                        }
                    </Template>
                </TableColumn>
                <TableColumn @bind-Field="@context.RefNum" Text="Ref #" Sortable="true" Searchable="true"
                             Align="Alignment.Center" Width="100"/>
                <TableColumn @bind-Field="@context.RefLine" Text="Ref Line" Sortable="true"
                             Align="Alignment.Center" Width="80"/>
                <TableColumn @bind-Field="@context.TransType" Text="Trans Type" Sortable="true" Searchable="true"
                             Align="Alignment.Center" Width="80"/>
                <TableColumn @bind-Field="@context.RefType" Text="Ref Type" Sortable="true" Searchable="true"
                             Align="Alignment.Center" Width="80"/>
                <TableColumn @bind-Field="@context.Cost" Text="Cost" Sortable="true"
                             Align="Alignment.Right" Width="100">
                    <Template Context="value">
                        @if (value.Value.HasValue) {
                            @value.Value.Value.ToString("C2")
                        }
                        else {
                            <span class="text-muted">-</span>
                        }
                    </Template>
                </TableColumn>
                <TableColumn @bind-Field="@context.Loc" Text="Location" Sortable="true" Searchable="true"
                             Align="Alignment.Center" Width="100"/>
                <TableColumn @bind-Field="@context.Lot" Text="Lot" Sortable="true" Searchable="true"
                             Align="Alignment.Center" Width="120"/>
                <TableColumn @bind-Field="@context.ReasonCode" Text="Reason" Sortable="true" Searchable="true"
                             Align="Alignment.Center" Width="80"/>
                <TableColumn @bind-Field="@context.UM" Text="U/M" Sortable="true" Searchable="true"
                             Align="Alignment.Center" Width="60"/>
            </TableColumns>
            <ExportButtonDropdownTemplate Context="ExportContext">
                <div class="dropdown-item" @onclick="() => ExcelExportAsync(ExportContext)">
                    <i class="fa-regular fa-file-excel"></i>
                    <span>Export Current Page Data to Excel</span>
                </div>
                <div class="dropdown-item" @onclick="() => ExcelExportAllAsync(ExportContext)">
                    <i class="fa-regular fa-file-excel"></i>
                    <span>Export All Data to Excel</span>
                </div>
                <div class="dropdown-item" @onclick="() => CsvExportAsync(ExportContext)">
                    <i class="fa-regular fa-file-excel"></i>
                    <span>MS-CSV</span>
                </div>
                <div class="dropdown-item" @onclick="() => ClipBoardExportAsync(ExportContext)">
                    <i class="fa-regular fa-file-excel"></i>
                    <span>Export Current Page to Clipboard</span>
                </div>
                <div class="dropdown-item" @onclick="() => ClipBoardExportAllAsync(ExportContext)">
                    <i class="fa-regular fa-file-excel"></i>
                    <span>Export All Pages to Clipboard</span>
                </div>
            </ExportButtonDropdownTemplate>
            <EmptyTemplate>
                <div class="text-center py-5">
                    @if (_isLoading) {
                        <div class="text-muted">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <h5>Loading transactions...</h5>
                            <p>Please wait while we fetch the data.</p>
                        </div>
                    }
                    else if (DateRangeValue.Start == DateTime.MinValue && DateRangeValue.End == DateTime.MinValue) {
                        <div class="text-muted">
                            <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                            <h5>Please select a date range</h5>
                            <p>Choose a date range above to view material transactions.</p>
                        </div>
                    }
                    else {
                        <div class="text-muted">
                            <i class="fas fa-search fa-3x mb-3"></i>
                            <h5>No transactions found</h5>
                            <p>No material transactions found for the selected date range.</p>
                        </div>
                    }
                </div>
            </EmptyTemplate>
        </Table>
    }
</SecurePage>

@code {
    private List<MatlTran> Items { get; set; } = new();
    private MatlTran SearchModel { get; set; } = new();
    private DateTimeRangeValue DateRangeValue { get; set; } = new(); // Empty by default
    private Table<MatlTran>? _tableRef;
    private bool _isLoading; // 添加加载状态

    private const string RelativePath = "api/matltrans";
    private bool _isReady;

    protected override async Task OnInitializedAsync() {
        // Check authentication state, but don't load data initially
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            // Just set ready state, don't load data
            _isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadData() {
        // Only load data if the date range is selected
        if (DateRangeValue.Start == DateTime.MinValue && DateRangeValue.End == DateTime.MinValue) {
            Items = new List<MatlTran>(); // Clear data when no date range selected
            _isLoading = false;
            return;
        }

        try {
            _isLoading = true;
            StateHasChanged(); // 立即更新UI显示加载状态

            var url = RelativePath;

            // Add date range parameters if set
            var queryParams = new List<string>();
            if (DateRangeValue.Start != DateTime.MinValue) {
                queryParams.Add($"startDate={DateRangeValue.Start:yyyy-MM-dd}");
            }

            if (DateRangeValue.End != DateTime.MinValue) {
                queryParams.Add($"endDate={DateRangeValue.End:yyyy-MM-dd}");
            }

            if (queryParams.Any()) {
                url += "?" + string.Join("&", queryParams);
            }

            var response = await Api.GetAsync<HttpResponseModel<List<MatlTran>>>(url);
            if (response?.IsSuccess == true && response.Data != null) {
                Items = response.Data;
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = response?.Message ?? "Failed to load material transactions",
                    Color = Color.Danger
                });
            }
        }
        finally {
            _isLoading = false;
        }
    }


    private async Task OnDateRangeConfirm(DateTimeRangeValue value) {
        DateRangeValue = value;
        await LoadData();
        // 强制表格刷新
        if (_tableRef != null) {
            await _tableRef.QueryAsync();
        }

        StateHasChanged();
    }

    private async Task OnDateRangeClear(DateTimeRangeValue value) {
        DateRangeValue = new DateTimeRangeValue(); // Reset to empty
        Items = new List<MatlTran>(); // Clear data when date range is cleared
        _isLoading = false; // 确保清除时不显示加载状态
        // 强制表格刷新
        if (_tableRef != null) {
            await _tableRef.QueryAsync();
        }

        StateHasChanged();
    }


    private Task<QueryData<MatlTran>> OnQueryAsync(QueryPageOptions options) {
        IEnumerable<MatlTran> items = Items;

        // Apply global search
        if (!string.IsNullOrEmpty(options.SearchText)) {
            items = items.Where(i =>
                (i.TransNum.ToString()?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                ||
                (i.Item?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                ||
                (i.RefNum?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                ||
                (i.TransType?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                ||
                (i.RefType?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                ||
                (i.Loc?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                ||
                (i.Lot?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                ||
                (i.ReasonCode?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false)
                ||
                (i.UM?.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase) ?? false));
        }

        // Apply advanced search filters
        if (SearchModel.TransNum > 0) {
            items = items.Where(i => i.TransNum == SearchModel.TransNum);
        }

        if (!string.IsNullOrEmpty(SearchModel.Item)) {
            items = items.Where(i => i.Item?.Contains(SearchModel.Item, StringComparison.OrdinalIgnoreCase) ?? false);
        }

        if (!string.IsNullOrEmpty(SearchModel.RefNum)) {
            items = items.Where(i => i.RefNum?.Contains(SearchModel.RefNum, StringComparison.OrdinalIgnoreCase) ?? false);
        }

        if (!string.IsNullOrEmpty(SearchModel.TransType)) {
            items = items.Where(i => i.TransType?.Contains(SearchModel.TransType, StringComparison.OrdinalIgnoreCase) ?? false);
        }

        if (!string.IsNullOrEmpty(SearchModel.Loc)) {
            items = items.Where(i => i.Loc?.Contains(SearchModel.Loc, StringComparison.OrdinalIgnoreCase) ?? false);
        }

        // Apply sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set the total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);

        return Task.FromResult(new QueryData<MatlTran> {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private Task OnResetSearchAsync(MatlTran searchModel) {
        searchModel.TransNum = null;
        searchModel.RefType = "";
        searchModel.Item = "";
        searchModel.RefNum = "";
        searchModel.TransType = "";
        searchModel.RefType = "";
        searchModel.Loc = "";
        searchModel.Lot = "";
        searchModel.ReasonCode = "";
        searchModel.UM = "";
        return Task.CompletedTask;
    }

    private async Task ExcelExportAsync(ITableExportContext<MatlTran> context) {
        // 自定义导出模板导出当前页面数据为 Excel 方法
        // 使用 BootstrapBlazor 内置服务 ITableExcelExport 实例方法 ExportAsync 进行导出操作
        // 导出数据使用 context 传递来的 Rows/Columns 即为当前页数据
        var ret = await TableExport.ExportExcelAsync(context.Rows, context.Columns, $"Test_{DateTime.Now:yyyyMMddHHmmss}.xlsx");

        // 返回 true 时自动弹出提示框
        await ShowToast(ret);
    }

    private async Task ExcelExportAllAsync(ITableExportContext<MatlTran> context) {
        // 自定义导出模板导出当前页面数据为 Excel 方法
        // 使用 BootstrapBlazor 内置服务 ITableExport 实例方法 ExportExcelAsync 进行导出操作
        // 通过 context 参数的查询条件
        var option = context.BuildQueryPageOptions();

        // 通过内置扩展方法 GetFilterFunc 过滤数据
        // EFCore 可使用 GetFilterLambda 获得表达式直接给 Where 方法使用
        var data = Items.Where(option.ToFilterFunc<MatlTran>());

        // 导出符合条件的所有数据 data
        var ret = await TableExport.ExportExcelAsync(data, context.Columns, $"Test_{DateTime.Now:yyyyMMddHHmmss}.xlsx");

        // 返回 true 时自动弹出提示框
        await ShowToast(ret);
    }


    private async Task CsvExportAsync(ITableExportContext<MatlTran> context) {
        // 自定义导出模板导出当前页面数据为 Csv 方法
        // 使用 BootstrapBlazor 内置服务 ITableExcelExport 实例方法 ExportCsvAsync 进行导出操作
        // 导出数据使用 context 传递来的 Rows/Columns 即为当前页数据
        var ret = await TableExport.ExportCsvAsync(context.Rows, context.Columns, $"Test_{DateTime.Now:yyyyMMddHHmmss}.csv");

        // 返回 true 时自动弹出提示框
        await ShowToast(ret);
    }

    private async Task ClipBoardExportAllAsync(ITableExportContext<MatlTran> context) {
        // 自定义导出当前页面数据到剪切板,可以直接粘贴到 Excel 中
        // 使用 BootstrapBlazor 内置服务 ClipboardService 实例方法 Copy 进行导出操作
        // 通过 context 参数的查询条件
        var option = context.BuildQueryPageOptions();

        // 通过内置扩展方法 GetFilterFunc 过滤数据
        // EFCore 可使用 GetFilterLambda 获得表达式直接给 Where 方法使用
        var data = Items.Where(option.ToFilterFunc<MatlTran>());

        // 导出符合条件的所有数据 data
        await ExportToClipBoard(context.Columns, data);

        // 返回 true 时自动弹出提示框
        await ShowToast(true);
    }

    private async Task ExportToClipBoard(IEnumerable<ITableColumn> columns, IEnumerable<MatlTran> rows) {
        var sb = new StringBuilder();

        // 导出表格 Header
        var titles = columns.Select(x => x.GetDisplayName());
        sb.AppendJoin('\t', titles).AppendLine();

        // 导出表格 Row
        var fieldNames = columns.Select(x => x.GetFieldName());
        foreach (var row in rows) {
            var values = fieldNames.Select(x => row.GetType().GetProperty(x)?.GetValue(row)).ToArray();
            sb.AppendJoin('\t', values).AppendLine();
        }

        var result = sb.ToString();
        await ClipboardService.Copy(result);
    }

    private async Task ClipBoardExportAsync(ITableExportContext<MatlTran> context) {
        // 自定义导出当前页面数据到剪切板,可以直接粘贴到 Excel 中
        // 使用 BootstrapBlazor 内置服务 ClipboardService 实例方法 Copy 进行导出操作
        // 导出数据使用 context 传递来的 Rows/Columns 即为当前页数据
        await ExportToClipBoard(context.Columns, context.Rows);

        // 返回 true 时自动弹出提示框
        await ShowToast(true);
    }

    private async Task ShowToast(bool result) {
        if (result) {
            await Toast.Success("Data Export", "Data exported successfully, closing in 4 seconds");
        }
        else {
            await Toast.Error("Data Export", "Failed to export data, closing in 4 seconds");
        }
    }

}
