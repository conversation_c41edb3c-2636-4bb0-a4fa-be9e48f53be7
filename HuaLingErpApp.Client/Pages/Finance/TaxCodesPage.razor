@page "/finance/taxcodes"
@using System.Globalization

<SecurePage RequiredRoles="Administrator,Manager" PageName="Tax Code Management">
    @if (_isReady) {
        <Table TItem="TaxCode"
               IsPagination="true" PageItemsSource="[20, 25, 30]"
               IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
               ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
               AddModalTitle="Add New Tax Code" EditModalTitle="Edit Tax Code"
               SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
               OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
               DoubleClickToEdit="true"
               ShowSkeleton="true">
            <TableColumns>
                <TableColumn @bind-Field="@context.Id" Text="Tax Code" Sortable="true" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.TaxRate" Text="Tax Rate" Sortable="true" Searchable="true"
                             Align="Alignment.Center" FormatString="F4">
                </TableColumn>
            </TableColumns>
            <EmptyTemplate>
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-percentage fa-3x mb-3"></i>
                        <h5>No tax codes found</h5>
                        <p>No tax codes match your search criteria. Try adjusting your search or add a new tax code.</p>
                    </div>
                </div>
            </EmptyTemplate>
        </Table>
    }
</SecurePage>

@code {
    private List<TaxCode> TaxCodeItems { get; set; } = new();

    private TaxCode SearchModel { get; set; } = new();

    private const string RelativePath = "api/taxcode";
    private bool _isReady;

    protected override async Task OnParametersSetAsync() {
        // Check authentication state, only load data if authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely() {
        try {
            await LoadTaxCodesAsync();
        }
        catch (Exception ex) {
            // Only show error message if authenticated
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            _isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadTaxCodesAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<TaxCode>>>(RelativePath);
            if (response is { IsSuccess: true, Data: not null }) {
                TaxCodeItems = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load tax codes: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private Task<QueryData<TaxCode>> OnQueryAsync(QueryPageOptions options) {
        var items = TaxCodeItems.Where(options.ToFilterFunc<TaxCode>());
        if (!string.IsNullOrEmpty(options.SearchText)) {
            // Use Linq to process
            items = TaxCodeItems.Where(i =>
                !string.IsNullOrEmpty(i.Id) && i.Id.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.CreatedBy) && i.CreatedBy.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                i.TaxRate.HasValue && i.TaxRate.Value.ToString(CultureInfo.InvariantCulture).Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
            );
        }

        // Use the Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set the total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<TaxCode>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<TaxCode> OnAddAsync() {
        return await Task.FromResult(new TaxCode() {
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now,
            TaxRate = 0.0m
        });
    }

    private async Task<bool> OnSaveAsync(TaxCode item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(RelativePath, item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Tax code added successfully",
                        Color = Color.Success
                    });
                    await LoadTaxCodesAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else {
                var response = await Api.PutAsync($"{RelativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Tax code updated successfully",
                        Color = Color.Success
                    });
                    await LoadTaxCodesAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Operation failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<TaxCode> items) {
        try {
            var itemList = items.ToList();
            if (!itemList.Any()) {
                await MessageService.Show(new MessageOption {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1) {
                // Single delete
                response = await Api.DeleteAsync($"{RelativePath}/{ids[0]}");
            }
            else {
                // Batch delete
                response = await Api.PostAsync($"{RelativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = "Tax code(s) deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadTaxCodesAsync();
                return true;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Delete operation failed: {ex.Message}",
                Color = Color.Danger
            });
        }

        return false;
    }

    private Task OnResetSearchAsync(TaxCode searchModel) {
        searchModel.Id = string.Empty;
        searchModel.TaxRate = null;
        searchModel.CreatedBy = string.Empty;
        return Task.CompletedTask;
    }

}
