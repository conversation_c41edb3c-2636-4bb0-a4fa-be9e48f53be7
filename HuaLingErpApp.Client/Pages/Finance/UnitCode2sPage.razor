@page "/finance/unitcode2s"
@using System.Text.Json
@using Console = System.Console
@using HuaLingErpApp.Shared

<SecurePage RequiredRoles="Administrator,Manager" PageName="Unit Code 2 Management">
@if (isReady) {
    <Table TItem="UnitCode2"
           IsPagination="true" PageItemsSource="[20, 25, 30]"
           IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
           ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
           AddModalTitle="Add New Unit Code 2" EditModalTitle="Edit Unit Code 2"
           SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
           OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
           OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
           DoubleClickToEdit="true"
           ShowSkeleton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.Id" Text="Unit Code 2" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="150"/>
            <TableColumn @bind-Field="@context.Name" Text="Name" Sortable="true" Searchable="true"
                         Align="Alignment.Center"/>
        </TableColumns>
        <EmptyTemplate>
            <div class="text-center py-5">
                <div class="text-muted">
                    <i class="fas fa-code fa-3x mb-3"></i>
                    <h5>No unit codes found</h5>
                    <p>No unit code 2 records match your search criteria. Try adjusting your search or add a new unit code.</p>
                </div>
            </div>
        </EmptyTemplate>
    </Table>
}
</SecurePage>

@code {
    private List<UnitCode2> Items { get; set; } = new();
    private UnitCode2 SearchModel { get; set; } = new();

    private string relativePath = "api/unitcode2";
    private bool isReady = false;

    protected override async Task OnInitializedAsync() {
        // Check authentication state, only load data if authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }

    protected override void OnAfterRender(bool firstRender) {
    }

    private async Task LoadDataSafely() {
        try {
            await LoadData();
        }
        catch (Exception ex) {
            // Only show error message if authenticated
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadData() {
        var response = await Api.GetAsync<HttpResponseModel<List<UnitCode2>>>(relativePath);
        if (response?.IsSuccess == true && response.Data != null) {
            Items = response.Data;
        }
        else {
            await MessageService.Show(new MessageOption {
                Content = response?.Message ?? "Failed to load unit codes 2",
                Color = Color.Danger
            });
        }
    }

    private Task<QueryData<UnitCode2>> OnQueryAsync(QueryPageOptions options) {
        IEnumerable<UnitCode2> items = Items;

        // Apply search
        if (!string.IsNullOrEmpty(SearchModel.Id)) {
            items = items.Where(i => i.Id.Contains(SearchModel.Id, StringComparison.OrdinalIgnoreCase));
        }
        if (!string.IsNullOrEmpty(SearchModel.Name)) {
            items = items.Where(i => !string.IsNullOrEmpty(i.Name) && i.Name.Contains(SearchModel.Name, StringComparison.OrdinalIgnoreCase));
        }

        // Apply sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<UnitCode2>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<UnitCode2> OnAddAsync() {
        return await Task.FromResult(new UnitCode2() {
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(UnitCode2 item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode) {
                    await LoadData();
                    await MessageService.Show(new MessageOption {
                        Content = "Unit code 2 created successfully",
                        Color = Color.Success
                    });
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await LoadData();
                    await MessageService.Show(new MessageOption {
                        Content = "Unit code 2 updated successfully",
                        Color = Color.Success
                    });
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Error: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<UnitCode2> items) {
        try {
            var itemList = items.ToList();
            if (!itemList.Any()) {
                await MessageService.Show(new MessageOption {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1) {
                // Single delete
                response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
            }
            else {
                // Batch delete
                response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = "Deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadData();
                return true;
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                throw new Exception(error);
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Delete failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private Task OnResetSearchAsync(UnitCode2 searchModel) {
        searchModel.Id = "";
        searchModel.Name = "";
        return Task.CompletedTask;
    }
}
