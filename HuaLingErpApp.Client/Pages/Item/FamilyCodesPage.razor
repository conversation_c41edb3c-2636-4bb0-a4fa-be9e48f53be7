@page "/item/familycodes"

<SecurePage RequiredRoles="Administrator,Manager" PageName="FamilyCode Management">
    @if (_isReady) {
        <Table TItem="FamilyCode"
               IsPagination="true" PageItemsSource="[20, 25, 30 ]"
               IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
               ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
               AddModalTitle="Add New" EditModalTitle="Edit"
               SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
               OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
               DoubleClickToEdit="true"
               ShowSkeleton="true">
            <TableColumns>

                <TableColumn @bind-Field="@context.Id" Text="U/M" Sortable="true" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.Description" Text="Description" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
            </TableColumns>
            <EmptyTemplate>
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-sitemap fa-3x mb-3"></i>
                        <h5>No family codes found</h5>
                        <p>No family codes match your search criteria. Try adjusting your search or add a new family
                            code.</p>
                    </div>
                </div>
            </EmptyTemplate>
        </Table>
    }
</SecurePage>

@code {
    private List<FamilyCode> FamilyCodeItems { get; set; } = new();

    private FamilyCode SearchModel { get; set; } = new();

    private const string RelativePath = "api/familycode";
    private bool _isReady;

    protected override async Task OnParametersSetAsync() {
        // 检查认证状态，只有已认证才加载数据
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely() {
        try {
            await LoadFamilyCodesAsync();
        }
        catch (Exception ex) {
            // 只有在认证状态下才显示错误消息
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            _isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadFamilyCodesAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<FamilyCode>>>(RelativePath);
            if (response is { IsSuccess: true, Data: not null }) {
                FamilyCodeItems = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load FamilyCodes: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private Task<QueryData<FamilyCode>> OnQueryAsync(QueryPageOptions options) {
        var items = FamilyCodeItems.Where(options.ToFilterFunc<FamilyCode>());
        if (!string.IsNullOrEmpty(options.SearchText)) {
            // Use Linq to process
            items = FamilyCodeItems.Where(i =>
                !string.IsNullOrEmpty(i.Id) && i.Id.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Description) && i.Description.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
            );
        }

        // Use the Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set the total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<FamilyCode>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<FamilyCode> OnAddAsync() {
        return await Task.FromResult(new FamilyCode() {
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(FamilyCode item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(RelativePath, item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Added successfully",
                        Color = Color.Success
                    });
                    await LoadFamilyCodesAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else {
                var response = await Api.PutAsync($"{RelativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Updated successfully",
                        Color = Color.Success
                    });
                    await LoadFamilyCodesAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Save failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<FamilyCode> items) {
        try {
            var itemList = items.ToList();
            if (!itemList.Any()) {
                await MessageService.Show(new MessageOption {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1) {
                // Single delete
                response = await Api.DeleteAsync($"{RelativePath}/{ids[0]}");
            }
            else {
                // Batch delete
                response = await Api.DeleteAsJsonAsync($"{RelativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = "Deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadFamilyCodesAsync();
                return true;
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                throw new Exception(error);
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Delete failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private Task OnResetSearchAsync(FamilyCode model) {
        model.Id = "";
        model.Description = "";
        return Task.CompletedTask;
    }

}