@page "/item/items"
@using HuaLingErpApp.Client.Components
@using Microsoft.AspNetCore.Components.Authorization
@using System.Text.Json
@using System.Text
@using HuaLingErpApp.Shared.Models
@using HuaLingErpApp.Shared
@using System.IO
@inject MessageService MessageService
@inject DialogService DialogService
@inject ApiService Api
@inject NavigationManager NavigationManager
@inject AuthenticationStateProvider AuthStateProvider
@inject ITableExport TableExport
@inject ToastService Toast
@inject ClipboardService ClipboardService
<SecurePage RequiredRoles="Administrator,Manager" PageName="Items Management">

    <PageTitle>Items Management</PageTitle>

    @if (isReady) {
        <Table TItem="Item" @ref="table"
               IsPagination="true" PageItemsSource="[ 20, 25, 30 ]"
               IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
               ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
               ShowExtendDeleteButton="false" ShowDeleteButton="false"
               AddModalTitle="Add New" EditModalTitle="Edit"
               SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
               OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" DoubleClickToEdit="true"
               ShowSkeleton="true" ShowExportButton="true">
            <TableToolbarTemplate>
                <TableToolbarButton TItem="Item" Color="Color.Primary" Icon="fas fa-plus" Text="Import New Items"
                                    OnClick="@(() => ShowImportDialog(ImportMode.Import))"/>
                <TableToolbarButton TItem="Item" Color="Color.Warning" Icon="fas fa-edit" Text="Update Existing Items"
                                    OnClick="@(() => ShowImportDialog(ImportMode.Update))"/>
            </TableToolbarTemplate>
            <BeforeRowButtonTemplate>
                <Button Size="Size.ExtraSmall"
                        Color="@(context.Status == "1" ? Color.Warning : Color.Success)"
                        Icon="@(context.Status == "1" ? "fas fa-toggle-on" : "fas fa-toggle-off")"
                        Text="@(context.Status == "1" ? "Disable" : "Activate")"
                        OnClick=@(() => ToggleItemStatus(context))></Button>
            </BeforeRowButtonTemplate>
            <ExportButtonDropdownTemplate Context="ExportContext">
                <div class="dropdown-item" @onclick="() => ExcelExportAsync(ExportContext)">
                    <i class="fa-regular fa-file-excel"></i>
                    <span>Export Current Page Data to Excel</span>
                </div>
                <div class="dropdown-item" @onclick="() => ExcelExportAllAsync(ExportContext)">
                    <i class="fa-regular fa-file-excel"></i>
                    <span>Export All Data to Excel</span>
                </div>
                <div class="dropdown-item" @onclick="() => CsvExportAsync(ExportContext)">
                    <i class="fa-regular fa-file-excel"></i>
                    <span>MS-CSV</span>
                </div>
                <div class="dropdown-item" @onclick="() => ClipBoardExportAsync(ExportContext)">
                    <i class="fa-regular fa-file-excel"></i>
                    <span>Export Current Page to Clipboard</span>
                </div>
                <div class="dropdown-item" @onclick="() => ClipBoardExportAllAsync(ExportContext)">
                    <i class="fa-regular fa-file-excel"></i>
                    <span>Export All Pages to Clipboard</span>
                </div>
            </ExportButtonDropdownTemplate>
            <TableColumns>
                <TableColumn @bind-Field="@context.Id" Text="Item" Sortable="true" Searchable="true" Required="true"
                             Align="Alignment.Center"
                             IsReadonlyWhenEdit="true" IsRequiredWhenEdit="true">
                </TableColumn>
                <TableColumn @bind-Field="@context.Description" Text="Description" Sortable="true" Searchable="true"
                             Required="true" Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.UnitOfMeasure" Text="U/M" Sortable="true" Required="true"
                             Align="Alignment.Center">
                    <EditTemplate Context="columnContext">
                        <div class="col-12 col-sm-6 col-md-6">
                            <Select TValue="string" ShowRequired="true" @bind-Value="@columnContext.UnitOfMeasure"
                                    Items="UnitOfMeasureItems"
                                    ShowSearch="true"
                                    IsPopover="true"
                                    PlaceHolder="Select UnitOfMeasure..."
                                    DisplayText="U/M">
                                <Options>
                                    <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                                  Active="true"></SelectOption>
                                </Options>
                            </Select>
                        </div>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.MaterialType" Text="Type" Sortable="true" Searchable="true"
                             Required="true" Align="Alignment.Center">
                    <EditTemplate Context="columnContext">
                        <div class="col-12 col-sm-6 col-md-6">
                            @if (string.IsNullOrEmpty(columnContext.Id)) {
                                <!-- 新增模式：可以选择MaterialType -->
                                <Select TValue="string" ShowRequired="true" @bind-Value="@columnContext.MaterialType"
                                        ShowSearch="true"
                                        IsPopover="true"
                                        PlaceHolder="Select MaterialType..."
                                        DisplayText="Type">
                                    <Options>
                                        <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                                      Active="true"></SelectOption>
                                        <SelectOption Value="item" Text="Item"></SelectOption>
                                        <SelectOption Value="Virtual" Text="Virtual"></SelectOption>
                                    </Options>
                                </Select>
                            }
                            else {
                                <!-- 编辑模式：只读显示MaterialType -->
                                <BootstrapInput @bind-Value="@columnContext.MaterialType"
                                                IsDisabled="true"
                                                DisplayText="Type"
                                                ShowRequired="true"/>
                            }
                        </div>
                    </EditTemplate>
                    <SearchTemplate Context="columnContext">
                        <div class="col-12 col-sm-6 col-md-6">
                            <Select TValue="string" ShowRequired="true" @bind-Value="@columnContext.PMTCode"
                                    ShowSearch="true"
                                    IsPopover="true"
                                    PlaceHolder="Select Source..."
                                    DisplayText="Source">
                                <Options>
                                    <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                                  Active="true"></SelectOption>
                                    <SelectOption Value="Purchased" Text="Purchased"></SelectOption>
                                    <SelectOption Value="Manufactured" Text="Manufactured"></SelectOption>
                                    <SelectOption Value="Transferred" Text="Transferred"></SelectOption>
                                </Options>
                            </Select>
                        </div>
                    </SearchTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.PMTCode" Text="Source" Sortable="true" Required="true"
                             Align="Alignment.Center">
                    <EditTemplate Context="columnContext">
                        <div class="col-12 col-sm-6 col-md-6">
                            <Select TValue="string" ShowRequired="true" @bind-Value="@columnContext.PMTCode"
                                    ShowSearch="true"
                                    IsPopover="true"
                                    PlaceHolder="Select Source..."
                                    DisplayText="Source">
                                <Options>
                                    <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                                  Active="true"></SelectOption>
                                    <SelectOption Value="Purchased" Text="Purchased"></SelectOption>
                                    <SelectOption Value="Manufactured" Text="Manufactured"></SelectOption>
                                    <SelectOption Value="Transferred" Text="Transferred"></SelectOption>
                                </Options>
                            </Select>
                        </div>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.ABCCode" Text="ABC" Sortable="true" Align="Alignment.Center">
                    <EditTemplate Context="columnContext">
                        <div class="col-12 col-sm-6 col-md-6">
                            <Select TValue="string" ShowRequired="true" @bind-Value="@columnContext.ABCCode"
                                    ShowSearch="true"
                                    IsPopover="true"
                                    PlaceHolder="Select ABC Code..."
                                    DisplayText="ABC Code">
                                <Options>
                                    <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                                  Active="true"></SelectOption>
                                    <SelectOption Value="A" Text="A"></SelectOption>
                                    <SelectOption Value="B" Text="B"></SelectOption>
                                    <SelectOption Value="C" Text="C"></SelectOption>
                                </Options>
                            </Select>
                        </div>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.UnitCost" Step="0.00000001" Text="Unit Cost" Sortable="true"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.UnitPrice" Text="Unit Price" Sortable="true"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.IsLotTracked" Text="LotTracked" Sortable="true"
                             Align="Alignment.Center"
                             IsReadonlyWhenEdit="true">
                    <Template Context="columnContext">
                        @if (columnContext.Value ?? false) {
                            <Tag Color="Color.Success">LotTracked</Tag>
                        }
                        else {
                            <Tag Color="Color.Danger">Not LotTracked</Tag>
                        }
                    </Template>
                </TableColumn>
                <TableColumn @bind-Field="@context.Status" Text="Status" Sortable="true" Align="Alignment.Center">
                    <Template Context="columnContext">
                        @if (columnContext.Value == "1") {
                            <Tag Color="Color.Success">Active</Tag>
                        }
                        else {
                            <Tag Color="Color.Danger">Disabled</Tag>
                        }
                    </Template>
                    <EditTemplate Context="columnContext">
                        <div class="col-12 col-sm-6 col-md-6">
                            <Select TValue="string" ShowRequired="true" @bind-Value="@columnContext.Status"
                                    ShowSearch="true"
                                    IsPopover="true"
                                    PlaceHolder="Select Status..."
                                    DisplayText="Status">
                                <Options>
                                    <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                                  Active="true"></SelectOption>
                                    <SelectOption Value="1" Text="Enable"></SelectOption>
                                    <SelectOption Value="0" Text="Disabled"></SelectOption>
                                </Options>
                            </Select>
                        </div>
                    </EditTemplate>
                </TableColumn>
            </TableColumns>
            <EmptyTemplate>
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-box fa-3x mb-3"></i>
                        <h5>No items found</h5>
                        <p>No items match your search criteria. Try adjusting your search or add a new item.</p>
                    </div>
                </div>
            </EmptyTemplate>
        </Table>
    }

    <!-- Import Dialog -->
    <Modal @ref="importModal" IsKeyboard="true" OnCloseAsync="@OnImportDialogClose">
        <ModalDialog Title="@GetImportDialogTitle()" Size="Size.Large" ShowCloseButton="true" ShowSaveButton="false">
            <BodyTemplate>
                <div class="mb-4">
                    <label class="form-label">Select Excel File (.xlsx, .xls)</label>
                    <DropUpload @ref="dropUpload"
                                OnChange="@OnDropUpload"
                                OnDelete="@OnFileDelete"
                                FooterText="File size is no more than 10MB, supports .xlsx, .xls "
                                IsDisabled="@isImporting"
                                IsMultiple="false"
                                ShowProgress="true"
                                ShowUploadFileList="true"
                                ShowFooter="true"
                                Accept=".xlsx, .xls">

                    </DropUpload>
                </div>

                <div class="mb-3">
                    <a href="api/items/template" target="_blank" class="btn btn-info btn-sm">
                        <i class="fas fa-download me-2"></i>Download Template
                    </a>
                    <small class="text-muted ms-2">(Optional - if you need a template)</small>
                </div>

                @if (validationResult != null) {
                    <div class="mt-4">
                        <h6>Validation Results:</h6>

                        @if (validationResult.ValidCount > 0) {
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>@GetValidationMessage()
                            </div>
                        }

                        @if (validationResult.ErrorCount > 0) {
                            <div class="alert alert-danger">
                                <strong>@validationResult.ErrorCount errors found:</strong>
                                <ul class="mb-0 mt-2">
                                    @foreach (var error in validationResult.Errors.Take(10)) {
                                        <li>Row @error.RowNumber (@error.Field): @error.Message</li>
                                    }
                                    @if (validationResult.Errors.Count > 10) {
                                        <li><em>... and @(validationResult.Errors.Count - 10) more errors</em></li>
                                    }
                                </ul>
                            </div>
                        }
                    </div>
                }
            </BodyTemplate>
            <FooterTemplate>
                <Button Color="@GetButtonColor()" OnClick="@(() => ExecuteOperation(currentImportMode))"
                        IsDisabled="@(!CanImport)" IsLoading="@isImporting">
                    <i class="@GetButtonIcon() me-2"></i>@GetButtonText()
                </Button>
                @if (validationResult != null) {
                    @if (validationResult.ErrorCount > 0 && validationResult.ValidCount > 0) {
                        <div class="text-warning mt-2">
                            <small><i class="fas fa-exclamation-triangle me-1"></i>
                                Will import @validationResult.ValidCount valid record(s) and
                                skip @validationResult.ErrorCount invalid record(s)
                            </small>
                        </div>
                    }
                    else if (validationResult.ErrorCount > 0 && validationResult.ValidCount == 0) {
                        <div class="text-danger mt-2">
                            <small><i class="fas fa-times-circle me-1"></i>
                                No valid records found. Please fix errors in your file.
                            </small>
                        </div>
                    }
                    else if (validationResult.ErrorCount == 0) {
                        <div class="text-success mt-2">
                            <small><i class="fas fa-check-circle me-1"></i>
                                All @validationResult.ValidCount record(s) are valid and ready to import
                            </small>
                        </div>
                    }
                }
            </FooterTemplate>
        </ModalDialog>
    </Modal>
</SecurePage>

@code {
    private Table<Item>? table;
    private List<Item> Items { get; set; } = new();
    private Item SearchModel { get; set; } = new();
    private List<UnitOfMeasure> UnitOfMeasures { get; set; } = new();
    private string relativePath = "api/items";
    private bool isReady = false; // 确保初始值为 false

    // Import related variables
    private Modal? importModal;
    private DropUpload? dropUpload;
    private IBrowserFile? selectedFile;
    private bool isImporting = false;
    private ValidationResult? validationResult;
    private bool CanImport => selectedFile != null && validationResult != null && validationResult.ValidCount > 0;

    // Import data models
    public class ValidationResult {
        public int TotalRows { get; set; }
        public int ValidCount { get; set; }
        public int ErrorCount { get; set; }
        public List<ValidationError> Errors { get; set; } = new();
    }

    public class ValidationError {
        public int RowNumber { get; set; }
        public string Field { get; set; } = "";
        public string Message { get; set; } = "";
        public string Value { get; set; } = "";
    }

    public class ImportResult {
        public int TotalRows { get; set; }
        public int SuccessCount { get; set; }
        public int SkippedCount { get; set; }
        public int ErrorCount { get; set; }
        public List<string> Messages { get; set; } = new();
    }

    private IEnumerable<SelectedItem> UnitOfMeasureItems => UnitOfMeasures
        .Select(c => new SelectedItem(c.Id, $"{c.Id}"));

    protected override async Task OnInitializedAsync() {
        // 检查认证状态，只有已认证才加载数据
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely() {
        try {
            await LoadUnitOfMeasuresAsync();
            await LoadData();
        }
        catch (Exception ex) {
            // 只有在认证状态下才显示错误消息
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadUnitOfMeasuresAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<UnitOfMeasure>>>("api/units");
            if (response?.IsSuccess == true && response.Data != null) {
                UnitOfMeasures = response.Data;
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load UnitOfMeasure: {response?.Message ?? "Unknown error"}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load UnitOfMeasure: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadData() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<Item>>>(relativePath);
            if (response?.IsSuccess == true) {
                if (response.Data != null) {
                    Items = response.Data;
                }
            }
            else if (response == null) {
                // This might indicate an authentication error that was already handled
                // Don't show an error message as the user is likely being redirected
                return;
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = response?.Message ?? "Failed to load data",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            // Only show error if it's not an authentication-related issue
            if (!ex.Message.Contains("401") && !ex.Message.Contains("Authentication") && !ex.Message.Contains("invalid start of a value")) {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load data: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
    }

    private Task<QueryData<Item>> OnQueryAsync(QueryPageOptions options) {
        var items = Items.Where(options.ToFilterFunc<Item>());
        if (!string.IsNullOrEmpty(options.SearchText)) {
            // Use Linq to process
            items = Items.Where(i =>
                (!string.IsNullOrEmpty(i.Description) && i.Description.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase))
                ||
                (!string.IsNullOrEmpty(i.Id) && i.Id.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase))
                ||
                (!string.IsNullOrEmpty(i.PMTCode) && i.PMTCode.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase))
            );
        }

        if (!string.IsNullOrEmpty(SearchModel.Description)) {
            items = items.Where(i => i.Description == SearchModel.Description);
        }


        if (!string.IsNullOrEmpty(SearchModel.Id)) {
            items = items.Where(i => i.Id == SearchModel.Id);
        }

        if (!string.IsNullOrEmpty(SearchModel.PMTCode)) {
            items = items.Where(i => i.PMTCode == SearchModel.PMTCode);
        }


        // Use Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<Item>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<Item> OnAddAsync() {
        return await Task.FromResult(new Item() {
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(Item item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Added successfully",
                        Color = Color.Success
                    });
                    await LoadData();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Updated successfully",
                        Color = Color.Success
                    });
                    await LoadData();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Save failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }


    private Task OnResetSearchAsync(Item model) {
        model.Id = "";
        model.Description = "";
        model.PMTCode = "";
        return Task.CompletedTask;
    }

    private async Task ToggleItemStatus(Item item) {
        try {
            // 切换状态：1 -> 0, 0 -> 1
            var newStatus = item.Status == "1" ? "0" : "1";
            var originalStatus = item.Status;

            // 创建更新对象
            var updateItem = new Item {
                Id = item.Id,
                Description = item.Description,
                UnitOfMeasure = item.UnitOfMeasure,
                MaterialType = item.MaterialType,
                PMTCode = item.PMTCode,
                ABCCode = item.ABCCode,
                UnitCost = item.UnitCost,
                UnitPrice = item.UnitPrice,
                Status = newStatus,
                CreatedDate = item.CreatedDate,
                RecordDate = DateTime.Now
            };

            var response = await Api.PutAsync($"{relativePath}/{item.Id}", updateItem);
            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = $"Status {(newStatus == "1" ? "enabled" : "disabled")} successfully",
                    Color = Color.Success
                });

                // 重新加载数据并刷新表格
                await LoadData();
                await table?.QueryAsync();
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to toggle status: {error}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to toggle status: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    // Import related methods
    private ImportMode currentImportMode = ImportMode.Import;

    private async Task ShowImportDialog(ImportMode mode) {
        currentImportMode = mode;
        // 重置所有导入相关状态
        await ResetImportState();
        await importModal!.Show();
    }

    /// <summary>
    /// 重置导入状态
    /// </summary>
    private Task ResetImportState() {
        selectedFile = null;
        validationResult = null;
        isImporting = false;

        // 清除DropUpload组件的文件列表
        dropUpload?.UploadFiles.Clear();

        return Task.CompletedTask;
    }

    /// <summary>
    /// 对话框关闭时的回调
    /// </summary>
    private async Task OnImportDialogClose() {
        // 对话框关闭时重置状态，确保下次打开时是干净的状态
        await ResetImportState();
    }

    /// <summary>
    /// 文件删除时的回调
    /// </summary>
    private Task<bool> OnFileDelete(UploadFile file) {
        // 用户删除文件时，清除选中的文件和验证结果
        selectedFile = null;
        validationResult = null;
        StateHasChanged();
        return Task.FromResult(true);
    }

    private async Task OnDropUpload(UploadFile file) {
        if (file?.File != null) {
            // 文件大小验证
            var maxFileSize = 10 * 1024 * 1024; // 10MB
            if (file.Size > maxFileSize) {
                await MessageService.Show(new MessageOption {
                    Content = "File size exceeds 10MB limit",
                    Color = Color.Danger
                });
                return;
            }

            // 文件格式验证
            var allowedExtensions = new[] { ".xlsx", ".xls" };
            var fileExtension = Path.GetExtension(file.File.Name).ToLowerInvariant();
            if (!allowedExtensions.Contains(fileExtension)) {
                await MessageService.Show(new MessageOption {
                    Content = "Only .xlsx and .xls file formats are supported",
                    Color = Color.Danger
                });
                return;
            }

            selectedFile = file.File;
            validationResult = null;

            await ValidateFile();
            StateHasChanged();
        }
    }


    private async Task ValidateFile() {
        if (selectedFile == null) return;

        try {
            var maxFileSize = 10 * 1024 * 1024; // 10MB
            if (selectedFile.Size > maxFileSize) {
                validationResult = new ValidationResult {
                    TotalRows = 0,
                    ValidCount = 0,
                    ErrorCount = 1,
                    Errors = new List<ValidationError> {
                        new ValidationError {
                            RowNumber = 0,
                            Field = "File",
                            Message = "File size exceeds 10MB limit",
                            Value = $"{selectedFile.Size / 1024 / 1024}MB"
                        }
                    }
                };
                return;
            }

            // 发送文件到后端进行验证
            using var content = new MultipartFormDataContent();
            var fileContent = new StreamContent(selectedFile.OpenReadStream(maxFileSize));
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(selectedFile.ContentType);
            content.Add(fileContent, "file", selectedFile.Name);

            // 添加模式参数
            content.Add(new StringContent(currentImportMode.ToString()), "mode");

            var response = await Api.PostFileAsync($"{relativePath}/validate", content);
            if (response.IsSuccessStatusCode) {
                var responseContent = await response.Content.ReadAsStringAsync();
                var httpResponse = JsonSerializer.Deserialize<HttpResponseModel<ValidationResult>>(responseContent, new JsonSerializerOptions {
                    PropertyNameCaseInsensitive = true
                });

                if (httpResponse?.IsSuccess == true && httpResponse.Data != null) {
                    validationResult = httpResponse.Data;
                }
                else {
                    validationResult = new ValidationResult {
                        TotalRows = 0,
                        ValidCount = 0,
                        ErrorCount = 1,
                        Errors = new List<ValidationError> {
                            new ValidationError {
                                RowNumber = 0,
                                Field = "Validation",
                                Message = httpResponse?.Message ?? "Validation failed",
                                Value = ""
                            }
                        }
                    };
                }
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                validationResult = new ValidationResult {
                    TotalRows = 0,
                    ValidCount = 0,
                    ErrorCount = 1,
                    Errors = new List<ValidationError> {
                        new ValidationError {
                            RowNumber = 0,
                            Field = "Server",
                            Message = $"Server error: {error}",
                            Value = ""
                        }
                    }
                };
            }
        }
        catch (Exception ex) {
            validationResult = new ValidationResult {
                TotalRows = 0,
                ValidCount = 0,
                ErrorCount = 1,
                Errors = new List<ValidationError> {
                    new ValidationError {
                        RowNumber = 0,
                        Field = "Exception",
                        Message = $"Error validating file: {ex.Message}",
                        Value = ""
                    }
                }
            };
        }
    }

    private async Task ExecuteOperation(ImportMode mode) {
        if (selectedFile == null || validationResult == null) return;

        isImporting = true;
        try {
            var maxFileSize = 10 * 1024 * 1024; // 10MB
            using var content = new MultipartFormDataContent();
            var fileContent = new StreamContent(selectedFile.OpenReadStream(maxFileSize));
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(selectedFile.ContentType);
            content.Add(fileContent, "file", selectedFile.Name);

            var endpoint = mode == ImportMode.Import ? "import" : "update";
            var operationName = mode == ImportMode.Import ? "Import" : "Update";

            var response = await Api.PostFileAsync($"{relativePath}/{endpoint}", content);
            if (response.IsSuccessStatusCode) {
                var responseContent = await response.Content.ReadAsStringAsync();
                var httpResponse = JsonSerializer.Deserialize<HttpResponseModel<ImportResult>>(responseContent, new JsonSerializerOptions {
                    PropertyNameCaseInsensitive = true
                });

                if (httpResponse?.IsSuccess == true && httpResponse.Data != null) {
                    var result = httpResponse.Data;
                    var message = $"{operationName} completed! ";

                    if (result.SuccessCount > 0) {
                        message += $"✅ {result.SuccessCount} record(s) processed successfully. ";
                    }

                    if (result.SkippedCount > 0) {
                        message += $"⚠️ {result.SkippedCount} record(s) skipped. ";
                    }

                    if (result.ErrorCount > 0) {
                        message += $"❌ {result.ErrorCount} record(s) failed. ";
                    }

                    if (result.Messages?.Any() == true) {
                        message += "\n\nDetails:\n" + string.Join("\n", result.Messages.Take(20)); // 限制显示前20条详细信息
                        if (result.Messages.Count > 20) {
                            message += $"\n... and {result.Messages.Count - 20} more messages";
                        }
                    }

                    var messageColor = result.SuccessCount > 0 ? Color.Success :
                        result.SkippedCount > 0 ? Color.Warning : Color.Danger;

                    await MessageService.Show(new MessageOption {
                        Content = message,
                        Color = messageColor
                    });

                    await importModal!.Close();
                    await ResetImportState();
                    await LoadData();
                    await table!.QueryAsync();
                }
                else {
                    await MessageService.Show(new MessageOption {
                        Content = $"{operationName} failed: {httpResponse?.Message ?? "Unknown error"}",
                        Color = Color.Danger
                    });
                }
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                await MessageService.Show(new MessageOption {
                    Content = $"{operationName} failed: {error}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Error during {mode.ToString().ToLower()}: {ex.Message}",
                Color = Color.Danger
            });
        }
        finally {
            isImporting = false;
        }
    }

    // UI helper methods for import dialog
    private string GetImportDialogTitle() {
        return currentImportMode == ImportMode.Import
            ? "Import New Items from Excel"
            : "Update Existing Items from Excel";
    }

    private Color GetButtonColor() {
        return currentImportMode == ImportMode.Import ? Color.Primary : Color.Warning;
    }

    private string GetButtonIcon() {
        return currentImportMode == ImportMode.Import ? "fas fa-plus" : "fas fa-edit";
    }

    private string GetButtonText() {
        return currentImportMode == ImportMode.Import ? "Import New Records" : "Update Existing Records";
    }

    private string GetValidationMessage() {
        if (validationResult == null) return "";

        var operationType = currentImportMode == ImportMode.Import ? "import" : "update";
        return $"{validationResult.ValidCount} valid records ready to {operationType}";
    }

    private async Task ExcelExportAsync(ITableExportContext<Item> context) {
        // 自定义导出模板导出当前页面数据为 Excel 方法
        // 使用 BootstrapBlazor 内置服务 ITableExcelExport 实例方法 ExportAsync 进行导出操作
        // 导出数据使用 context 传递来的 Rows/Columns 即为当前页数据
        var ret = await TableExport.ExportExcelAsync(context.Rows, context.Columns, $"Test_{DateTime.Now:yyyyMMddHHmmss}.xlsx");

        // 返回 true 时自动弹出提示框
        await ShowToast(ret);
    }

    private async Task ExcelExportAllAsync(ITableExportContext<Item> context) {
        // 自定义导出模板导出当前页面数据为 Excel 方法
        // 使用 BootstrapBlazor 内置服务 ITableExport 实例方法 ExportExcelAsync 进行导出操作
        // 通过 context 参数的查询条件
        var option = context.BuildQueryPageOptions();

        // 通过内置扩展方法 GetFilterFunc 过滤数据
        // EFCore 可使用 GetFilterLambda 获得表达式直接给 Where 方法使用
        var data = Items.Where(option.ToFilterFunc<Item>());

        // 导出符合条件的所有数据 data
        var ret = await TableExport.ExportExcelAsync(data, context.Columns, $"Test_{DateTime.Now:yyyyMMddHHmmss}.xlsx");

        // 返回 true 时自动弹出提示框
        await ShowToast(ret);
    }

    private async Task CsvExportAsync(ITableExportContext<Item> context) {
        // 自定义导出模板导出当前页面数据为 Csv 方法
        // 使用 BootstrapBlazor 内置服务 ITableExcelExport 实例方法 ExportCsvAsync 进行导出操作
        // 导出数据使用 context 传递来的 Rows/Columns 即为当前页数据
        var ret = await TableExport.ExportCsvAsync(context.Rows, context.Columns, $"Test_{DateTime.Now:yyyyMMddHHmmss}.csv");

        // 返回 true 时自动弹出提示框
        await ShowToast(ret);
    }

    private async Task ClipBoardExportAllAsync(ITableExportContext<Item> context) {
        // 自定义导出当前页面数据到剪切板,可以直接粘贴到 Excel 中
        // 使用 BootstrapBlazor 内置服务 ClipboardService 实例方法 Copy 进行导出操作
        // 通过 context 参数的查询条件
        var option = context.BuildQueryPageOptions();

        // 通过内置扩展方法 GetFilterFunc 过滤数据
        // EFCore 可使用 GetFilterLambda 获得表达式直接给 Where 方法使用
        var data = Items.Where(option.ToFilterFunc<Item>());

        // 导出符合条件的所有数据 data
        await ExportToClipBoard(context.Columns, data);

        // 返回 true 时自动弹出提示框
        await ShowToast(true);
    }

    private async Task ExportToClipBoard(IEnumerable<ITableColumn> columns, IEnumerable<Item> rows) {
        var sb = new StringBuilder();

        // 导出表格 Header
        var titles = columns.Select(x => x.GetDisplayName());
        sb.AppendJoin('\t', titles).AppendLine();

        // 导出表格 Row
        var fieldNames = columns.Select(x => x.GetFieldName());
        foreach (var row in rows) {
            var values = fieldNames.Select(x => row.GetType().GetProperty(x)?.GetValue(row)).ToArray();
            sb.AppendJoin('\t', values).AppendLine();
        }

        var result = sb.ToString();
        await ClipboardService.Copy(result);
    }

    private async Task ClipBoardExportAsync(ITableExportContext<Item> context) {
        // 自定义导出当前页面数据到剪切板,可以直接粘贴到 Excel 中
        // 使用 BootstrapBlazor 内置服务 ClipboardService 实例方法 Copy 进行导出操作
        // 导出数据使用 context 传递来的 Rows/Columns 即为当前页数据
        await ExportToClipBoard(context.Columns, context.Rows);

        // 返回 true 时自动弹出提示框
        await ShowToast(true);
    }

    private async Task ShowToast(bool result) {
        if (result) {
            await Toast.Success("Data Export", "Data exported successfully, closing in 4 seconds");
        }
        else {
            await Toast.Error("Data Export", "Failed to export data, closing in 4 seconds");
        }
    }

}

