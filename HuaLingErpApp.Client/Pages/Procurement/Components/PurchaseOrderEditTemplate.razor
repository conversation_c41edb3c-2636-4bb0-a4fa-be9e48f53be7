@using System.ComponentModel.DataAnnotations
@using Console = System.Console
@inherits OwningComponentBase

@if (Model is not null) {
    <EditorForm Model="Model" RowType="RowType.Inline" ItemsPerRow="2">
        <FieldItems>
            <!-- Hidden fields -->
            <EditorItem @bind-Field="@context.Id" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.PoNum" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.CreatedBy" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.ModifiedBy" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.CreatedDate" Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.RecordDate" Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.TaxRate" Readonly="true" Step="0.01"></EditorItem>
            <EditorItem @bind-Field="@context.PoDate" Readonly="true"></EditorItem>
            <EditorItem @bind-Field="@context.ExchangeRate" Readonly="true" Required="false" Ignore="true"></EditorItem>

            <!-- Vendor AutoFill -->
            <EditorItem @bind-Field="@context.VendorNum" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <AutoFill @bind-Value="SelectedVendor"
                                  Items="Vendors"
                                  OnGetDisplayText="GetVendorDisplay"
                                  OnSelectedItemChanged="OnVendorChanged"
                                  IsLikeMatch="true"
                                  DisplayText="Vendor"
                                  RequiredErrorMessage="Vendor is required"
                                  ShowRequired="true"
                                  IsSelectAllTextOnFocus="true"
                                  Placeholder="Search for vendors...">
                        </AutoFill>
                    </div>
                </EditTemplate>
            </EditorItem>

            <!-- TaxCode & TaxRate AutoFill -->
            <EditorItem @bind-Field="@context.TaxCode" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <AutoFill @bind-Value="SelectedTaxCode"
                                  Items="TaxCodes"
                                  OnGetDisplayText="GetTaxCodeDisplay"
                                  OnSelectedItemChanged="OnTaxCodeChanged"
                                  IsLikeMatch="true"
                                  DisplayText="Tax Code"
                                  RequiredErrorMessage="Tax Code is required"
                                  ShowRequired="true"
                                  IsSelectAllTextOnFocus="true"
                                  Placeholder="Search for tax code...">
                        </AutoFill>
                    </div>
                </EditTemplate>
            </EditorItem>

            <!-- Dropdown -->
            <EditorItem @bind-Field="@context.CurrencyCode" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <Select TValue="string" @bind-Value="@context.CurrencyCode" Items="CurrencyItems"
                                ShowSearch="true"
                                IsPopover="true"
                                PlaceHolder="Select currency...">
                            <Options>
                                <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                              Active="true"></SelectOption>
                            </Options>
                        </Select>
                    </div>
                </EditTemplate>
            </EditorItem>

            <EditorItem @bind-Field="@context.TermsCode" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <Select TValue="string" @bind-Value="@context.TermsCode" Items="TermsCodeItems"
                                ShowSearch="true"
                                IsPopover="true"
                                PlaceHolder="Select TermsCode...">
                            <Options>
                                <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                              Active="true"></SelectOption>
                            </Options>
                        </Select>
                    </div>
                </EditTemplate>
            </EditorItem>

        </FieldItems>
    </EditorForm>
}

@code {
    [Parameter] [Required] public PurchaseOrder? Model { get; set; }
    [Parameter] [Required] public List<Vendor> Vendors { get; set; } = [];
    [Parameter] [Required] public List<CurrencyCode> Currencies { get; set; } = [];
    [Parameter] [Required] public List<TermsCode> TermsCodes { get; set; } = [];
    [Parameter] [Required] public List<TaxCode> TaxCodes { get; set; } = [];

    private Vendor? SelectedVendor { get; set; }
    private TaxCode? SelectedTaxCode { get; set; }

    private IEnumerable<SelectedItem> CurrencyItems => Currencies
        .Select(c => new SelectedItem(c.Id, $"{c.Id} - {c.Description}"));

    private IEnumerable<SelectedItem> TermsCodeItems => TermsCodes
        .Select(c => new SelectedItem(c.Id, $"{c.Id} - {c.Description}"));

    private IEnumerable<SelectedItem> TaxCodeItems => TaxCodes
        .Select(c => new SelectedItem(c.Id, $"{c.Id}"));

    protected override void OnParametersSet() {
        base.OnParametersSet();

        // Initialize AutoFill values based on Model
        if (Model != null) {
            // Initialize Vendor
            if (!string.IsNullOrEmpty(Model.VendorNum)) {
                SelectedVendor = Vendors.FirstOrDefault(v => v.VendNum == Model.VendorNum);
            }

            // Initialize TaxCode
            if (!string.IsNullOrEmpty(Model.TaxCode)) {
                SelectedTaxCode = TaxCodes.FirstOrDefault(t => t.Id == Model.TaxCode);
            }
        }

        foreach (var vendor in Vendors) {
            Console.WriteLine(vendor.Name);
        }
    }


    // private List<SelectedItem> CurrencyItems => new List<SelectedItem> { new SelectedItem(" ", "Select currency..."){IsDisabled = true,Active = true,} }
    //     .Concat(Currencies.Select(c => new SelectedItem(c.Id, $"{c.Id} - {c.Description}")))
    //     .ToList();
    private static string GetVendorDisplay(Vendor? v) => v is null ? string.Empty : $"{v.VendNum} - {v.Name}";
    private static string GetTaxCodeDisplay(TaxCode? t) => t is null ? string.Empty : $"{t.Id}";

    private Task OnVendorChanged(Vendor? vendor) {
        if (vendor is null || Model is null) return Task.CompletedTask;

        Model.VendorNum = vendor.VendNum;
        Model.CurrencyCode = vendor.CurrencyCode ?? string.Empty;
        Model.TermsCode = vendor.TermsCode ?? string.Empty;
        Model.Contact = vendor.Contact ?? string.Empty;
        Model.Phone = vendor.Phone ?? string.Empty;
        Model.Email = vendor.Email ?? string.Empty;
        Model.Address = vendor.Address ?? string.Empty;
        var taxCode = TaxCodes.FirstOrDefault(c => c.Id == vendor.TaxCode);
        if (taxCode != null) {
            Model.TaxCode = taxCode.Id;
            Model.TaxRate = taxCode.TaxRate;
            SelectedTaxCode = taxCode; // 更新 AutoFill 的绑定值
        }

        Model.TaxRate = TaxCodes.FirstOrDefault(c => c.Id == vendor.TaxCode)?.TaxRate ?? 0;

        // Update SelectedVendor
        SelectedVendor = vendor;

        return Task.CompletedTask;
    }

    private Task OnTaxCodeChanged(TaxCode? taxCode) {
        if (taxCode is null || Model is null) return Task.CompletedTask;

        Model.TaxRate = taxCode.TaxRate;


        // Update SelectedTaxCode
        SelectedTaxCode = taxCode;
        return Task.CompletedTask;
    }

}
