@page "/procurement/purchaseorders"
@using System.Net.Http.Headers
@using HuaLingErpApp.Client.Pages.Procurement.Components
@using HuaLingErpApp.Client.Components
@using HuaLingErpApp.Shared
@using HuaLingErpApp.Shared.Models
@using Microsoft.AspNetCore.Authorization
@using Microsoft.AspNetCore.Components.Authorization
@inject HttpClient Http
@inject MessageService MessageService
@inject DialogService DialogService
@inject ApiService Api
@inject NavigationManager NavigationManager


<PageTitle>Purchase Order Management</PageTitle>

<SecurePage RequiredRoles="Administrator,Manager" PageName="Purchase Order Management">
    @if (isReady) {
        <Table TItem="PurchaseOrder"
               IsPagination="true" PageItemsSource="new int[] { 20, 25, 30 }"
               IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
               ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
               AddModalTitle="Add New" EditModalTitle="Edit"
               SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
               OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
               DoubleClickToEdit="true"
               ShowSkeleton="true">

            <EditTemplate>
                <PurchaseOrderEditTemplate Model="context" Vendors="VendorItems"
                                           Currencies="CurrencyItems" TermsCodes="TermsCodeItems"
                                           TaxCodes="TaxCodeItems"/>
            </EditTemplate>
            <TableColumns>

                <TableColumn @bind-Field="@context.PoNum" Text=" " Sortable="false" Align="Alignment.Center" Width="10"
                             IsVisibleWhenAdd="false" IsRequiredWhenAdd="false" IsRequiredWhenEdit="false"
                             Required="false">
                    <Template Context="columnContext">
                        <Button
                            @onclick=@(() => Navigation.NavigateTo($"procurement/purchaseorderlines/{columnContext.Value}"))
                            Color="Color.Light" Size="Size.ExtraSmall">
                            <i class="fa fa-list"></i>
                        </Button>
                        @* <a href="@($"procurement/purchaseorderlines/{columnContext.Value}")" class="btn btn-sm btn-link" *@
                        @*    title="View Lines"> *@

                        @* </a> *@
                    </Template>

                </TableColumn>

                <TableColumn @bind-Field="@context.PoNum" Text="PO Number" Sortable="true" Searchable="true"
                             IsVisibleWhenAdd="false" IsRequiredWhenAdd="false" IsRequiredWhenEdit="false"
                             Required="false" Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.VendorNum" Text="Vendor" Sortable="true" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.Status" Text="Status" Sortable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.CurrencyCode" Required="true" Text="Currency" Sortable="true"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.TermsCode" Required="true" Text="Terms" Sortable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.Contact" Text="Contact" Sortable="true" Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.Phone" Text="Phone" Sortable="true" Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.Email" Text="Email" Sortable="true" Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.PoDate" Text="PO Date" Sortable="true" FormatString="yyyy-MM-dd"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.TaxCode" Text="Tax Code" Sortable="true" Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.TaxRate" Step="0.01" Text="Tax Rate" Sortable="true"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.ExchangeRate" FormatString="0.0000" Step="0.0001"
                             Text="Exchange Rate" Sortable="true"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.Address" Text="Address" Sortable="true" Align="Alignment.Center"/>
            </TableColumns>
            <EmptyTemplate>
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-file-invoice fa-3x mb-3"></i>
                        <h5>No purchase orders found</h5>
                        <p>No purchase orders match your search criteria. Try adjusting your search or create a new purchase order.</p>
                    </div>
                </div>
            </EmptyTemplate>
        </Table>
    }
</SecurePage>

@code {
    private List<PurchaseOrder> Items { get; set; } = new();
    private PurchaseOrder SearchModel { get; set; } = new();

    private string relativePath = "api/purchaseorder";
    private bool isReady = false; // 确保初始值为 false

    // Vendor and Currency related data
    private List<Vendor> VendorItems { get; set; } = new();
    private List<CurrencyCode> CurrencyItems { get; set; } = new();
    private List<TermsCode> TermsCodeItems { get; set; } = new();
    private List<TaxCode> TaxCodeItems { get; set; } = new();

    protected override async Task OnInitializedAsync() {
        // 检查认证状态，只有已认证才加载数据
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely() {
        try {
            await LoadVendorsAsync();
            await LoadCurrenciesAsync();
            await LoadTermsCodesAsync();
            await LoadTaxCodesAsync();
            await LoadData();
        }
        catch (Exception ex) {
            // 只有在认证状态下才显示错误消息
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadCurrenciesAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<CurrencyCode>>>("api/currencycode");
            if (response?.IsSuccess == true && response.Data != null) {
                CurrencyItems = response.Data;
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load currencies: {response?.Message ?? "Unknown error"}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load currencies: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadTermsCodesAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<TermsCode>>>("api/termscode");
            if (response?.IsSuccess == true && response.Data != null) {
                TermsCodeItems = response.Data;
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load TermsCode: {response?.Message ?? "Unknown error"}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load TermsCode: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadTaxCodesAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<TaxCode>>>("api/taxcode");
            if (response?.IsSuccess == true && response.Data != null) {
                TaxCodeItems = response.Data;
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load TaxCodes: {response?.Message ?? "Unknown error"}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load TaxCodes: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadVendorsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<Vendor>>>("api/vendors");
            if (response?.IsSuccess == true && response.Data != null) {
                VendorItems = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load vendors: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadData() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<PurchaseOrder>>>(relativePath);
            if (response?.IsSuccess == true) {
                if (response.Data != null) {
                    Items = response.Data;
                }
            }
            else if (response == null) {
                // This might indicate an authentication error that was already handled
                // Don't show an error message as the user is likely being redirected
                return;
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = response?.Message ?? "Failed to load data",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            // Only show error if it's not an authentication-related issue
            if (!ex.Message.Contains("401") && !ex.Message.Contains("Authentication") && !ex.Message.Contains("invalid start of a value")) {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load data: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
    }

    private Task<QueryData<PurchaseOrder>> OnQueryAsync(QueryPageOptions options) {
        var items = Items.Where(options.ToFilterFunc<PurchaseOrder>());
        if (!string.IsNullOrEmpty(options.SearchText)) {
            // Use Linq to process
            items = Items.Where(i =>
                !string.IsNullOrEmpty(i.PoNum) && i.PoNum.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.VendorNum) && i.VendorNum.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                // 处理 Status 字段的搜索逻辑
                Enum.TryParse<EnumStatus>(options.SearchText, true, out var status) && i.Status == status
            );
        }

        // Use Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<PurchaseOrder>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<PurchaseOrder> OnAddAsync() {
        return await Task.FromResult(new PurchaseOrder() {
            PoNum = "PN?", // Default value
            PoDate = DateTime.Today,
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(PurchaseOrder item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Added successfully",
                        Color = Color.Success
                    });
                    await LoadData();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Updated successfully",
                        Color = Color.Success
                    });
                    await LoadData();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Save failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }

        return false;
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<PurchaseOrder> items) {
        try {
            var itemList = items.ToList();
            if (!itemList.Any()) {
                await MessageService.Show(new MessageOption {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1) {
                // Single delete
                response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
            }
            else {
                // Batch delete
                response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = "Deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadData();
                return true;
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                throw new Exception(error);
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Delete failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private Task OnResetSearchAsync(PurchaseOrder model) {
        model.PoNum = "";
        model.VendorNum = "";
        return Task.CompletedTask;
    }

}

