@page "/procurement/vendors"
@using HuaLingErpApp.Client.Components
@using HuaLingErpApp.Shared
@inject MessageService MessageService
@inject ApiService Api

<SecurePage RequiredRoles="Administrator,Manager" PageName="Purchase Order Management">
    @if (isReady) {
        <Table TItem="Vendor"
               IsPagination="true" PageItemsSource="[20, 25, 30 ]"
               IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
               ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
               AddModalTitle="Add New" EditModalTitle="Edit"
               SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
               OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
               DoubleClickToEdit="true"
               ShowSkeleton="true">
            <TableColumns>

                <TableColumn @bind-Field="@context.VendNum" Text="Vendor Num" Sortable="true" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.Name" Text="Name" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.CurrencyCode" Text="Currency"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.TermsCode" Text="TermsCode"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.TaxCode" Text="TaxCode"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.Contact" Text="Contact"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.Phone" Text="Phone"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.Email" Text="Email"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.Address" Text="Address"
                             Align="Alignment.Center"/>
            </TableColumns>
            <EmptyTemplate>
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-truck fa-3x mb-3"></i>
                        <h5>No vendors found</h5>
                        <p>No vendors match your search criteria. Try adjusting your search or add a new vendor.</p>
                    </div>
                </div>
            </EmptyTemplate>
        </Table>
    }
</SecurePage>

@code {
    private List<Vendor> VendorItems { get; set; } = new();

    private Vendor SearchModel { get; set; } = new();

    // private ITableSearchModel CustomerSearchModel { get; set; } = new PurchaseOrderLinesSearchModel();
    private string relativePath = "api/vendors";
    private bool isReady;

    protected override async Task OnParametersSetAsync() {
        // 检查认证状态，只有已认证才加载数据
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely() {
        try {
            await LoadVendorsAsync();
        }
        catch (Exception ex) {
            // 只有在认证状态下才显示错误消息
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadVendorsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<Vendor>>>("api/vendors");
            if (response?.IsSuccess == true && response.Data != null) {
                VendorItems = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load vendors: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private Task<QueryData<Vendor>> OnQueryAsync(QueryPageOptions options) {
        var items = VendorItems.Where(options.ToFilterFunc<Vendor>());
        if (!string.IsNullOrEmpty(options.SearchText)) {
            // Use Linq to process
            items = VendorItems.Where(i =>
                !string.IsNullOrEmpty(i.VendNum) && i.VendNum.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Name) && i.Name.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
            );
        }

        // Use Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<Vendor>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<Vendor> OnAddAsync() {
        return await Task.FromResult(new Vendor() {
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(Vendor item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Added successfully",
                        Color = Color.Success
                    });
                    await LoadVendorsAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Updated successfully",
                        Color = Color.Success
                    });
                    await LoadVendorsAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Save failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }

        return false;
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<Vendor> items) {
        try {
            var itemList = items.ToList();
            if (!itemList.Any()) {
                await MessageService.Show(new MessageOption {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1) {
                // Single delete
                response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
            }
            else {
                // Batch delete
                response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = "Deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadVendorsAsync();
                return true;
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                throw new Exception(error);
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Delete failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private Task OnResetSearchAsync(Vendor model) {
        model.VendNum = "";
        model.Name = "";
        return Task.CompletedTask;
    }

}
