@page "/profile/change-password"
@using HuaLingErpApp.Client.Components
@using HuaLingErpApp.Shared.Models
@using HuaLingErpApp.Shared
@using Microsoft.AspNetCore.Authorization
@using System.Text.Json
@attribute [Authorize]
@inject ApiService Api
@inject MessageService MessageService

@implements IDisposable

<PageTitle>Change Password - HuaLing ERP</PageTitle>
<Card >
                    <HeaderTemplate>
                        <h4>Change Password</h4>
                    </HeaderTemplate>
                    <BodyTemplate>
                        <ValidateForm Model="@changePasswordRequest" OnValidSubmit="@HandleChangePassword">
                            <div class="row g-3">
                                <div class="col-12">
                                    <BootstrapPassword @bind-Value="@changePasswordRequest.CurrentPassword"
                                                       DisplayText="Current Password"
                                                       PlaceHolder="Enter current password"
                                                       ShowRequired="true"
                                                       OnValueChanged="@OnCurrentPasswordChanged"
                                                       SkipValidate="true"/>
                                    @if (isVerifyingPassword)
                                    {
                                        <small class="text-muted">
                                            <i class="fas fa-spinner fa-spin me-1"></i>Verifying password...
                                        </small>
                                    }
                                    else if (!string.IsNullOrEmpty(currentPasswordValidationMessage))
                                    {
                                        <small class="@(isCurrentPasswordValid ? "text-success" : "text-danger")">
                                            <i class="@(isCurrentPasswordValid ? "fas fa-check" : "fas fa-times") me-1"></i>@currentPasswordValidationMessage
                                        </small>
                                    }
                                </div>
                                <div class="col-12">
                                    <BootstrapPassword @bind-Value="@changePasswordRequest.NewPassword" 
                                                       DisplayText="New Password" 
                                                       PlaceHolder="Enter new password"
                                                       ShowRequired="true" />
                                </div>
                                <div class="col-12">
                                    <BootstrapPassword @bind-Value="@changePasswordRequest.ConfirmPassword" 
                                                       DisplayText="Confirm Password" 
                                                       PlaceHolder="Confirm new password"
                                                       ShowRequired="true" />
                                </div>
                                <div class="col-12">
                                    <Button ButtonType="ButtonType.Submit" Color="Color.Primary" IsDisabled="@isLoading">
                                        @if (isLoading)
                                        {
                                            <i class="fas fa-spinner fa-spin me-1"></i>
                                        }
                                        Change Password
                                    </Button>
                                    <Button Color="Color.Secondary" class="ms-2" OnClick="@(() => Navigation.NavigateTo("/"))">
                                        Cancel
                                    </Button>
                                </div>
                            </div>
                        </ValidateForm>
                    </BodyTemplate>
                </Card>
@code {
    private ChangePasswordRequest changePasswordRequest = new();
    private bool isLoading = false;
    private bool isVerifyingPassword = false;
    private bool isCurrentPasswordValid = false;
    private string currentPasswordValidationMessage = "";
    private System.Threading.Timer? passwordVerificationTimer;

    // 自定义验证规则
    private List<IValidator> currentPasswordValidateRules = new List<IValidator>
    {
        new RequiredValidator() { ErrorMessage = "Current password is required" }
    };



    private async Task HandleChangePassword(EditContext context)
    {
        isLoading = true;
        try
        {
            var response = await Api.PostAsync("api/auth/change-password", changePasswordRequest);
            
            if (response.IsSuccessStatusCode)
            {
                await MessageService.Show(new MessageOption
                {
                    Content = "Password changed successfully",
                    Color = Color.Success
                });
                
                // 清空表单
                changePasswordRequest = new();
            }
            else
            {
                // 解析错误响应
                var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                await MessageService.Show(new MessageOption
                {
                    Content = errorMessage,
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Error: {ex.Message}",
                Color = Color.Danger
            });
        }
        finally
        {
            isLoading = false;
        }
    }

    private void ResetForm()
    {
        changePasswordRequest = new ChangePasswordRequest();
        currentPasswordValidationMessage = "";
        isCurrentPasswordValid = false;
        passwordVerificationTimer?.Dispose();
    }

    private async Task OnCurrentPasswordChanged(string value)
    {
        // 取消之前的验证计时器
        passwordVerificationTimer?.Dispose();

        // 重置验证状态
        currentPasswordValidationMessage = "";
        isCurrentPasswordValid = false;

        if (string.IsNullOrEmpty(value))
        {
            return;
        }

        // 设置延迟验证（避免用户输入时频繁调用API）
        passwordVerificationTimer = new System.Threading.Timer(async _ =>
        {
            await InvokeAsync(async () =>
            {
                await VerifyCurrentPassword(value);
                StateHasChanged();
            });
        }, null, TimeSpan.FromMilliseconds(800), Timeout.InfiniteTimeSpan); // 800ms延迟
    }

    private async Task VerifyCurrentPassword(string password)
    {
        if (string.IsNullOrEmpty(password)) return;

        isVerifyingPassword = true;
        try
        {
            var request = new VerifyPasswordRequest { Password = password };
            var response = await Api.PostAsync("api/auth/verify-current-password", request);

            if (response.IsSuccessStatusCode)
            {
                var result = await response.Content.ReadFromJsonAsync<HttpResponseModel<bool>>();
                if (result?.IsSuccess == true)
                {
                    isCurrentPasswordValid = result.Data;
                    currentPasswordValidationMessage = isCurrentPasswordValid
                        ? "Current password is correct"
                        : "Current password is incorrect";
                }
                else
                {
                    currentPasswordValidationMessage = "Failed to verify password";
                    isCurrentPasswordValid = false;
                }
            }
            else
            {
                currentPasswordValidationMessage = "Failed to verify password";
                isCurrentPasswordValid = false;
            }
        }
        catch (Exception ex)
        {
            currentPasswordValidationMessage = "Error verifying password";
            isCurrentPasswordValid = false;
        }
        finally
        {
            isVerifyingPassword = false;
        }
    }

    public void Dispose()
    {
        passwordVerificationTimer?.Dispose();
    }
}
