@using System.ComponentModel.DataAnnotations
@using Console = System.Console
@inherits OwningComponentBase

@if (Model is not null) {
    <EditorForm Model="Model" RowType="RowType.Inline" ItemsPerRow="2">
        <FieldItems>
            <!-- Hidden fields -->
            <EditorItem @bind-Field="@context.Id" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.CoNum" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.CreatedBy" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.ModifiedBy" Readonly="true" Required="false" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.CreatedDate" Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.RecordDate" Readonly="true" Ignore="true"></EditorItem>
            <EditorItem @bind-Field="@context.TaxRate" Readonly="true" Step="0.01"></EditorItem>
            <EditorItem @bind-Field="@context.CoDate" Readonly="true"></EditorItem>
            <EditorItem @bind-Field="@context.ExchRate" Readonly="true" Required="false" Ignore="true"></EditorItem>

            <!-- Customer AutoFill -->
            <EditorItem @bind-Field="@context.CustNum" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <AutoFill @bind-Value="SelectedCustomer"
                                  Items="Customers"
                                  OnGetDisplayText="GetCustomerDisplay"
                                  OnSelectedItemChanged="OnCustomerChanged"
                                  IsLikeMatch="true"
                                  DisplayText="Customer"
                                  RequiredErrorMessage="Customer is required"
                                  ShowRequired="true"
                                  IsSelectAllTextOnFocus="true"
                                  Placeholder="Search for customers...">
                        </AutoFill>
                    </div>
                </EditTemplate>
            </EditorItem>

            <!-- TaxCode & TaxRate AutoFill -->
            <EditorItem @bind-Field="@context.TaxCode" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <AutoFill @bind-Value="SelectedTaxCode"
                                  Items="TaxCodes"
                                  OnGetDisplayText="GetTaxCodeDisplay"
                                  OnSelectedItemChanged="OnTaxCodeChanged"
                                  IsLikeMatch="true"
                                  DisplayText="Tax Code"
                                  RequiredErrorMessage="Tax Code is required"
                                  ShowRequired="true"
                                  IsSelectAllTextOnFocus="true"
                                  Placeholder="Search for tax code...">
                        </AutoFill>
                    </div>
                </EditTemplate>
            </EditorItem>

            <!-- Dropdown -->
            <EditorItem @bind-Field="@context.CurrCode" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <Select TValue="string" @bind-Value="@context.CurrCode" Items="CurrencyItems"
                                ShowSearch="true"
                                IsPopover="true"
                                PlaceHolder="Select currency...">
                            <Options>
                                <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                              Active="true"></SelectOption>
                            </Options>
                        </Select>
                    </div>
                </EditTemplate>
            </EditorItem>

            <EditorItem @bind-Field="@context.TermsCode" Required="true">
                <EditTemplate Context="_">
                    <div class="col-12 col-sm-6 col-md-6">
                        <Select TValue="string" @bind-Value="@context.TermsCode" Items="TermsCodeItems"
                                ShowSearch="true"
                                IsPopover="true"
                                PlaceHolder="Select TermsCode...">
                            <Options>
                                <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                              Active="true"></SelectOption>
                            </Options>
                        </Select>
                    </div>
                </EditTemplate>
            </EditorItem>

        </FieldItems>
    </EditorForm>
}

@code {
    [Parameter] [Required] public CustomerOrder? Model { get; set; }
    [Parameter] [Required] public List<Customer> Customers { get; set; } = [];
    [Parameter] [Required] public List<CurrencyCode> Currencies { get; set; } = [];
    [Parameter] [Required] public List<TermsCode> TermsCodes { get; set; } = [];
    [Parameter] [Required] public List<TaxCode> TaxCodes { get; set; } = [];

    private Customer? SelectedCustomer { get; set; }
    private TaxCode? SelectedTaxCode { get; set; }

    private IEnumerable<SelectedItem> CurrencyItems => Currencies
        .Select(c => new SelectedItem(c.Id, $"{c.Id} - {c.Description}"));

    private IEnumerable<SelectedItem> TermsCodeItems => TermsCodes
        .Select(c => new SelectedItem(c.Id, $"{c.Id} - {c.Description}"));

    private IEnumerable<SelectedItem> TaxCodeItems => TaxCodes
        .Select(c => new SelectedItem(c.Id, $"{c.Id}"));

    protected override void OnParametersSet() {
        base.OnParametersSet();

        // Initialize AutoFill values based on Model
        if (Model != null) {
            // Initialize Customer
            if (!string.IsNullOrEmpty(Model.CustNum)) {
                SelectedCustomer = Customers.FirstOrDefault(c => c.CustNum == Model.CustNum);
            }

            // Initialize TaxCode
            if (!string.IsNullOrEmpty(Model.TaxCode)) {
                SelectedTaxCode = TaxCodes.FirstOrDefault(t => t.Id == Model.TaxCode);
            }
        }

        foreach (var customer in Customers) {
            Console.WriteLine(customer.Name);
        }
    }

    private static string GetCustomerDisplay(Customer? c) => c is null ? string.Empty : $"{c.CustNum} - {c.Name}";
    private static string GetTaxCodeDisplay(TaxCode? t) => t is null ? string.Empty : $"{t.Id}";

    private Task OnCustomerChanged(Customer? customer) {
        if (customer is null || Model is null) return Task.CompletedTask;

        Model.CustNum = customer.CustNum;
        Model.CurrCode = customer.CurrCode ?? string.Empty;
        Model.TermsCode = customer.TermsCode ?? string.Empty;
        Model.Contact = customer.Contact ?? string.Empty;
        Model.Phone = customer.Phone ?? string.Empty;
        Model.Email = customer.Email ?? string.Empty;
        Model.Addr = customer.Addr ?? string.Empty;
        var taxCode = TaxCodes.FirstOrDefault(c => c.Id == customer.TaxCode);
        if (taxCode != null) {
            Model.TaxCode = taxCode.Id;
            Model.TaxRate = taxCode.TaxRate;
            SelectedTaxCode = taxCode; // 更新 AutoFill 的绑定值
        }

        Model.TaxRate = TaxCodes.FirstOrDefault(c => c.Id == customer.TaxCode)?.TaxRate ?? 0;

        // Update SelectedCustomer
        SelectedCustomer = customer;

        return Task.CompletedTask;
    }

    private Task OnTaxCodeChanged(TaxCode? taxCode) {
        if (taxCode is null || Model is null) return Task.CompletedTask;

        Model.TaxRate = taxCode.TaxRate;

        // Update SelectedTaxCode
        SelectedTaxCode = taxCode;
        return Task.CompletedTask;
    }

}
