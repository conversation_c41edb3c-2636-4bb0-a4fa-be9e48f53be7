@page "/sales/customerorderlines"
@page "/sales/customerorderlines/{CoNum}"
@using HuaLingErpApp.Client.Pages.Sales.Components
@using HuaLingErpApp.Client.Components
@using HuaLingErpApp.Shared
@inject MessageService MessageService
@inject ApiService Api

<SecurePage RequiredRoles="Administrator,Manager" PageName="Customer Order Lines">
    <PageTitle>Customer Order Lines</PageTitle>

    @if (isReady) {
    <Table TItem="CoItem"
           IsPagination="true" PageItemsSource="new int[] {20, 25,30 }"
           IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
           ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
           AddModalTitle="Add New" EditModalTitle="Edit"
           SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
           OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
           OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync" DoubleClickToEdit="true"
           ShowSkeleton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.CoNum" Text="CO Number" Sortable="true" Searchable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.CoLine" Text="CO Line" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.Item" Text="Item" Sortable="true" Searchable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.QtyOrdered" Text="Qty Ordered" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.QtyShipped" Text="Qty Shipped" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.QtyInvoiced" Text="Qty Invoiced" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.UnitPrice" Text="Unit Price" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.DueDate" Text="Due Date" Sortable="true" Align="Alignment.Center" FormatString="yyyy-MM-dd"/>
            <TableColumn @bind-Field="@context.UnitCostShipped" Text="Unit Cost Shipped" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.Location" Text="Location" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.UnitOfMeasure" Text="UM" Required="true" Sortable="true" Align="Alignment.Center"/>
            <TableColumn @bind-Field="@context.Status" Text="Status" Sortable="true" Searchable="true"
                         Align="Alignment.Center">
            </TableColumn>
        </TableColumns>
        <EmptyTemplate>
            <div class="text-center py-5">
                <div class="text-muted">
                    <i class="fas fa-list-ul fa-3x mb-3"></i>
                    <h5>No order lines found</h5>
                    <p>No customer order lines match your search criteria. Try adjusting your search or add a new order line.</p>
                </div>
            </div>
        </EmptyTemplate>
        <EditTemplate>
            <CustomerOrderLinesEditTemplate Model="context" CoItems="CoItems" CoNums="CoNumItems"
                                            Items="Items" ItemLocations="ItemLocationItems"
                                            UnitOfMeasures = "UnitOfMeasureItems" CoNum=@CoNum/>
        </EditTemplate>
    </Table>
}
    </SecurePage>
@code {
    [Parameter]
    public string? CoNum { get; set; }
    
    private List<CoItem> CoItems { get; set; } = new();

    private CoItem SearchModel { get; set; } = new();

    private string relativePath = "api/customerorderlines";
    private bool isReady;

    private List<string> CoNumItems { get; set; } = new();
    private List<Item> Items { get; set; } = new();
    private List<ItemLocation> ItemLocationItems { get; set; } = new();
    private List<UnitOfMeasure> UnitOfMeasureItems { get; set; } = new();

    protected override async Task OnParametersSetAsync() {
        // 检查认证状态，只有已认证才加载数据
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            if (!string.IsNullOrEmpty(CoNum)) {
                SearchModel.CoNum = CoNum;
            }

            await LoadCoNumsAsync();
            await LoadItemsAsync();
            await LoadItemsLocationsAsync();
            await LoadUnitOfMeasuresAsync();
            await LoadData();
            isReady = true;
        }
    }
    
    private async Task LoadCoNumsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<string?>>>("api/customerorder/conums");
            if (response?.IsSuccess == true && response.Data != null) {
                CoNumItems = response.Data.Where(x => !string.IsNullOrEmpty(x)).Cast<string>().ToList();
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load CO numbers: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadItemsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<Item>>>("api/items");
            if (response?.IsSuccess == true && response.Data != null) {
                Items = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load items: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadItemsLocationsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<ItemLocation>>>("api/itemlocations");
            if (response?.IsSuccess == true && response.Data != null) {
                ItemLocationItems = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load item locations: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadUnitOfMeasuresAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<UnitOfMeasure>>>("api/units");
            if (response?.IsSuccess == true && response.Data != null) {
                UnitOfMeasureItems = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load unit of measures: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadData() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<CoItem>>>(relativePath);
            if (response?.IsSuccess == true) {
                if (response.Data != null) {
                    CoItems = response.Data;
                }
            }
            else if (response == null) {
                return;
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = response?.Message ?? "Failed to load data",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            if (!ex.Message.Contains("401") && !ex.Message.Contains("Authentication") && !ex.Message.Contains("invalid start of a value")) {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load data: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
    }

    private Task<QueryData<CoItem>> OnQueryAsync(QueryPageOptions options) {
        var items = CoItems.Where(options.ToFilterFunc<CoItem>());
        if (!string.IsNullOrEmpty(options.SearchText)) {
            items = CoItems.Where(i =>
                !string.IsNullOrEmpty(i.CoNum) && i.CoNum.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Item) && i.Item.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                Enum.TryParse<EnumStatus>(options.SearchText, true, out var status) && i.Status == status
            );
        }
        // 处理PoNum筛选
        if (!string.IsNullOrEmpty(SearchModel.CoNum)) {
            items = items.Where(i => i.CoNum == SearchModel.CoNum);
        }

        // 处理Item筛选
        if (!string.IsNullOrEmpty(SearchModel.Item)) {
            items = items.Where(i => i.Item == SearchModel.Item);
        }

        // 处理Status筛选
        if (SearchModel.Status.HasValue) {
            items = items.Where(i => i.Status == SearchModel.Status.Value);
        }
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        var total = items.Count();
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<CoItem>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<CoItem> OnAddAsync() {
        return await Task.FromResult(new CoItem() {
            CoNum = CoNum ?? "CO?",
            CoLine = 1,
            DueDate = DateTime.Today.AddDays(30),
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(CoItem item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Added successfully",
                        Color = Color.Success
                    });
                    await LoadData();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Updated successfully",
                        Color = Color.Success
                    });
                    await LoadData();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Save failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }

        return false;
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<CoItem> items) {
        try {
            var itemList = items.ToList();
            if (!itemList.Any()) {
                await MessageService.Show(new MessageOption {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1) {
                response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
            }
            else {
                response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = "Deleted successfully",
                    Color = Color.Success
                });

                await LoadData();
                return true;
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                throw new Exception(error);
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Delete failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private Task OnResetSearchAsync(CoItem model) {
        model.CoNum = "";
        model.Item = "";
        model.Status = null;
        return Task.CompletedTask;
    }

}
