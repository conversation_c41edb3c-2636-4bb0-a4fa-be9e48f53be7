@page "/sales/customerorders"
@using HuaLingErpApp.Client.Pages.Sales.Components
@inject MessageService MessageService
@inject ApiService Api
@inject NavigationManager NavigationManager


<PageTitle>Customer Order Management</PageTitle>

<SecurePage RequiredRoles="Administrator,Manager" PageName="Customer Order Management">
    @if (isReady) {
        <Table TItem="CustomerOrder"
               IsPagination="true" PageItemsSource="new[] { 20, 25, 30 }"
               IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
               ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
               AddModalTitle="Add New" EditModalTitle="Edit"
               SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
               OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
               DoubleClickToEdit="true"
               ShowSkeleton="true">

            <EditTemplate>
                <CustomerOrderEditTemplate Model="context" Customers="CustomerItems"
                                           Currencies="CurrencyItems" TermsCodes="TermsCodeItems"
                                           TaxCodes="TaxCodeItems"/>
            </EditTemplate>
            <TableColumns>

                <TableColumn @bind-Field="@context.CoNum" Text=" " Sortable="false" Align="Alignment.Center" Width="10"
                             IsVisibleWhenAdd="false" IsRequiredWhenAdd="false" IsRequiredWhenEdit="false"
                             Required="false">
                    <Template Context="columnContext">
                        <Button
                            @onclick=@(() => NavigationManager.NavigateTo($"sales/customerorderlines/{columnContext.Value}"))
                            Color="Color.Primary" Size="Size.ExtraSmall">
                            <i class="fa fa-list"></i>
                        </Button>
                    </Template>

                </TableColumn>

                <TableColumn @bind-Field="@context.CoNum" Text="CO Number" Sortable="true" Searchable="true"
                             IsVisibleWhenAdd="false" IsRequiredWhenAdd="false" IsRequiredWhenEdit="false"
                             Required="false" Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.CustNum" Text="Customer" Sortable="true" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.Status" Text="Status" Sortable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.CurrCode" Required="true" Text="Currency" Sortable="true"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.TermsCode" Required="true" Text="Terms" Sortable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.Contact" Text="Contact" Sortable="true" Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.Phone" Text="Phone" Sortable="true" Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.Email" Text="Email" Sortable="true" Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.CoDate" Text="CO Date" Sortable="true" FormatString="yyyy-MM-dd"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.TaxCode" Text="Tax Code" Sortable="true" Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.TaxRate" Step="0.01" Text="Tax Rate" Sortable="true"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.ExchRate" FormatString="0.0000" Step="0.0001"
                             Text="Exchange Rate" Sortable="true"
                             Align="Alignment.Center"/>
                <TableColumn @bind-Field="@context.Addr" Text="Address" Sortable="true" Align="Alignment.Center"/>
            </TableColumns>
            <EmptyTemplate>
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                        <h5>No customer orders found</h5>
                        <p>No customer orders match your search criteria. Try adjusting your search or create a new
                            order.</p>
                    </div>
                </div>
            </EmptyTemplate>
        </Table>
    }
</SecurePage>

@code {
    private List<CustomerOrder> Items { get; set; } = new();
    private CustomerOrder SearchModel { get; set; } = new();

    private string relativePath = "api/customerorder";
    private bool isReady = false; // 确保初始值为 false

    // Customer and Currency related data
    private List<Customer> CustomerItems { get; set; } = new();
    private List<CurrencyCode> CurrencyItems { get; set; } = new();
    private List<TermsCode> TermsCodeItems { get; set; } = new();
    private List<TaxCode> TaxCodeItems { get; set; } = new();

    protected override async Task OnInitializedAsync() {
        // 检查认证状态，只有已认证才加载数据
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely() {
        try {
            await LoadCustomersAsync();
            await LoadCurrenciesAsync();
            await LoadTermsCodesAsync();
            await LoadTaxCodesAsync();
            await LoadData();
        }
        catch (Exception ex) {
            // 只有在认证状态下才显示错误消息
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadCurrenciesAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<CurrencyCode>>>("api/currencycode");
            if (response?.IsSuccess == true && response.Data != null) {
                CurrencyItems = response.Data;
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load currencies: {response?.Message ?? "Unknown error"}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load currencies: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadTermsCodesAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<TermsCode>>>("api/termscode");
            if (response?.IsSuccess == true && response.Data != null) {
                TermsCodeItems = response.Data;
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load TermsCode: {response?.Message ?? "Unknown error"}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load TermsCode: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadTaxCodesAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<TaxCode>>>("api/taxcode");
            if (response?.IsSuccess == true && response.Data != null) {
                TaxCodeItems = response.Data;
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load TaxCodes: {response?.Message ?? "Unknown error"}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load TaxCodes: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadCustomersAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<Customer>>>("api/customers");
            if (response?.IsSuccess == true && response.Data != null) {
                CustomerItems = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load customers: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadData() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<CustomerOrder>>>(relativePath);
            if (response?.IsSuccess == true) {
                if (response.Data != null) {
                    Items = response.Data;
                }
            }
            else if (response == null) {
                // This might indicate an authentication error that was already handled
                // Don't show an error message as the user is likely being redirected
                return;
            }
            else {
                await MessageService.Show(new MessageOption {
                    Content = response?.Message ?? "Failed to load data",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            // Only show error if it's not an authentication-related issue
            if (!ex.Message.Contains("401") && !ex.Message.Contains("Authentication") && !ex.Message.Contains("invalid start of a value")) {
                await MessageService.Show(new MessageOption {
                    Content = $"Failed to load data: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
    }

    private Task<QueryData<CustomerOrder>> OnQueryAsync(QueryPageOptions options) {
        var items = Items.Where(options.ToFilterFunc<CustomerOrder>());
        if (!string.IsNullOrEmpty(options.SearchText)) {
            // Use Linq to process
            items = Items.Where(i =>
                !string.IsNullOrEmpty(i.CoNum) && i.CoNum.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.CustNum) && i.CustNum.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                // 处理 Status 字段的搜索逻辑
                Enum.TryParse<EnumStatus>(options.SearchText, true, out var status) && i.Status == status
            );
        }

        // Use Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<CustomerOrder>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<CustomerOrder> OnAddAsync() {
        return await Task.FromResult(new CustomerOrder() {
            CoNum = "CO?", // Default value
            CoDate = DateTime.Today,
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(CustomerOrder item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Added successfully",
                        Color = Color.Success
                    });
                    await LoadData();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Updated successfully",
                        Color = Color.Success
                    });
                    await LoadData();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Save failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }

        return false;
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<CustomerOrder> items) {
        try {
            var itemList = items.ToList();
            if (!itemList.Any()) {
                await MessageService.Show(new MessageOption {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1) {
                // Single delete
                response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
            }
            else {
                // Batch delete
                response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = "Deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadData();
                return true;
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                throw new Exception(error);
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Delete failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private Task OnResetSearchAsync(CustomerOrder model) {
        model.CoNum = "";
        model.CustNum = "";
        return Task.CompletedTask;
    }

}
