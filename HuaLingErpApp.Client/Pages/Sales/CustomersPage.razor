@page "/customer/customers"
@using HuaLingErpApp.Shared

<SecurePage RequiredRoles="Administrator,Manager" PageName="Customer Management">
@if (isReady) {
    <Table TItem="Customer"
           IsPagination="true" PageItemsSource="[20, 25, 30 ]"
           IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
           ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
           AddModalTitle="Add New Customer" EditModalTitle="Edit Customer"
           SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
           OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
           OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
           DoubleClickToEdit="true"
           ShowSkeleton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.CustNum" Text="Customer Number" Sortable="true" Searchable="true"
                         Align="Alignment.Center" Width="120">
            </TableColumn>
            <TableColumn @bind-Field="@context.Name" Text="Customer Name" Searchable="true"
                         Align="Alignment.Center" Width="200">
            </TableColumn>
            <TableColumn @bind-Field="@context.CurrCode" Text="Currency" Sortable="true"
                         Align="Alignment.Center" Width="80">
            </TableColumn>
            <TableColumn @bind-Field="@context.TermsCode" Text="Terms" Sortable="true"
                         Align="Alignment.Center" Width="80">
            </TableColumn>
            <TableColumn @bind-Field="@context.TaxCode" Text="Tax Code" Sortable="true"
                         Align="Alignment.Center" Width="80">
            </TableColumn>
            <TableColumn @bind-Field="@context.Contact" Text="Contact"
                         Align="Alignment.Center" Width="150">
            </TableColumn>
            <TableColumn @bind-Field="@context.Phone" Text="Phone"
                         Align="Alignment.Center" Width="120">
            </TableColumn>
            <TableColumn @bind-Field="@context.Email" Text="Email"
                         Align="Alignment.Center" Width="180">
            </TableColumn>
            <TableColumn @bind-Field="@context.Addr" Text="Address"
                         Align="Alignment.Center" Width="200">
            </TableColumn>
        </TableColumns>
        <EmptyTemplate>
            <div class="text-center py-5">
                <div class="text-muted">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <h5>No customers found</h5>
                    <p>No customers match your search criteria. Try adjusting your search or add a new customer.</p>
                </div>
            </div>
        </EmptyTemplate>
    </Table>
}
</SecurePage>

@code {
    private List<Customer> CustomerItems { get; set; } = new();

    private Customer SearchModel { get; set; } = new();

    private string relativePath = "api/customers";
    private bool isReady;
    
    protected override async Task OnParametersSetAsync() {
        // 检查认证状态，只有已认证才加载数据
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }
    
    private async Task LoadDataSafely() {
        try {
            await LoadCustomersAsync();
        }
        catch (Exception ex) {
            // 只有在认证状态下才显示错误消息
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            isReady = true;
            StateHasChanged();
        }
    }
    
    private async Task LoadCustomersAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<Customer>>>(relativePath);
            if (response?.IsSuccess == true && response.Data != null) {
                CustomerItems = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load customers: {ex.Message}",
                Color = Color.Danger
            });
        }
    }
    
    private Task<QueryData<Customer>> OnQueryAsync(QueryPageOptions options) {
        var items = CustomerItems.Where(options.ToFilterFunc<Customer>());
        if (!string.IsNullOrEmpty(options.SearchText)) {
            // Use Linq to process
            items = CustomerItems.Where(i =>
                !string.IsNullOrEmpty(i.CustNum) && i.CustNum.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Name) && i.Name.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Contact) && i.Contact.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Phone) && i.Phone.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Email) && i.Email.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.CurrCode) && i.CurrCode.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.TermsCode) && i.TermsCode.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.TaxCode) && i.TaxCode.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Addr) && i.Addr.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
            );
        }

        // Use Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<Customer>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<Customer> OnAddAsync() {
        return await Task.FromResult(new Customer() {
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(Customer item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Customer added successfully",
                        Color = Color.Success
                    });
                    await LoadCustomersAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Customer updated successfully",
                        Color = Color.Success
                    });
                    await LoadCustomersAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Save failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }

        return false;
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<Customer> items) {
        try {
            var itemList = items.ToList();
            if (!itemList.Any()) {
                await MessageService.Show(new MessageOption {
                    Content = "Please select customers to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1) {
                // Single delete
                response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
            }
            else {
                // Batch delete
                response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = "Customer(s) deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadCustomersAsync();
                return true;
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                throw new Exception(error);
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Delete failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private Task OnResetSearchAsync(Customer model) {
        model.CustNum = "";
        model.Name = "";
        return Task.CompletedTask;
    }
}
