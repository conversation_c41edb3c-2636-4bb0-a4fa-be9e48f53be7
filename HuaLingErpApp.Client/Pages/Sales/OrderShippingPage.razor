@page "/sales/ordershipping"
@page "/sales/ordershipping/{CoNum}"
@using Console = System.Console
@inject IJSRuntime JSRuntime
@inject ApiService Api
@inject NavigationManager NavigationManager
@inject MessageService MessageService

<SecurePage RequiredRoles="Administrator,Manager" PageName="Order Shipping">
    <PageTitle>Order Shipping</PageTitle>
    <!-- Alert Messages -->
    @if (!string.IsNullOrEmpty(_alertMessage)) {
        <div class="alert-custom @_alertClass">
            @_alertMessage
            <button type="button" class="btn-close float-end" @onclick="ClearAlert"></button>
        </div>
    }

    <div class="page-container">
        @if (_isReady) {
            <!-- Toolbar -->
            <div class="toolbar-container">
                <div class="row g-3 align-items-center">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center">
                            <label class="form-label me-2 mb-0" style="min-width: 90px;">CO Number:</label>
                            <Select TValue="string" Items="CoNumSelectedItems"
                                    OnSelectedItemChanged="OnSelectedCoNumChanged"
                                    @bind-Value="Model.CoNum" ShowSearch="true"
                                    IsPopover="true"
                                    PlaceHolder="Select CoNum..."
                                    class="flex-fill">
                                <Options>
                                    <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                                  Active="true"></SelectOption>
                                </Options>
                            </Select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <label class="form-label me-2 mb-0" style="min-width: 50px;">Date:</label>
                            <DateTimePicker @bind-Value="@_selectedDateTime" ShowIcon="false" IsEditable="true"
                                            DateFormat="yyyy-MM-dd" class="flex-fill"/>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="d-flex justify-content-end align-items-center gap-2">
                            <div class="text-muted small me-3">
                                Selected: @_selectedRows.Count | Edited: @_editedRows.Count
                            </div>
                            <button class="btn btn-outline-primary" @onclick="RefreshTable">
                                <i class="fas fa-sync-alt me-1"></i>Refresh
                            </button>
                            <button class="btn btn-success" @onclick="OnShipButtonClick"
                                    disabled="@_isShipButtonDisabled">
                                <i class="fas fa-shipping-fast me-1"></i>Ship (@_selectedRows.Count)
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Table Container -->
            <div class="table-container">
                <table class="table table-striped table-bordered table-hover custom-table">
                    <thead>
                    <tr>
                        <th style="width: 50px;">
                            <input type="checkbox" @onchange="OnSelectAllChanged" checked="@IsAllSelected"/>
                        </th>
                        <th style="width: 100px;">CO Number</th>
                        <th style="width: 80px;">CO Line</th>
                        <th style="width: 120px;">Item</th>
                        <th style="width: 100px;">Location</th>
                        <th style="width: 60px;">U/M</th>
                        <th style="width: 100px;">Qty Ordered</th>
                        <th style="width: 100px;">Qty Shipped</th>
                        <th style="width: 120px;">Qty Shipping</th>
                        <th style="width: 120px;">Reason Code</th>
                        <th style="width: 150px;">Info</th>
                    </tr>
                    </thead>
                    <tbody>
                    @if (FilteredItems.Any()) {
                        @foreach (var item in PaginatedItems) {
                            var rowKey = GetRowKey(item);
                            var isSelected = _selectedRows.Contains(rowKey);
                            var isEdited = _editedRows.Contains(rowKey);
                            var isEditing = _editingRows.ContainsKey(rowKey) && _editingRows[rowKey];
                            <tr class="@(isSelected ? "edited-row" : "")" @ondblclick="@(() => OnRowDoubleClick(item))"
                                @onclick="@(() => OnRowClick(item))" style="cursor: pointer;">
                                <td @onclick:stopPropagation="true">
                                    <input type="checkbox" @onchange="@((e) => OnRowCheckboxChanged(item, e))"
                                           checked="@isSelected"/>
                                </td>
                                <td>@item.CoNum</td>
                                <td>@item.CoLine</td>
                                <td>@item.Item</td>
                                <td>@item.Location</td>
                                <td>@item.UnitOfMeasure</td>
                                <td>@item.QtyOrdered.ToString("F2")</td>
                                <td>@item.QtyShipped.ToString("F2")</td>
                                <td @onclick:stopPropagation="true">
                                    @if (isEditing) {
                                        <input type="number" class="form-control form-control-sm"
                                               @bind="item.QtyShipping"
                                               @oninput="@(async (e) => await OnQtyShippingInput(item, e))"
                                               @onblur="@(async () => await OnQtyShippingChanged(item))"
                                               step="0.01"
                                               style="width: 100px;"/>
                                    }
                                    else {
                                        <span>@item.QtyShipping.ToString("F2")</span>
                                    }
                                </td>
                                <td @onclick:stopPropagation="true">
                                    @if (isEditing && item.QtyShipping < 0) {
                                        <Select TValue="string" @bind-Value="@item.ReasonCode"
                                                Items="ReasonCodeSelectedItems"
                                                OnSelectedItemChanged="@(async (selectedItem) => await OnReasonCodeChanged(item, selectedItem))"
                                                ShowSearch="true"
                                                IsPopover="true"
                                                PlaceHolder="Select ReasonCode..."
                                                IsClearable="true">
                                            <Options>
                                                <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                                              Active="true"></SelectOption>
                                            </Options>
                                        </Select>
                                    }
                                    else if (item.QtyShipping < 0) {
                                        <span>@item.ReasonCode</span>
                                    }
                                    else {
                                        <span class="text-muted">N/A</span>
                                    }
                                </td>
                                <td>@item.Info</td>
                            </tr>
                        }
                    }
                    else {
                        <tr>
                            <td colspan="11" class="text-center text-muted py-4">
                                @if (string.IsNullOrEmpty(Model.CoNum)) {
                                    <span>Please select a CO Number to view items</span>
                                }
                                else {
                                    <span>No items found for CO @Model.CoNum</span>
                                }
                            </td>
                        </tr>
                    }
                    </tbody>
                </table>

                <!-- Pagination -->
                @if (FilteredItems.Any()) {
                    <div class="pagination-container">
                        <div class="d-flex justify-content-between align-items-center w-100">
                            <div class="d-flex align-items-center">
                            <span class="me-3 small text-muted">
                                Showing @Math.Min((_currentPage - 1) * _pageSize + 1, TotalItems) to @Math.Min(_currentPage * _pageSize, TotalItems) of @TotalItems items
                            </span>
                                <div class="d-flex align-items-center">
                                    <span class="me-2 small text-muted">Size:</span>
                                    <Select TValue="int" @bind-Value="_pageSize"
                                            Items="PageSizeItems"
                                            OnSelectedItemChanged="OnPageSizeSelectedChanged"
                                            ShowSearch="false"
                                            IsPopover="false"
                                            style="width: 80px;">
                                    </Select>
                                </div>
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0">
                                    <li class="page-item @(_currentPage == 1 ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => GoToPage(1)"
                                                disabled="@(_currentPage == 1)">First
                                        </button>
                                    </li>
                                    <li class="page-item @(_currentPage == 1 ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => GoToPage(_currentPage - 1)"
                                                disabled="@(_currentPage == 1)">Previous
                                        </button>
                                    </li>

                                    @for (int i = Math.Max(1, _currentPage - 2); i <= Math.Min(TotalPages, _currentPage + 2); i++) {
                                        var pageNum = i;
                                        <li class="page-item @(_currentPage == pageNum ? "active" : "")">
                                            <button class="page-link"
                                                    @onclick="() => GoToPage(pageNum)">@pageNum</button>
                                        </li>
                                    }

                                    <li class="page-item @(_currentPage == TotalPages ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => GoToPage(_currentPage + 1)"
                                                disabled="@(_currentPage == TotalPages)">Next
                                        </button>
                                    </li>
                                    <li class="page-item @(_currentPage == TotalPages ? "disabled" : "")">
                                        <button class="page-link" @onclick="() => GoToPage(TotalPages)"
                                                disabled="@(_currentPage == TotalPages)">Last
                                        </button>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                }
            </div>

            <!-- Inventory Information -->
            @if (_showInventoryTable && ItemInventories != null) {
                <div class="inventory-container">
                    <h6>Inventory Information</h6>
                    @if (ItemInventories.Any()) {
                        <table class="table table-striped table-bordered table-sm">
                            <thead>
                            <tr>
                                <th>Item</th>
                                <th>Description</th>
                                <th>Location</th>
                                <th>Qty On Hand</th>
                            </tr>
                            </thead>
                            <tbody>
                            @foreach (var inventory in ItemInventories) {
                                <tr>
                                    <td>@inventory.Item?.Id</td>
                                    <td>@inventory.Item?.Description</td>
                                    <td>@inventory.Location</td>
                                    <td>@inventory.QuantityOnHand.ToString("F2")</td>
                                </tr>
                            }
                            </tbody>
                        </table>
                    }
                    else {
                        <div class="alert alert-warning py-2 my-2">No inventory information found</div>
                    }
                </div>
            }
        }
    </div>

    <!-- Result Modal -->
    @if (_showResultModal) {
        <div class="modal-backdrop-custom" @onclick="CloseResultModal"></div>
        <div class="modal-custom">
            <div class="modal-header p-3 border-bottom">
                <h5 class="modal-title">Processing Results</h5>
            </div>
            <div class="modal-body p-3">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="card border-success">
                            <div class="card-body py-2">
                                <h3 class="text-success mb-1">@_successCount</h3>
                                <small class="text-muted">Successful</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="card border-danger">
                            <div class="card-body py-2">
                                <h3 class="text-danger mb-1">@_failureCount</h3>
                                <small class="text-muted">Failed</small>
                            </div>
                        </div>
                    </div>
                </div>
                @if (!string.IsNullOrEmpty(_resultMessage)) {
                    <div class="mt-3">
                        <div class="alert alert-info py-2 mb-0">
                            @_resultMessage
                        </div>
                    </div>
                }
            </div>
            <div class="modal-footer p-3 border-top">
                <button type="button" class="btn btn-primary btn-sm" @onclick="CloseResultModal">OK</button>
            </div>
        </div>
    }
</SecurePage>

@code {
    [Parameter] public string? CoNum { get; set; }

    // Core data
    private bool _isReady;
    private bool _isShipButtonDisabled = false;
    public OrderShippingDto Model { get; set; } = new();
    private List<OrderShippingDto> CoShippingItems { get; set; } = [];
    private List<CoItem> CoLineItems { get; set; } = [];
    private List<string> CoNumItems { get; set; } = [];
    private List<ReasonCode> ReasonCodes { get; set; } = [];
    private List<ItemInventoryDto>? ItemInventories = [];
    private bool _showInventoryTable;
    private DateTime _selectedDateTime = DateTime.Now;

    // Row selection management
    private HashSet<string> _editedRows = new();
    private HashSet<string> _selectedRows = new();
    private Dictionary<string, bool> _editingRows = new();

    // Pagination
    private int _currentPage = 1;
    private int _pageSize = 10;

    // Alert system
    private string _alertMessage = "";
    private string _alertClass = "";

    // Result modal
    private bool _showResultModal = false;
    private int _successCount = 0;
    private int _failureCount = 0;
    private string _resultMessage = "";

    // Computed properties
    private IEnumerable<OrderShippingDto> FilteredItems {
        get { return CoShippingItems.AsEnumerable(); }
    }

    private bool IsAllSelected => CoShippingItems.Any() && CoShippingItems.All(item => _selectedRows.Contains(GetRowKey(item)));

    private IEnumerable<SelectedItem> ReasonCodeSelectedItems => ReasonCodes
        .Select(c => new SelectedItem(c.ReasonCodeValue, $"{c.ReasonCodeValue}"));

    private IEnumerable<SelectedItem> CoNumSelectedItems => CoNumItems
        .Select(p => new SelectedItem(p, $"{p}"));

    private IEnumerable<SelectedItem> PageSizeItems => new List<SelectedItem> {
        new("5", "5"),
        new("10", "10"),
        new("15", "15"),
        new("20", "20"),
        new("25", "25"),
        new("30", "30")
    };

    private IEnumerable<OrderShippingDto> PaginatedItems {
        get {
            return FilteredItems
                .Skip((_currentPage - 1) * _pageSize)
                .Take(_pageSize);
        }
    }

    private int TotalItems => FilteredItems.Count();
    private int TotalPages => (int)Math.Ceiling((double)TotalItems / _pageSize);

    protected override async Task OnInitializedAsync() {
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadCoNumsAsync();
            await LoadReasonCodesAsync();
            await LoadCoShippingItemsAsync();
            _isReady = true;
        }
    }

    protected override async Task OnParametersSetAsync() {
        if (!string.IsNullOrEmpty(CoNum) && CoNum != Model.CoNum) {
            Model.CoNum = CoNum;
            await OnSelectedCoNumChanged(new SelectedItem(CoNum, CoNum));
        }
    }

    private string GetRowKey(OrderShippingDto item) => $"{item.CoNum}-{item.CoLine}";

    // Alert methods
    private void ShowAlert(string message, string type = "success") {
        _alertMessage = message;
        _alertClass = $"alert-{type}";
        StateHasChanged();
    }

    private void ClearAlert() {
        _alertMessage = "";
        _alertClass = "";
        StateHasChanged();
    }

    // Selection methods
    private void OnSelectAllChanged(ChangeEventArgs e) {
        var isChecked = (bool)(e.Value ?? false);

        if (isChecked) {
            foreach (var item in CoShippingItems) {
                _selectedRows.Add(GetRowKey(item));
            }
        }
        else {
            _selectedRows.Clear();
        }

        StateHasChanged();
    }

    private void OnRowCheckboxChanged(OrderShippingDto item, ChangeEventArgs e) {
        var isChecked = (bool)(e.Value ?? false);
        var rowKey = GetRowKey(item);

        if (isChecked) {
            _selectedRows.Add(rowKey);
        }
        else {
            _selectedRows.Remove(rowKey);
        }

        StateHasChanged();
    }

    private async Task RefreshTable() {
        // Clear inventory information when refreshing
        ItemInventories?.Clear();
        _showInventoryTable = false;

        if (!string.IsNullOrEmpty(Model.CoNum)) {
            await LoadCoLinesAsync(Model.CoNum);
            if (CoLineItems.Count > 0) {
                await LoadCoShippingItemsAsync();
                _isShipButtonDisabled = false;
            }
        }

        ShowAlert("Table refreshed successfully", "success");
    }

    // Pagination methods
    private void GoToPage(int page) {
        if (page >= 1 && page <= TotalPages) {
            _currentPage = page;
            StateHasChanged();
        }
    }

    private void OnPageSizeChanged() {
        _currentPage = 1; // Reset to first page
        StateHasChanged();
    }

    private async Task OnPageSizeSelectedChanged(SelectedItem selectedItem) {
        if (int.TryParse(selectedItem.Value, out var newPageSize)) {
            _pageSize = newPageSize;
            _currentPage = 1; // Reset to first page
            StateHasChanged();
        }

        await Task.CompletedTask;
    }

    // Result modal methods
    private void ShowResultModal(int successCount, int failureCount, string message = "") {
        _successCount = successCount;
        _failureCount = failureCount;
        _resultMessage = message;
        _showResultModal = true;
        StateHasChanged();
    }

    private void CloseResultModal() {
        _showResultModal = false;
        StateHasChanged();
    }

    private async Task LoadCoShippingItemsAsync() {
        try {
            CoShippingItems.Clear();
            _editedRows.Clear(); // Clear edited rows when loading new data
            _selectedRows.Clear(); // Clear selected rows when loading new data
            _editingRows.Clear(); // Clear editing state when loading new data

            if (CoLineItems.Count > 0) {
                var dtos = CoLineItems.Select(item => new OrderShippingDto {
                    CoNum = item.CoNum ?? string.Empty,
                    CoLine = item.CoLine,
                    Item = item.Item,
                    UnitOfMeasure = item.UnitOfMeasure,
                    Location = item.Location,
                    QtyOrdered = item.QtyOrdered,
                    QtyShipped = item.QtyShipped,
                    QtyShipping = 0,
                    ReasonCode = null,
                    Info = null
                }).ToList();

                CoShippingItems.AddRange(dtos);
            }

            StateHasChanged();
        }
        catch (Exception ex) {
            ShowAlert($"Failed to load data: {ex.Message}", "danger");
        }
    }

    private async Task LoadCoNumsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<string>>>("api/customerorder/conums");
            if (response?.IsSuccess == true && response.Data != null) {
                CoNumItems = response.Data;
            }
            else {
                ShowAlert($"Failed to load CO Numbers: {response?.Message ?? "Unknown error"}", "danger");
            }
        }
        catch (Exception ex) {
            ShowAlert($"Failed to load CO Numbers: {ex.Message}", "danger");
        }
    }

    private async Task LoadCoLinesAsync(string coNum) {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<CoItem>>>($"api/customerorderlines/byconum/{coNum}");
            if (response?.IsSuccess == true) {
                CoLineItems = response.Data ?? new List<CoItem>();
                if (!CoLineItems.Any()) {
                    ShowAlert("No CO lines found for the selected CO number", "warning");
                }
            }
            else {
                CoLineItems = new List<CoItem>();
                ShowAlert(response?.Message ?? "Failed to load CO lines", "danger");
            }
        }
        catch (Exception ex) {
            CoLineItems = new List<CoItem>();
            ShowAlert("No CO lines found for the selected CO number", "danger");
        }
    }

    private async Task LoadReasonCodesAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<ReasonCode>>>("api/reasoncode");
            if (response?.IsSuccess == true) {
                ReasonCodes = response.Data ?? [];
            }
            else {
                ShowAlert(response?.Message ?? "Failed to load reason codes", "danger");
            }
        }
        catch (Exception ex) {
            ShowAlert($"Failed to load reason codes: {ex.Message}", "danger");
        }
    }

    private async Task OnSelectedCoNumChanged(SelectedItem selectedItem) {
        // Re-enable ship button
        _isShipButtonDisabled = false;

        // Clear current items and all states
        CoShippingItems.Clear();
        _editedRows.Clear();
        _selectedRows.Clear();
        _editingRows.Clear();
        _currentPage = 1; // Reset pagination
        ClearAlert(); // Clear any existing alerts

        // Clear inventory information
        ItemInventories?.Clear();
        _showInventoryTable = false;

        StateHasChanged();

        // Load corresponding lines if CO number is selected
        if (!string.IsNullOrEmpty(Model.CoNum)) {
            await LoadCoLinesAsync(Model.CoNum);
            if (CoLineItems.Count > 0) {
                await LoadCoShippingItemsAsync();
            }
        }
    }

    private async Task OnRowClick(OrderShippingDto item) {
        if (string.IsNullOrEmpty(item.Item)) {
            ShowAlert("Please select a valid item", "warning");
            return;
        }

        try {
            var response = await Api.GetAsync<HttpResponseModel<List<ItemInventoryDto>>>($"api/items/{item.Item}/inventory");
            if (response?.IsSuccess == true && response.Data != null) {
                ItemInventories = response.Data;
            }
            else {
                ShowAlert(response?.Message ?? "No inventory information found for the selected item", "warning");
            }
        }
        catch (Exception ex) {
            ShowAlert($"Failed to load inventory information: {ex.Message}", "danger");
        }
        finally {
            _showInventoryTable = true;
            StateHasChanged();
        }
    }

    private void OnRowDoubleClick(OrderShippingDto item) {
        var rowKey = GetRowKey(item);

        // Start editing this row
        _editingRows[rowKey] = true;

        // Also select this row
        _selectedRows.Add(rowKey);

        StateHasChanged();
    }

    private async Task OnShipButtonClick() {
        if (!_selectedRows.Any()) {
            ShowAlert("Please select items to ship", "warning");
            return;
        }

        // Get selected items
        var selectedItems = CoShippingItems
            .Where(item => _selectedRows.Contains(GetRowKey(item)))
            .ToList();

        // Validate selected items
        var invalidItems = selectedItems.Where(x => x.QtyShipping == 0).ToList();
        if (invalidItems.Any()) {
            ShowAlert("Please enter shipping quantity for all selected items", "warning");
            return;
        }

        var negativeItemsWithoutReason = selectedItems.Where(x => x.QtyShipping < 0 && string.IsNullOrEmpty(x.ReasonCode)).ToList();
        if (negativeItemsWithoutReason.Any()) {
            ShowAlert("Reason code is required for negative shipping quantities", "warning");
            return;
        }

        await ShipAndFillInfo(selectedItems);
    }

    private async Task ShipAndFillInfo(List<OrderShippingDto> list) {
        try {
            // Add transaction date to each item
            foreach (var item in list) {
                item.TransDate = _selectedDateTime;
            }

            // Call stored procedure API
            var response = await Api.PostAsync("api/ordershipping/process-sp", list);

            if (response.IsSuccessStatusCode) {
                var content = await response.Content.ReadAsStringAsync();
                var result = System.Text.Json.JsonSerializer.Deserialize<HttpResponseModel<List<OrderShippingDto>>>(content);

                if (result?.IsSuccess == true && result.Data != null) {
                    int successCount = 0;
                    int failureCount = 0;

                    // Update table data and count results
                    foreach (var updatedItem in result.Data) {
                        var originalItem = CoShippingItems.FirstOrDefault(x => x.CoNum == updatedItem.CoNum && x.CoLine == updatedItem.CoLine);
                        if (originalItem != null) {
                            originalItem.Info = updatedItem.Info;

                            // Count success/failure based on Info content
                            if (!string.IsNullOrEmpty(updatedItem.Info) && !updatedItem.Info.Contains("Error") && !updatedItem.Info.Contains("Failed")) {
                                successCount++;
                            }
                            else {
                                failureCount++;
                            }
                        }
                    }

                    // Clear selected rows and editing state after successful processing
                    _selectedRows.Clear();
                    _editingRows.Clear();
                    _isShipButtonDisabled = true;

                    StateHasChanged();

                    // Show detailed result modal
                    ShowResultModal(successCount, failureCount, $"Processing completed for {list.Count} items");
                }
                else {
                    ShowAlert(result?.Message ?? "Failed to process items", "danger");
                }
            }
            else {
                var errorContent = await response.Content.ReadAsStringAsync();
                var errorResponse = System.Text.Json.JsonSerializer.Deserialize<HttpResponseModel<object>>(errorContent);

                ShowAlert(errorResponse?.Message ?? "Failed to execute stored procedure", "danger");
            }
        }
        catch (Exception ex) {
            ShowAlert($"Error processing shipping: {ex.Message}", "danger");
        }
    }

    private async Task OnQtyShippingInput(OrderShippingDto item, ChangeEventArgs e) {
        if (decimal.TryParse(e.Value?.ToString(), out var qty)) {
            item.QtyShipping = qty;

            // Clear reason code if quantity becomes non-negative
            if (qty >= 0) {
                item.ReasonCode = null;
            }

            // Mark row as edited
            var rowKey = GetRowKey(item);
            if (qty != 0) // Only mark as edited if quantity is not zero
            {
                _editedRows.Add(rowKey);
            }
            else {
                _editedRows.Remove(rowKey); // Remove from edited if quantity is reset to zero
            }

            // Update original item in the list
            var originalItem = CoShippingItems.FirstOrDefault(x =>
                x.CoNum == item.CoNum && x.CoLine == item.CoLine);
            if (originalItem != null) {
                originalItem.QtyShipping = qty;
                if (qty >= 0) {
                    originalItem.ReasonCode = null;
                }
            }

            StateHasChanged();
        }

        await Task.CompletedTask;
    }

    private async Task OnQtyShippingChanged(OrderShippingDto item) {
        var rowKey = GetRowKey(item);
        // Stop editing when user finishes editing (blur event)
        _editingRows[rowKey] = false;

        Console.WriteLine($"QtyShipping changed: {item.CoNum}-{item.CoLine} = {item.QtyShipping}");
        StateHasChanged();
        await Task.CompletedTask;
    }

    private async Task OnReasonCodeChanged(OrderShippingDto item, SelectedItem selectedItem) {
        item.ReasonCode = selectedItem.Value;

        // Mark row as edited if reason code is selected
        var rowKey = GetRowKey(item);
        if (!string.IsNullOrEmpty(selectedItem.Value)) {
            _editedRows.Add(rowKey);
        }

        // Update original item in the list
        var originalItem = CoShippingItems.FirstOrDefault(x =>
            x.CoNum == item.CoNum && x.CoLine == item.CoLine);
        if (originalItem != null) {
            originalItem.ReasonCode = selectedItem.Value;
        }

        Console.WriteLine($"ReasonCode changed: {item.CoNum}-{item.CoLine} = {item.ReasonCode}");
        StateHasChanged();
        await Task.CompletedTask;
    }

}
