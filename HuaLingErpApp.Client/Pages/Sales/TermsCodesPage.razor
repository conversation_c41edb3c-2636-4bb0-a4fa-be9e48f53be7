@page "/sales/termscodes"
@using HuaLingErpApp.Shared

<SecurePage RequiredRoles="Administrator,Manager" PageName="Terms Code Management">
    @if (isReady) {
        <Table TItem="TermsCode"
               IsPagination="true" PageItemsSource="[20, 25, 30]"
               IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
               ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
               AddModalTitle="Add New Terms Code" EditModalTitle="Edit Terms Code"
               SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
               OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
               DoubleClickToEdit="true"
               ShowSkeleton="true">
            <TableColumns>
                <TableColumn @bind-Field="@context.Id" Text="Terms Code" Sortable="true" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.Description" Text="Description" Sortable="true" Searchable="true"
                             Align="Alignment.Left">
                </TableColumn>
            </TableColumns>
            <EmptyTemplate>
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-file-contract fa-3x mb-3"></i>
                        <h5>No terms codes found</h5>
                        <p>No terms codes match your search criteria. Try adjusting your search or add a new terms
                            code.</p>
                    </div>
                </div>
            </EmptyTemplate>
        </Table>
    }
</SecurePage>

@code {
    private List<TermsCode> TermsCodeItems { get; set; } = new();

    private TermsCode SearchModel { get; set; } = new();

    private string relativePath = "api/termscode";
    private bool isReady;

    protected override async Task OnParametersSetAsync() {
        // Check authentication state, only load data if authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely() {
        try {
            await LoadTermsCodesAsync();
        }
        catch (Exception ex) {
            // Only show error message if authenticated
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadTermsCodesAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<TermsCode>>>(relativePath);
            if (response?.IsSuccess == true && response.Data != null) {
                TermsCodeItems = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load terms codes: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private Task<QueryData<TermsCode>> OnQueryAsync(QueryPageOptions options) {
        var items = TermsCodeItems.Where(options.ToFilterFunc<TermsCode>());
        if (!string.IsNullOrEmpty(options.SearchText)) {
            // Use Linq to process
            items = TermsCodeItems.Where(i =>
                !string.IsNullOrEmpty(i.Id) && i.Id.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Description) && i.Description.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
            );
        }

        // Use Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<TermsCode>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<TermsCode> OnAddAsync() {
        return await Task.FromResult(new TermsCode() {
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(TermsCode item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Terms code added successfully",
                        Color = Color.Success
                    });
                    await LoadTermsCodesAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Terms code updated successfully",
                        Color = Color.Success
                    });
                    await LoadTermsCodesAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Operation failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<TermsCode> items) {
        try {
            var itemList = items.ToList();
            if (!itemList.Any()) {
                await MessageService.Show(new MessageOption {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1) {
                // Single delete
                response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
            }
            else {
                // Batch delete
                response = await Api.PostAsync($"{relativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = "Terms code(s) deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadTermsCodesAsync();
                return true;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Delete operation failed: {ex.Message}",
                Color = Color.Danger
            });
        }

        return false;
    }

    private Task OnResetSearchAsync(TermsCode searchModel) {
        searchModel.Id = string.Empty;
        searchModel.Description = string.Empty;
        return Task.CompletedTask;
    }

}
