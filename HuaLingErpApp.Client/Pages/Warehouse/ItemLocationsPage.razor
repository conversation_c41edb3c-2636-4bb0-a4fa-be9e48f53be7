@page "/warehouse/itemlocations"
@using System.Text.Json
@using System.Net.Http.Headers
@using HuaLingErpApp.Shared.Models

<SecurePage RequiredRoles="Administrator,Manager" PageName="Item Location Management">
    @if (isReady) {
        <Table @ref="table" TItem="ItemLocation"
               IsPagination="true" PageItemsSource="[20, 25, 30]"
               IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
               ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
               AddModalTitle="Add New Item Location" EditModalTitle="Edit Item Location"
               SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
               OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
               DoubleClickToEdit="true"
               ShowSkeleton="true">
            <TableToolbarTemplate>
                <TableToolbarButton TItem="ItemLocation" Color="Color.Primary" Icon="fa-solid fa-file-import"
                                    Text="Import" OnClick="() => ShowImportDialog(ImportMode.Import)"/>
                <TableToolbarButton TItem="ItemLocation" Color="Color.Warning" Icon="fa-solid fa-file-pen" Text="Update"
                                    OnClick="() => ShowImportDialog(ImportMode.Update)"/>
            </TableToolbarTemplate>
            <TableColumns>
                <TableColumn @bind-Field="@context.Item" Text="Item" Sortable="true" Searchable="true" Required="true"
                             Align="Alignment.Center">
                    <EditTemplate Context="columnContext">
                        <div class="col-12 col-sm-6 col-md-6">
                            <Select TValue="string" ShowRequired="true" @bind-Value="@columnContext.Item"
                                    Items="ItemItems"
                                    ShowSearch="true"
                                    IsPopover="true"
                                    PlaceHolder="Select Item..."
                                    DisplayText="Item">
                                <Options>
                                    <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                                  Active="true"></SelectOption>
                                </Options>
                            </Select>
                        </div>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Location" Text="Location" Sortable="true" Searchable="true"
                             Required="true"
                             Align="Alignment.Center">
                    <EditTemplate Context="columnContext">
                        <div class="col-12 col-sm-6 col-md-6">
                            <Select TValue="string" ShowRequired="true" @bind-Value="@columnContext.Location"
                                    Items="LocationItems"
                                    ShowSearch="true"
                                    IsPopover="true"
                                    PlaceHolder="Select Location..."
                                    DisplayText="Location">
                                <Options>
                                    <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                                  Active="true"></SelectOption>
                                </Options>
                            </Select>
                        </div>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.Account" Text="Account" Sortable="true" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.QuantityOnHand" Text="Qty On Hand" Sortable="true"
                             Align="Alignment.Center" FormatString="F5">
                </TableColumn>
                <TableColumn @bind-Field="@context.Rank" Text="Rank" Sortable="true" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.UnitOfMeasure" Text="Unit Of Measure" Sortable="true"
                             Searchable="true"
                             Align="Alignment.Center">
                    <EditTemplate Context="columnContext">
                        <div class="col-12 col-sm-6 col-md-6">
                            <Select TValue="string" @bind-Value="@columnContext.UnitOfMeasure"
                                    Items="UnitOfMeasureItems"
                                    ShowSearch="true"
                                    IsPopover="true"
                                    PlaceHolder="Select UnitOfMeasure..."
                                    DisplayText="U/M">
                                <Options>
                                    <SelectOption Value="" Text="Please select..." IsDisabled="true"
                                                  Active="true"></SelectOption>
                                </Options>
                            </Select>
                        </div>
                    </EditTemplate>
                </TableColumn>
                <TableColumn @bind-Field="@context.AccountUnit1" Text="Account Unit 1" Sortable="true" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.AccountUnit2" Text="Account Unit 2" Sortable="true" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
            </TableColumns>
            <EmptyTemplate>
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-boxes fa-3x mb-3"></i>
                        <h5>No item locations found</h5>
                        <p>No item locations match your search criteria. Try adjusting your search or add a new item
                            location.</p>
                    </div>
                </div>
            </EmptyTemplate>
        </Table>
    }

    <!-- Import Dialog -->
    <Modal @ref="importModal" IsKeyboard="true" OnCloseAsync="@OnImportDialogClose">
        <ModalDialog Title="@GetImportDialogTitle()" Size="Size.Large" ShowCloseButton="true" ShowSaveButton="false">
            <BodyTemplate>
                <div class="mb-4">
                    <label class="form-label">Select Excel File (.xlsx, .xls)</label>
                    <DropUpload @ref="dropUpload"
                                OnChange="@OnDropUpload"
                                OnDelete="@OnFileDelete"
                                FooterText="File size is no more than 10MB, supports .xlsx, .xls "
                                IsDisabled="@isImporting"
                                IsMultiple="false"
                                ShowProgress="true"
                                ShowUploadFileList="true"
                                ShowFooter="true"
                                Accept=".xlsx, .xls">

                    </DropUpload>
                </div>

                <div class="mb-3">
                    <a href="api/itemlocations/template" target="_blank" class="btn btn-info btn-sm">
                        <i class="fas fa-download me-2"></i>Download Template
                    </a>
                    <small class="text-muted ms-2">(Optional - if you need a template)</small>
                </div>

                @if (validationResult != null) {
                    <div class="mb-4">
                        <h6>Validation Results</h6>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-primary">@validationResult.TotalRows</h5>
                                        <p class="card-text">Total Rows</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-success">@validationResult.ValidCount</h5>
                                        <p class="card-text">Valid Records</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h5 class="card-title text-danger">@validationResult.ErrorCount</h5>
                                        <p class="card-text">Errors</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        @if (validationResult.Errors.Any()) {
                            <div class="mt-3">
                                <h6>Error Details</h6>
                                <div style="max-height: 300px; overflow-y: auto;">
                                    <table class="table table-sm table-striped">
                                        <thead>
                                        <tr>
                                            <th>Row</th>
                                            <th>Field</th>
                                            <th>Error</th>
                                            <th>Value</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach (var error in validationResult.Errors) {
                                            <tr>
                                                <td>@error.RowNumber</td>
                                                <td>@error.Field</td>
                                                <td>@error.Message</td>
                                                <td>@error.Value</td>
                                            </tr>
                                        }
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        }
                    </div>
                }
            </BodyTemplate>
            <FooterTemplate>
                <Button Color="@GetButtonColor()" OnClick="@(() => ExecuteOperation(currentImportMode))"
                        IsDisabled="@(!CanImport)" IsLoading="@isImporting">
                    <i class="@GetButtonIcon() me-2"></i>@GetButtonText()
                </Button>
                @if (validationResult != null) {
                    @if (validationResult.ErrorCount > 0 && validationResult.ValidCount > 0) {
                        <div class="text-warning mt-2">
                            <small><i class="fas fa-exclamation-triangle me-1"></i>
                                Will import @validationResult.ValidCount valid record(s) and
                                skip @validationResult.ErrorCount invalid record(s)
                            </small>
                        </div>
                    }
                    else if (validationResult.ErrorCount > 0 && validationResult.ValidCount == 0) {
                        <div class="text-danger mt-2">
                            <small><i class="fas fa-times-circle me-1"></i>
                                No valid records found. Please fix errors in your file.
                            </small>
                        </div>
                    }
                    else if (validationResult.ErrorCount == 0) {
                        <div class="text-success mt-2">
                            <small><i class="fas fa-check-circle me-1"></i>
                                All @validationResult.ValidCount record(s) are valid and ready to import
                            </small>
                        </div>
                    }
                }
            </FooterTemplate>
        </ModalDialog>
    </Modal>
</SecurePage>

@code {
    private Table<ItemLocation>? table;
    private List<ItemLocation> ItemLocationItems { get; set; } = new();
    private List<Item> Items { get; set; } = new();
    private List<Location> Locations { get; set; } = new();
    private List<UnitOfMeasure> UnitOfMeasures { get; set; } = new();

    private ItemLocation SearchModel { get; set; } = new();

    private string relativePath = "api/itemlocations";
    private bool isReady;

    // Import related variables
    private Modal? importModal;
    private DropUpload? dropUpload;
    private IBrowserFile? selectedFile;
    private bool isImporting = false;
    private ValidationResult? validationResult;
    private bool CanImport => selectedFile != null && validationResult != null && validationResult.ValidCount > 0;

    // Import data models
    public class ValidationResult {
        public int TotalRows { get; set; }
        public int ValidCount { get; set; }
        public int ErrorCount { get; set; }
        public List<ValidationError> Errors { get; set; } = new();
    }

    public class ValidationError {
        public int RowNumber { get; set; }
        public string Field { get; set; } = "";
        public string Message { get; set; } = "";
        public string Value { get; set; } = "";
    }

    public class ImportResult {
        public int TotalRows { get; set; }
        public int SuccessCount { get; set; }
        public int SkippedCount { get; set; }
        public int ErrorCount { get; set; }
        public List<string> Messages { get; set; } = new();
    }

    // Convert to SelectedItem for dropdowns
    private IEnumerable<SelectedItem> ItemItems => Items
        .Select(c => new SelectedItem(c.Id, $"{c.Id} - {c.Description}"));

    private IEnumerable<SelectedItem> LocationItems => Locations
        .Select(c => new SelectedItem(c.Id, $"{c.Id} - {c.Description}"));

    private IEnumerable<SelectedItem> UnitOfMeasureItems => UnitOfMeasures
        .Select(c => new SelectedItem(c.Id, $"{c.Id}"));

    protected override async Task OnParametersSetAsync() {
        // Check authentication state, only load data if authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely() {
        try {
            await LoadItemsAsync();
            await LoadLocationsAsync();
            await LoadUnitOfMeasuresAsync();
            await LoadItemLocationsAsync();
        }
        catch (Exception ex) {
            // Only show error message if authenticated
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadItemsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<Item>>>("api/items");
            if (response?.IsSuccess == true && response.Data != null) {
                Items = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load items: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadLocationsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<Location>>>("api/locations");
            if (response?.IsSuccess == true && response.Data != null) {
                Locations = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load locations: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadUnitOfMeasuresAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<UnitOfMeasure>>>("api/units");
            if (response?.IsSuccess == true && response.Data != null) {
                UnitOfMeasures = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load unit of measures: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadItemLocationsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<ItemLocation>>>(relativePath);
            if (response?.IsSuccess == true && response.Data != null) {
                ItemLocationItems = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load item locations: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private Task<QueryData<ItemLocation>> OnQueryAsync(QueryPageOptions options) {
        var items = ItemLocationItems.Where(options.ToFilterFunc<ItemLocation>());
        if (!string.IsNullOrEmpty(options.SearchText)) {
            // Use Linq to process
            items = ItemLocationItems.Where(i =>
                !string.IsNullOrEmpty(i.Item) && i.Item.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Location) && i.Location.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Account) && i.Account.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.UnitOfMeasure) && i.UnitOfMeasure.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.AccountUnit1) && i.AccountUnit1.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.AccountUnit2) && i.AccountUnit2.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
            );
        }

        // Use Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<ItemLocation>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<ItemLocation> OnAddAsync() {
        return await Task.FromResult(new ItemLocation() {
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(ItemLocation item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Item location added successfully",
                        Color = Color.Success
                    });
                    await LoadItemLocationsAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Item location updated successfully",
                        Color = Color.Success
                    });
                    await LoadItemLocationsAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Operation failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<ItemLocation> items) {
        try {
            var itemList = items.ToList();
            if (!itemList.Any()) {
                await MessageService.Show(new MessageOption {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1) {
                // Single delete
                response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
            }
            else {
                // Batch delete
                response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = "Item location(s) deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadItemLocationsAsync();
                return true;
            }
            else {
                // 解析错误响应
                var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                await MessageService.Show(new MessageOption {
                    Content = errorMessage,
                    Color = Color.Danger
                });
                return false;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Delete operation failed: {ex.Message}",
                Color = Color.Danger
            });
        }

        return false;
    }

    private Task OnResetSearchAsync(ItemLocation searchModel) {
        searchModel.Item = null;
        searchModel.Location = null;
        searchModel.Account = null;
        searchModel.UnitOfMeasure = null;
        searchModel.AccountUnit1 = null;
        searchModel.AccountUnit2 = null;
        searchModel.Rank = null;

        return Task.CompletedTask;
    }

    // Import related methods
    private ImportMode currentImportMode = ImportMode.Import;

    private async Task ShowImportDialog(ImportMode mode) {
        currentImportMode = mode;
        // 重置所有导入相关状态
        await ResetImportState();
        await importModal!.Show();
    }

    /// <summary>
    /// 重置导入状态
    /// </summary>
    private Task ResetImportState() {
        selectedFile = null;
        validationResult = null;
        isImporting = false;

        // 清除DropUpload组件的文件列表
        dropUpload?.UploadFiles.Clear();

        return Task.CompletedTask;
    }

    /// <summary>
    /// 对话框关闭时的回调
    /// </summary>
    private async Task OnImportDialogClose() {
        // 对话框关闭时重置状态，确保下次打开时是干净的状态
        await ResetImportState();
    }

    /// <summary>
    /// 文件删除时的回调
    /// </summary>
    private Task<bool> OnFileDelete(UploadFile file) {
        // 用户删除文件时，清除选中的文件和验证结果
        selectedFile = null;
        validationResult = null;
        StateHasChanged();
        return Task.FromResult(true);
    }

    private async Task OnDropUpload(UploadFile file) {
        if (file?.File != null) {
            // 文件大小验证
            var maxFileSize = 10 * 1024 * 1024; // 10MB
            if (file.Size > maxFileSize) {
                await MessageService.Show(new MessageOption {
                    Content = "File size exceeds 10MB limit",
                    Color = Color.Danger
                });
                return;
            }

            // 文件格式验证
            var allowedExtensions = new[] { ".xlsx", ".xls" };
            var fileExtension = Path.GetExtension(file.File.Name).ToLowerInvariant();
            if (!allowedExtensions.Contains(fileExtension)) {
                await MessageService.Show(new MessageOption {
                    Content = "Only .xlsx and .xls file formats are supported",
                    Color = Color.Danger
                });
                return;
            }

            selectedFile = file.File;
            validationResult = null;

            await ValidateFile();
            StateHasChanged();
        }
    }

    private async Task ValidateFile() {
        if (selectedFile == null) return;

        try {
            var maxFileSize = 10 * 1024 * 1024; // 10MB
            if (selectedFile.Size > maxFileSize) {
                validationResult = new ValidationResult {
                    TotalRows = 0,
                    ValidCount = 0,
                    ErrorCount = 1,
                    Errors = new List<ValidationError> {
                        new ValidationError {
                            RowNumber = 0,
                            Field = "File",
                            Message = "File size exceeds 10MB limit",
                            Value = $"{selectedFile.Size / 1024 / 1024}MB"
                        }
                    }
                };
                return;
            }

            // 发送文件到后端进行验证
            using var content = new MultipartFormDataContent();
            var fileContent = new StreamContent(selectedFile.OpenReadStream(maxFileSize));
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(selectedFile.ContentType);
            content.Add(fileContent, "file", selectedFile.Name);

            // 添加模式参数
            content.Add(new StringContent(currentImportMode.ToString()), "mode");

            var response = await Api.PostFileAsync($"{relativePath}/validate", content);
            if (response.IsSuccessStatusCode) {
                var responseContent = await response.Content.ReadAsStringAsync();
                var httpResponse = JsonSerializer.Deserialize<HttpResponseModel<ValidationResult>>(responseContent, new JsonSerializerOptions {
                    PropertyNameCaseInsensitive = true
                });

                if (httpResponse?.IsSuccess == true && httpResponse.Data != null) {
                    validationResult = httpResponse.Data;
                }
                else {
                    validationResult = new ValidationResult {
                        TotalRows = 0,
                        ValidCount = 0,
                        ErrorCount = 1,
                        Errors = new List<ValidationError> {
                            new ValidationError {
                                RowNumber = 0,
                                Field = "Validation",
                                Message = httpResponse?.Message ?? "Validation failed",
                                Value = ""
                            }
                        }
                    };
                }
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                validationResult = new ValidationResult {
                    TotalRows = 0,
                    ValidCount = 0,
                    ErrorCount = 1,
                    Errors = new List<ValidationError> {
                        new ValidationError {
                            RowNumber = 0,
                            Field = "Server",
                            Message = $"Server error: {error}",
                            Value = ""
                        }
                    }
                };
            }
        }
        catch (Exception ex) {
            validationResult = new ValidationResult {
                TotalRows = 0,
                ValidCount = 0,
                ErrorCount = 1,
                Errors = new List<ValidationError> {
                    new ValidationError {
                        RowNumber = 0,
                        Field = "Exception",
                        Message = $"Error validating file: {ex.Message}",
                        Value = ""
                    }
                }
            };
        }
    }

    private async Task ExecuteOperation(ImportMode mode) {
        if (selectedFile == null || validationResult == null) return;

        isImporting = true;
        try {
            var maxFileSize = 10 * 1024 * 1024; // 10MB
            using var content = new MultipartFormDataContent();
            var fileContent = new StreamContent(selectedFile.OpenReadStream(maxFileSize));
            fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(selectedFile.ContentType);
            content.Add(fileContent, "file", selectedFile.Name);

            var endpoint = mode == ImportMode.Import ? "import" : "update";
            var operationName = mode == ImportMode.Import ? "Import" : "Update";

            var response = await Api.PostFileAsync($"{relativePath}/{endpoint}", content);
            if (response.IsSuccessStatusCode) {
                var responseContent = await response.Content.ReadAsStringAsync();
                var httpResponse = JsonSerializer.Deserialize<HttpResponseModel<ImportResult>>(responseContent, new JsonSerializerOptions {
                    PropertyNameCaseInsensitive = true
                });

                if (httpResponse?.IsSuccess == true && httpResponse.Data != null) {
                    var result = httpResponse.Data;
                    var message = $"{operationName} completed! ";

                    if (result.SuccessCount > 0) {
                        message += $"✅ {result.SuccessCount} record(s) processed successfully. ";
                    }

                    if (result.SkippedCount > 0) {
                        message += $"⚠️ {result.SkippedCount} record(s) skipped. ";
                    }

                    if (result.ErrorCount > 0) {
                        message += $"❌ {result.ErrorCount} record(s) failed. ";
                    }

                    if (result.Messages?.Any() == true) {
                        message += "\n\nDetails:\n" + string.Join("\n", result.Messages.Take(20)); // 限制显示前20条详细信息
                        if (result.Messages.Count > 20) {
                            message += $"\n... and {result.Messages.Count - 20} more messages";
                        }
                    }

                    var messageColor = result.SuccessCount > 0 ? Color.Success :
                        result.SkippedCount > 0 ? Color.Warning : Color.Danger;

                    await MessageService.Show(new MessageOption {
                        Content = message,
                        Color = messageColor
                    });

                    await importModal!.Close();
                    await ResetImportState();
                    await LoadItemLocationsAsync();
                    await table!.QueryAsync();
                }
                else {
                    await MessageService.Show(new MessageOption {
                        Content = $"{operationName} failed: {httpResponse?.Message ?? "Unknown error"}",
                        Color = Color.Danger
                    });
                }
            }
            else {
                var error = await response.Content.ReadAsStringAsync();
                await MessageService.Show(new MessageOption {
                    Content = $"{operationName} failed: {error}",
                    Color = Color.Danger
                });
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Operation failed: {ex.Message}",
                Color = Color.Danger
            });
        }
        finally {
            isImporting = false;
        }
    }

    private string GetImportDialogTitle() {
        return currentImportMode == ImportMode.Import ? "Import Item Locations from Excel" : "Update Item Locations from Excel";
    }

    private Color GetButtonColor() {
        return currentImportMode == ImportMode.Import ? Color.Primary : Color.Warning;
    }

    private string GetButtonIcon() {
        return currentImportMode == ImportMode.Import ? "fa-solid fa-file-import" : "fa-solid fa-file-pen";
    }

    private string GetButtonText() {
        return currentImportMode == ImportMode.Import ? "Import" : "Update";
    }

}
