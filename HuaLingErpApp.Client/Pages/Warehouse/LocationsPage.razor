@page "/warehouse/locations"

<SecurePage RequiredRoles="Administrator,Manager" PageName="Location Management">
    @if (isReady) {
        <Table TItem="Location"
               IsPagination="true" PageItemsSource="[20, 25, 30]"
               IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
               ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
               AddModalTitle="Add New Location" EditModalTitle="Edit Location"
               SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
               OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
               OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
               DoubleClickToEdit="true"
               ShowSkeleton="true">
            <TableColumns>
                <TableColumn @bind-Field="@context.Id" Text="Location Code" Sortable="true" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
                <TableColumn @bind-Field="@context.Description" Text="Description" Sortable="true" Searchable="true"
                             Align="Alignment.Center">
                </TableColumn>
            </TableColumns>
            <EmptyTemplate>
                <div class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-warehouse fa-3x mb-3"></i>
                        <h5>No locations found</h5>
                        <p>No locations match your search criteria. Try adjusting your search or add a new location.</p>
                    </div>
                </div>
            </EmptyTemplate>
        </Table>
    }
</SecurePage>

@code {
    private List<Location> LocationItems { get; set; } = new();

    private Location SearchModel { get; set; } = new();

    private string relativePath = "api/locations";
    private bool isReady;

    protected override async Task OnParametersSetAsync() {
        // Check authentication state, only load data if authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true) {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely() {
        try {
            await LoadLocationsAsync();
        }
        catch (Exception ex) {
            // Only show error message if authenticated
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true) {
                await MessageService.Show(new MessageOption {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadLocationsAsync() {
        try {
            var response = await Api.GetAsync<HttpResponseModel<List<Location>>>(relativePath);
            if (response?.IsSuccess == true && response.Data != null) {
                LocationItems = response.Data;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Failed to load locations: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private Task<QueryData<Location>> OnQueryAsync(QueryPageOptions options) {
        var items = LocationItems.Where(options.ToFilterFunc<Location>());
        if (!string.IsNullOrEmpty(options.SearchText)) {
            // Use Linq to process
            items = LocationItems.Where(i =>
                !string.IsNullOrEmpty(i.Id) && i.Id.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Description) && i.Description.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
            );
        }

        // Use Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName)) {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<Location>() {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<Location> OnAddAsync() {
        return await Task.FromResult(new Location() {
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(Location item, ItemChangedType changedType) {
        try {
            if (changedType == ItemChangedType.Add) {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Location added successfully",
                        Color = Color.Success
                    });
                    await LoadLocationsAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode) {
                    await MessageService.Show(new MessageOption {
                        Content = "Location updated successfully",
                        Color = Color.Success
                    });
                    await LoadLocationsAsync();
                    return true;
                }
                else {
                    // 解析错误响应
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Operation failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<Location> items) {
        try {
            var itemList = items.ToList();
            if (!itemList.Any()) {
                await MessageService.Show(new MessageOption {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1) {
                // Single delete
                response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
            }
            else {
                // Batch delete
                response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode) {
                await MessageService.Show(new MessageOption {
                    Content = "Location(s) deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadLocationsAsync();
                return true;
            }
        }
        catch (Exception ex) {
            await MessageService.Show(new MessageOption {
                Content = $"Delete operation failed: {ex.Message}",
                Color = Color.Danger
            });
        }

        return false;
    }

    private Task OnResetSearchAsync(Location searchModel) {
        searchModel.Id = string.Empty;
        searchModel.Description = string.Empty;
        return Task.CompletedTask;
    }

}
