@page "/warehouse/reasoncodes"
@using HuaLingErpApp.Shared

<SecurePage RequiredRoles="Administrator,Manager" PageName="Reason Code Management">
@if (isReady) {
    <Table TItem="ReasonCode"
           IsPagination="true" PageItemsSource="[20, 25, 30]"
           IsStriped="true" IsBordered="true" TableSize="TableSize.Compact"
           ShowToolbar="true" ShowSearch="true" IsMultipleSelect="true" ShowExtendButtons="true"
           AddModalTitle="Add New Reason Code" EditModalTitle="Edit Reason Code"
           SearchModel="@SearchModel" SearchMode="SearchMode.Popup" ShowEmpty="true"
           OnQueryAsync="@OnQueryAsync" OnResetSearchAsync="@OnResetSearchAsync"
           OnAddAsync="@OnAddAsync" OnSaveAsync="@OnSaveAsync" OnDeleteAsync="@OnDeleteAsync"
           DoubleClickToEdit="true"
           ShowSkeleton="true">
        <TableColumns>
            <TableColumn @bind-Field="@context.ReasonCodeValue" Text="Reason Code" Sortable="true" Searchable="true" Required="true"
                         Align="Alignment.Center">
            </TableColumn>
            <TableColumn @bind-Field="@context.Description" Text="Description" Searchable="true"
                         Align="Alignment.Center">
            </TableColumn>
            <TableColumn @bind-Field="@context.ReasonClass" Text="Reason Class" Sortable="true" Searchable="true" Required="true"
                         Align="Alignment.Center">
            </TableColumn>
            <TableColumn @bind-Field="@context.Account" Text="Account" Searchable="true"
                         Align="Alignment.Center">
                <EditTemplate Context="columnContext">
                    <div class="col-12 col-sm-6 col-md-6">
                        <Select TValue="string" @bind-Value="@columnContext.Account" 
                                Items="ChartItems"
                                ShowSearch="true"
                                IsPopover="true"
                                PlaceHolder="Select Account..." 
                                DisplayText="Account">
                            <Options>
                                @if (ChartItems != null)
                                {
                                    @foreach (var item in ChartItems)
                                    {
                                        <SelectOption Text="@item.Id" Value="@item.Id" />
                                    }
                                }
                            </Options>
                        </Select>
                    </div>
                </EditTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.AccountUnit1" Text="Unit 1" Searchable="true"
                         Align="Alignment.Center">
                <EditTemplate Context="columnContext">
                    <div class="col-12 col-sm-6 col-md-6">
                        <Select TValue="string" @bind-Value="@columnContext.AccountUnit1" 
                                Items="UnitCode1Items"
                                ShowSearch="true"
                                IsPopover="true"
                                PlaceHolder="Select Unit 1..." 
                                DisplayText="Unit 1">
                            <Options>
                                @if (UnitCode1Items != null)
                                {
                                    @foreach (var item in UnitCode1Items)
                                    {
                                        <SelectOption Text="@item.Id" Value="@item.Id" />
                                    }
                                }
                            </Options>
                        </Select>
                    </div>
                </EditTemplate>
            </TableColumn>
            <TableColumn @bind-Field="@context.AccountUnit2" Text="Unit 2" Searchable="true"
                         Align="Alignment.Center">
                <EditTemplate Context="columnContext">
                    <div class="col-12 col-sm-6 col-md-6">
                        <Select TValue="string" @bind-Value="@columnContext.AccountUnit2" 
                                Items="UnitCode2Items"
                                ShowSearch="true"
                                IsPopover="true"
                                PlaceHolder="Select Unit 2..." 
                                DisplayText="Unit 2">
                            <Options>
                                @if (UnitCode2Items != null)
                                {
                                    @foreach (var item in UnitCode2Items)
                                    {
                                        <SelectOption Text="@item.Id" Value="@item.Id" />
                                    }
                                }
                            </Options>
                        </Select>
                    </div>
                </EditTemplate>
            </TableColumn>
        </TableColumns>
        <EmptyTemplate>
            <div class="text-center py-5">
                <div class="text-muted">
                    <i class="fas fa-code fa-3x mb-3"></i>
                    <h5>No reason codes found</h5>
                    <p>No reason codes match your search criteria. Try adjusting your search or add a new reason code.</p>
                </div>
            </div>
        </EmptyTemplate>
    </Table>
}
</SecurePage>

@code {
    private List<ReasonCode> ReasonCodeItems { get; set; } = new();
    private List<Chart> ChartItems { get; set; } = new();
    private List<UnitCode1> UnitCode1Items { get; set; } = new();
    private List<UnitCode2> UnitCode2Items { get; set; } = new();

    private ReasonCode SearchModel { get; set; } = new();

    private string relativePath = "api/reasoncode";
    private bool isReady;

    protected override async Task OnParametersSetAsync()
    {
        // Check authentication state, only load data if authenticated
        var authState = await AuthStateProvider.GetAuthenticationStateAsync();
        if (authState.User.Identity?.IsAuthenticated == true)
        {
            await LoadDataSafely();
        }
    }

    private async Task LoadDataSafely()
    {
        try
        {
            await Task.WhenAll(
                LoadReasonCodesAsync(),
                LoadChartsAsync(),
                LoadUnitCode1Async(),
                LoadUnitCode2Async()
            );
        }
        catch (Exception ex)
        {
            // Only show error message if authenticated
            var authState = await AuthStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                await MessageService.Show(new MessageOption
                {
                    Content = $"Initialization failed: {ex.Message}",
                    Color = Color.Danger
                });
            }
        }
        finally
        {
            isReady = true;
            StateHasChanged();
        }
    }

    private async Task LoadReasonCodesAsync()
    {
        try
        {
            var response = await Api.GetAsync<HttpResponseModel<List<ReasonCode>>>(relativePath);
            if (response?.IsSuccess == true && response.Data != null)
            {
                ReasonCodeItems = response.Data;
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Failed to load reason codes: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadChartsAsync()
    {
        try
        {
            var response = await Api.GetAsync<HttpResponseModel<List<Chart>>>("api/charts");
            if (response?.IsSuccess == true && response.Data != null)
            {
                ChartItems = response.Data;
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Failed to load charts: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadUnitCode1Async()
    {
        try
        {
            var response = await Api.GetAsync<HttpResponseModel<List<UnitCode1>>>("api/unitcode1");
            if (response?.IsSuccess == true && response.Data != null)
            {
                UnitCode1Items = response.Data;
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Failed to load unit codes 1: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private async Task LoadUnitCode2Async()
    {
        try
        {
            var response = await Api.GetAsync<HttpResponseModel<List<UnitCode2>>>("api/unitcode2");
            if (response?.IsSuccess == true && response.Data != null)
            {
                UnitCode2Items = response.Data;
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Failed to load unit codes 2: {ex.Message}",
                Color = Color.Danger
            });
        }
    }

    private Task<QueryData<ReasonCode>> OnQueryAsync(QueryPageOptions options)
    {
        var items = ReasonCodeItems.Where(options.ToFilterFunc<ReasonCode>());
        if (!string.IsNullOrEmpty(options.SearchText))
        {
            // Use Linq to process
            items = ReasonCodeItems.Where(i =>
                !string.IsNullOrEmpty(i.ReasonCodeValue) && i.ReasonCodeValue.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Description) && i.Description.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.ReasonClass) && i.ReasonClass.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
                ||
                !string.IsNullOrEmpty(i.Account) && i.Account.Contains(options.SearchText, StringComparison.OrdinalIgnoreCase)
            );
        }

        // Use Sort extension method for sorting
        var isSorted = false;
        if (!string.IsNullOrEmpty(options.SortName))
        {
            items = items.Sort(options.SortName, options.SortOrder);
            isSorted = true;
        }

        // Set total record count
        var total = items.Count();

        // In-memory pagination
        items = items.Skip((options.PageIndex - 1) * options.PageItems).Take(options.PageItems);
        return Task.FromResult(new QueryData<ReasonCode>()
        {
            Items = items,
            TotalCount = total,
            IsSorted = isSorted,
            IsFiltered = options.Filters.Count > 0,
            IsSearch = options.CustomerSearches.Count > 0 || !string.IsNullOrEmpty(options.SearchText),
            IsAdvanceSearch = options.CustomerSearches.Count > 0 && string.IsNullOrEmpty(options.SearchText),
        });
    }

    private async Task<ReasonCode> OnAddAsync()
    {
        return await Task.FromResult(new ReasonCode()
        {
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now
        });
    }

    private async Task<bool> OnSaveAsync(ReasonCode item, ItemChangedType changedType)
    {
        try
        {
            if (changedType == ItemChangedType.Add)
            {
                var response = await Api.PostAsync(relativePath, item);
                if (response.IsSuccessStatusCode)
                {
                    await MessageService.Show(new MessageOption
                    {
                        Content = "Added successfully",
                        Color = Color.Success
                    });
                    await LoadReasonCodesAsync();
                    return true;
                }
                else
                {
                    // Parse error response
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption
                    {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
            else
            {
                var response = await Api.PutAsync($"{relativePath}/{item.Id}", item);
                if (response.IsSuccessStatusCode)
                {
                    await MessageService.Show(new MessageOption
                    {
                        Content = "Updated successfully",
                        Color = Color.Success
                    });
                    await LoadReasonCodesAsync();
                    return true;
                }
                else
                {
                    // Parse error response
                    var errorMessage = await ApiService.ParseErrorMessageAsync(response);

                    await MessageService.Show(new MessageOption
                    {
                        Content = errorMessage,
                        Color = Color.Danger
                    });
                    return false;
                }
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Save failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private async Task<bool> OnDeleteAsync(IEnumerable<ReasonCode> items)
    {
        try
        {
            var itemList = items.ToList();
            if (!itemList.Any())
            {
                await MessageService.Show(new MessageOption
                {
                    Content = "Please select items to delete",
                    Color = Color.Warning
                });
                return false;
            }

            var ids = itemList.Select(i => i.Id).ToList();
            HttpResponseMessage response;

            if (ids.Count == 1)
            {
                // Single delete
                response = await Api.DeleteAsync($"{relativePath}/{ids[0]}");
            }
            else
            {
                // Batch delete
                response = await Api.DeleteAsJsonAsync($"{relativePath}/batch", ids);
            }

            if (response.IsSuccessStatusCode)
            {
                await MessageService.Show(new MessageOption
                {
                    Content = "Deleted successfully",
                    Color = Color.Success
                });

                // Reload data
                await LoadReasonCodesAsync();
                return true;
            }
            else
            {
                var error = await response.Content.ReadAsStringAsync();
                throw new Exception(error);
            }
        }
        catch (Exception ex)
        {
            await MessageService.Show(new MessageOption
            {
                Content = $"Delete failed: {ex.Message}",
                Color = Color.Danger
            });
            return false;
        }
    }

    private Task OnResetSearchAsync(ReasonCode model)
    {
        model.ReasonCodeValue = "";
        model.Description = "";
        model.ReasonClass = "";
        model.Account = "";
        model.AccountUnit1 = "";
        model.AccountUnit2 = "";
        return Task.CompletedTask;
    }
}
