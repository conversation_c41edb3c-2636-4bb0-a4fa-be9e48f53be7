using MiniExcelLibs.Attributes;

namespace HuaLingErpApp.Shared.Models {
    /// <summary>
    /// Excel导入模式
    /// </summary>
    public enum ImportMode {
        Import, // 只导入新记录，重复的跳过
        Update // 只更新已存在记录，不存在的跳过
    }

    /// <summary>
    /// Excel导入Item数据传输对象
    /// </summary>
    public class ItemImportDto {
        [ExcelColumn(Name = "ItemCode")] public string ItemCode { get; set; } = "";

        [ExcelColumn(Name = "Description")] public string Description { get; set; } = "";

        [ExcelColumn(Name = "UnitOfMeasure")] public string UnitOfMeasure { get; set; } = "";

        [ExcelColumn(Name = "MaterialType")] public string MaterialType { get; set; } = "";

        [ExcelColumn(Name = "Source")] public string Source { get; set; } = "";

        [ExcelColumn(Name = "ABCCode")] public string ABCCode { get; set; } = "";

        [ExcelColumn(Name = "UnitCost")] public string UnitCost { get; set; } = "";

        [ExcelColumn(Name = "UnitPrice")] public string UnitPrice { get; set; } = "";

        [ExcelColumn(Name = "Status")] public string Status { get; set; } = "";
    }

    /// <summary>
    /// Excel导入ItemLocation数据传输对象
    /// </summary>
    public class ItemLocationImportDto {
        [ExcelColumn(Name = "Item")] public string Item { get; set; } = "";

        [ExcelColumn(Name = "Location")] public string Location { get; set; } = "";

        [ExcelColumn(Name = "Account")] public string Account { get; set; } = "";

        [ExcelColumn(Name = "QuantityOnHand")] public string QuantityOnHand { get; set; } = "";

        [ExcelColumn(Name = "Rank")] public string Rank { get; set; } = "";

        [ExcelColumn(Name = "UnitOfMeasure")] public string UnitOfMeasure { get; set; } = "";

        [ExcelColumn(Name = "AccountUnit1")] public string AccountUnit1 { get; set; } = "";

        [ExcelColumn(Name = "AccountUnit2")] public string AccountUnit2 { get; set; } = "";
    }

    /// <summary>
    /// Excel导入ReasonCode数据传输对象
    /// </summary>
    public class ReasonCodeImportDto {
        [ExcelColumn(Name = "ReasonCode")] public string ReasonCode { get; set; } = "";

        [ExcelColumn(Name = "Description")] public string Description { get; set; } = "";

        [ExcelColumn(Name = "ReasonClass")] public string ReasonClass { get; set; } = "";

        [ExcelColumn(Name = "Account")] public string Account { get; set; } = "";

        [ExcelColumn(Name = "AccountUnit1")] public string AccountUnit1 { get; set; } = "";

        [ExcelColumn(Name = "AccountUnit2")] public string AccountUnit2 { get; set; } = "";
    }

    /// <summary>
    /// Excel文件验证结果
    /// </summary>
    public class ValidationResult {
        /// <summary>
        /// 总行数（不包括表头）
        /// </summary>
        public int TotalRows { get; set; }

        /// <summary>
        /// 有效记录数
        /// </summary>
        public int ValidCount { get; set; }

        /// <summary>
        /// 错误记录数
        /// </summary>
        public int ErrorCount { get; set; }

        /// <summary>
        /// 验证错误列表
        /// </summary>
        public List<ValidationError> Errors { get; set; } = new();
    }

    /// <summary>
    /// 验证错误信息
    /// </summary>
    public class ValidationError {
        /// <summary>
        /// 行号
        /// </summary>
        public int RowNumber { get; set; }

        /// <summary>
        /// 字段名
        /// </summary>
        public string Field { get; set; } = "";

        /// <summary>
        /// 错误消息
        /// </summary>
        public string Message { get; set; } = "";

        /// <summary>
        /// 字段值
        /// </summary>
        public string Value { get; set; } = "";
    }

    /// <summary>
    /// Excel导入结果
    /// </summary>
    public class ImportResult {
        /// <summary>
        /// 总行数（不包括表头）
        /// </summary>
        public int TotalRows { get; set; }

        /// <summary>
        /// 成功导入数
        /// </summary>
        public int SuccessCount { get; set; }

        /// <summary>
        /// 跳过的记录数
        /// </summary>
        public int SkippedCount { get; set; }

        /// <summary>
        /// 错误记录数
        /// </summary>
        public int ErrorCount { get; set; }

        /// <summary>
        /// 消息列表
        /// </summary>
        public List<string> Messages { get; set; } = new();
    }
}