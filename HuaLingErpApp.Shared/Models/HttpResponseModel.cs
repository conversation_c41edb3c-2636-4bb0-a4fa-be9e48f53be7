using System;

namespace HuaLingErpApp.Shared.Models {
    public class HttpResponseModel<T> {
        public bool IsSuccess { get; set; }
        public string Message { get; set; } = string.Empty;
        public T? Data { get; set; }
        public DateTime Timestamp { get; } = DateTime.UtcNow;

        public static HttpResponseModel<T> Success(T? data, string message = "Operation successful") {
            return new HttpResponseModel<T> {
                IsSuccess = true,
                Message = message,
                Data = data
            };
        }

        public static HttpResponseModel<T> Error(string message) {
            return new HttpResponseModel<T> {
                IsSuccess = false,
                Message = message
            };
        }
    }
}