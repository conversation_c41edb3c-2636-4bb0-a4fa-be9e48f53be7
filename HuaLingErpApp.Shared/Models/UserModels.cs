using SqlSugar;
using System.ComponentModel.DataAnnotations;

namespace HuaLingErpApp.Shared.Models {
    /// <summary>
    /// User entity
    /// </summary>
    [SugarTable("Users")]
    public class User : IEntity<int> {
        [SugarColumn(ColumnName = "Id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [Required(ErrorMessage = "Username is required")]
        [StringLength(50, ErrorMessage = "Username cannot exceed 50 characters")]
        [SugarColumn(ColumnName = "UserName", Length = 50, IsNullable = false)]
        public string UserName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        [StringLength(100, ErrorMessage = "Email cannot exceed 100 characters")]
        [SugarColumn(ColumnName = "Email", Length = 100, IsNullable = false)]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password hash is required")]
        [SugarColumn(ColumnName = "PasswordHash", Length = 255, IsNullable = false)]
        public string PasswordHash { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "Display name cannot exceed 50 characters")]
        [SugarColumn(ColumnName = "DisplayName", Length = 50, IsNullable = true)]
        public string? DisplayName { get; set; }

        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        [SugarColumn(ColumnName = "PhoneNumber", Length = 20, IsNullable = true)]
        public string? PhoneNumber { get; set; }

        [SugarColumn(ColumnName = "IsActive", IsNullable = false)]
        public bool IsActive { get; set; } = true;

        [SugarColumn(ColumnName = "LastLoginTime", IsNullable = true)]
        public DateTime? LastLoginTime { get; set; }

        [SugarColumn(ColumnName = "CreatedDate", IsNullable = false)]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [SugarColumn(ColumnName = "CreatedBy", Length = 50, IsNullable = false)]
        public string CreatedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "ModifiedBy", Length = 50, IsNullable = false)]
        public string ModifiedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "RecordDate", IsNullable = false)]
        public DateTime RecordDate { get; set; } = DateTime.Now;

        // Navigation properties
        [SugarColumn(IsIgnore = true)] public List<UserRole> UserRoles { get; set; } = new();
    }

    /// <summary>
    /// Role entity
    /// </summary>
    [SugarTable("Roles")]
    public class Role : IEntity<int> {
        [SugarColumn(ColumnName = "Id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [Required(ErrorMessage = "Role name is required")]
        [StringLength(50, ErrorMessage = "Role name cannot exceed 50 characters")]
        [SugarColumn(ColumnName = "Name", Length = 50, IsNullable = false)]
        public string Name { get; set; } = string.Empty;

        [StringLength(200, ErrorMessage = "Role description cannot exceed 200 characters")]
        [SugarColumn(ColumnName = "Description", Length = 200, IsNullable = true)]
        public string? Description { get; set; }

        [SugarColumn(ColumnName = "CreatedDate", IsNullable = false)]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [SugarColumn(ColumnName = "CreatedBy", Length = 50, IsNullable = false)]
        public string CreatedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "ModifiedBy", Length = 50, IsNullable = false)]
        public string ModifiedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "RecordDate", IsNullable = false)]
        public DateTime RecordDate { get; set; } = DateTime.Now;

        // Navigation properties
        [SugarColumn(IsIgnore = true)] public List<UserRole> UserRoles { get; set; } = new();
    }

    /// <summary>
    /// User role association table
    /// </summary>
    [SugarTable("UserRoles")]
    public class UserRole : IEntity<int> {
        [SugarColumn(ColumnName = "Id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "UserId", IsNullable = false)]
        public int UserId { get; set; }

        [SugarColumn(ColumnName = "RoleId", IsNullable = false)]
        public int RoleId { get; set; }

        [SugarColumn(ColumnName = "CreatedDate", IsNullable = false)]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [SugarColumn(ColumnName = "CreatedBy", Length = 50, IsNullable = false)]
        public string CreatedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "ModifiedBy", Length = 50, IsNullable = false)]
        public string ModifiedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "RecordDate", IsNullable = false)]
        public DateTime RecordDate { get; set; } = DateTime.Now;

        // Navigation properties
        [SugarColumn(IsIgnore = true)] public User? User { get; set; }

        [SugarColumn(IsIgnore = true)] public Role? Role { get; set; }
    }

    /// <summary>
    /// Login log entity
    /// </summary>
    [SugarTable("LoginLogs")]
    public class LoginLog : IEntity<int> {
        [SugarColumn(ColumnName = "Id", IsPrimaryKey = true, IsIdentity = true)]
        public int Id { get; set; }

        [SugarColumn(ColumnName = "UserId", IsNullable = false)]
        public int UserId { get; set; }

        [SugarColumn(ColumnName = "UserName", Length = 50, IsNullable = false)]
        public string UserName { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "LoginTime", IsNullable = false)]
        public DateTime LoginTime { get; set; } = DateTime.Now;

        [SugarColumn(ColumnName = "IpAddress", Length = 45, IsNullable = true)]
        public string? IpAddress { get; set; }

        [SugarColumn(ColumnName = "UserAgent", Length = 500, IsNullable = true)]
        public string? UserAgent { get; set; }

        [SugarColumn(ColumnName = "IsSuccess", IsNullable = false)]
        public bool IsSuccess { get; set; }

        [SugarColumn(ColumnName = "FailureReason", Length = 200, IsNullable = true)]
        public string? FailureReason { get; set; }

        [SugarColumn(ColumnName = "CreatedDate", IsNullable = false)]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        [SugarColumn(ColumnName = "CreatedBy", Length = 50, IsNullable = false)]
        public string CreatedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "ModifiedBy", Length = 50, IsNullable = false)]
        public string ModifiedBy { get; set; } = string.Empty;

        [SugarColumn(ColumnName = "RecordDate", IsNullable = false)]
        public DateTime RecordDate { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// Login request model
    /// </summary>
    public class LoginRequest {
        [Required(ErrorMessage = "Username is required")]
        public string UserName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        public string Password { get; set; } = string.Empty;

        public bool RememberMe { get; set; }
    }

    /// <summary>
    /// Login response model
    /// </summary>
    public class LoginResponse {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public UserInfo? User { get; set; }
    }

    /// <summary>
    /// User information model (for frontend display and editing)
    /// </summary>
    public class UserInfo : IEntity<int> {
        public int Id { get; set; }
        public string UserName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? DisplayName { get; set; }
        public string? PhoneNumber { get; set; }
        public DateTime? LastLoginTime { get; set; }
        public List<string> Roles { get; set; } = new();

        // Audit fields (read-only, for display purposes)
        public DateTime CreatedDate { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime RecordDate { get; set; }
        public string ModifiedBy { get; set; } = string.Empty;

        // Fields for editing (not populated during query, only used for create/update)
        public string? Password { get; set; } // Only used when creating new user
    }

    /// <summary>
    /// Create user request model
    /// </summary>
    public class CreateUserRequest {
        [Required(ErrorMessage = "Username is required")]
        [StringLength(50, ErrorMessage = "Username cannot exceed 50 characters")]
        public string UserName { get; set; } = string.Empty;

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "Password is required")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be between 6-100 characters")]
        public string? Password { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "Display name cannot exceed 50 characters")]
        public string? DisplayName { get; set; }

        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string? PhoneNumber { get; set; }

        [Required(ErrorMessage = "Please select at least one role")]
        public List<int> RoleIds { get; set; } = new();
    }

    /// <summary>
    /// Update user request model
    /// </summary>
    public class UpdateUserRequest {
        public int Id { get; set; }

        [Required(ErrorMessage = "Email is required")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address")]
        public string Email { get; set; } = string.Empty;

        [StringLength(50, ErrorMessage = "Display name cannot exceed 50 characters")]
        public string? DisplayName { get; set; }

        [StringLength(20, ErrorMessage = "Phone number cannot exceed 20 characters")]
        public string? PhoneNumber { get; set; }

        public bool IsActive { get; set; }

        [Required(ErrorMessage = "Please select at least one role")]
        public List<int> RoleIds { get; set; } = new();
    }

    /// <summary>
    /// Change password request model
    /// </summary>
    public class ChangePasswordRequest {
        public int UserId { get; set; }

        [Required(ErrorMessage = "Current password is required")]
        public string CurrentPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "New password is required")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be between 6-100 characters")]
        public string NewPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "Confirm password is required")]
        [Compare("NewPassword", ErrorMessage = "New password and confirm password do not match")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// Admin reset password request model (Administrator only)
    /// </summary>
    public class AdminResetPasswordRequest {
        public int UserId { get; set; }

        [Required(ErrorMessage = "New password is required")]
        [StringLength(100, MinimumLength = 6, ErrorMessage = "Password must be between 6-100 characters")]
        public string NewPassword { get; set; } = string.Empty;

        [Required(ErrorMessage = "Confirm password is required")]
        [Compare("NewPassword", ErrorMessage = "New password and confirm password do not match")]
        public string ConfirmPassword { get; set; } = string.Empty;
    }

    /// <summary>
    /// Verify password request model
    /// </summary>
    public class VerifyPasswordRequest {
        [Required(ErrorMessage = "Password is required")]
        public string Password { get; set; } = string.Empty;
    }

    /// <summary>
    /// System predefined roles
    /// </summary>
    public static class SystemRoles {
        public const string Administrator = "Administrator";
        public const string Manager = "Manager";
        public const string Operator = "Operator";
        public const string Viewer = "Viewer";

        public static readonly Dictionary<string, string> RoleDescriptions = new() {
            { Administrator, "System Administrator - Has all permissions" },
            { Manager, "Manager - Has management permissions" },
            { Operator, "Operator - Has operation permissions" },
            { Viewer, "Viewer - Has view-only permissions" }
        };

        public static List<string> GetAllRoles() => RoleDescriptions.Keys.ToList();
    }
}