using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Services;
using HuaLingErpApp.Shared.Models;
using HuaLingErpApp.Shared;
using System.Security.Claims;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/auth")]
    public class AuthController : ControllerBase {
        private readonly IAuthService _authService;
        private readonly ILogger<AuthController> _logger;

        public AuthController(IAuthService authService, ILogger<AuthController> logger) {
            _authService = authService;
            _logger = logger;
        }

        /// <summary>
        /// User login
        /// </summary>
        /// <param name="request">Login request</param>
        /// <returns>Login response</returns>
        [HttpPost("login")]
        public async Task<ActionResult<HttpResponseModel<LoginResponse>>> Login([FromBody] LoginRequest request) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<LoginResponse>.Error("Invalid request data"));
                }

                var result = await _authService.LoginAsync(request, HttpContext);

                if (result.Success) {
                    return Ok(HttpResponseModel<LoginResponse>.Success(result, "Login successful"));
                }
                else {
                    return Unauthorized(HttpResponseModel<LoginResponse>.Error(result.Message));
                }
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred during login");
                return StatusCode(500,
                    HttpResponseModel<LoginResponse>.Error($"An error occurred during login: {ex.Message}"));
            }
        }

        /// <summary>
        /// User logout
        /// </summary>
        /// <returns>Logout response</returns>
        [HttpPost("logout")]
        [Authorize]
        public async Task<ActionResult<HttpResponseModel<object>>> Logout() {
            try {
                await _authService.LogoutAsync(HttpContext);
                return Ok(HttpResponseModel<object>.Success(null, "Logout successful"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred during logout");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred during logout: {ex.Message}"));
            }
        }

        /// <summary>
        /// User logout (GET for navigation)
        /// </summary>
        /// <returns>Redirect to login</returns>
        [HttpGet("logout")]
        [Authorize]
        public async Task<IActionResult> LogoutGet() {
            try {
                await _authService.LogoutAsync(HttpContext);
                return Redirect("/login");
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred during logout");
                return Redirect("/login");
            }
        }

        /// <summary>
        /// Get current user information
        /// </summary>
        /// <returns>Current user information</returns>
        [HttpGet("me")]
        [Authorize]
        public async Task<ActionResult<HttpResponseModel<UserInfo>>> GetCurrentUser() {
            try {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId)) {
                    return Unauthorized(HttpResponseModel<UserInfo>.Error("Invalid user session"));
                }

                var userInfo = await _authService.GetUserInfoAsync(userId);
                if (userInfo == null) {
                    return NotFound(HttpResponseModel<UserInfo>.Error("User not found"));
                }

                return Ok(HttpResponseModel<UserInfo>.Success(userInfo));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while getting current user");
                return StatusCode(500,
                    HttpResponseModel<UserInfo>.Error(
                        $"An error occurred while getting user information: {ex.Message}"));
            }
        }

        /// <summary>
        /// Change password
        /// </summary>
        /// <param name="request">Change password request</param>
        /// <returns>Change password response</returns>
        [HttpPost("change-password")]
        [Authorize]
        public async Task<ActionResult<HttpResponseModel<object>>> ChangePassword(
            [FromBody] ChangePasswordRequest request) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Invalid request data"));
                }

                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId)) {
                    return Unauthorized(HttpResponseModel<object>.Error("Invalid user session"));
                }

                request.UserId = userId;
                var result = await _authService.ChangePasswordAsync(request);

                if (result) {
                    return Ok(HttpResponseModel<object>.Success(null, "Password changed successfully"));
                }
                else {
                    return BadRequest(
                        HttpResponseModel<object>.Error(
                            "Failed to change password. Please check your current password."));
                }
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while changing password");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while changing password: {ex.Message}"));
            }
        }

        /// <summary>
        /// Check authentication status
        /// </summary>
        /// <returns>Authentication status</returns>
        [HttpGet("status")]
        public ActionResult<HttpResponseModel<object>> GetAuthStatus() {
            try {
                var isAuthenticated = User.Identity?.IsAuthenticated ?? false;
                var userName = User.Identity?.Name;
                var roles = User.FindAll(ClaimTypes.Role).Select(c => c.Value).ToList();

                var status = new {
                    IsAuthenticated = isAuthenticated,
                    UserName = userName,
                    Roles = roles
                };

                return Ok(HttpResponseModel<object>.Success(status));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while getting auth status");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while getting authentication status: {ex.Message}"));
            }
        }

        /// <summary>
        /// Verify current password
        /// </summary>
        /// <param name="request">Verify password request</param>
        /// <returns>Verification result</returns>
        [HttpPost("verify-current-password")]
        [Authorize]
        public async Task<ActionResult<HttpResponseModel<bool>>> VerifyCurrentPassword(
            [FromBody] VerifyPasswordRequest request) {
            try {
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId)) {
                    return Unauthorized(HttpResponseModel<bool>.Error("Invalid user session"));
                }

                var result = await _authService.VerifyCurrentPasswordAsync(userId, request.Password);
                return Ok(HttpResponseModel<bool>.Success(result));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while verifying current password");
                return StatusCode(500,
                    HttpResponseModel<bool>.Error($"An error occurred while verifying password: {ex.Message}"));
            }
        }
    }
}