using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using System;
using System.Threading.Tasks;
using System.Linq.Expressions;
using SqlSugar;
using System.Collections.Generic;
using Microsoft.AspNetCore.Authorization;

namespace HuaLingErpApp.Controller
{
    [ApiController]
    [Route("api/charts")]
    [Authorize]
    public class ChartController(
        IRepository<Chart, string> repository,
        ILogger<ChartController> logger,
        ISqlSugarClient db)
        : ControllerBase
    {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<Chart>>>> GetAll()
        {
            try
            {
                var items = await repository.GetDataAsync();
                return Ok(HttpResponseModel<List<Chart>>.Success(items));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to retrieve charts list");
                return StatusCode(500,
                    HttpResponseModel<List<Chart>>.Error($"An error occurred while retrieving charts: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<Chart>>> GetById(string id)
        {
            try
            {
                var item = await repository.GetByIdAsync(id);
                if (item == null)
                {
                    return NotFound(HttpResponseModel<Chart>.Error("Chart not found"));
                }
                return Ok(HttpResponseModel<Chart>.Success(item));
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to retrieve chart {id}");
                return StatusCode(500,
                    HttpResponseModel<Chart>.Error($"An error occurred while retrieving the chart: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<Chart>>> Create([FromBody] Chart model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var exists = await repository.AnyAsync(p => p.Id == model.Id);
                if (exists)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Chart already exists"));
                }
                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await repository.AddAndReturnAsync(model);
                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<Chart>.Success(result, "Chart created successfully")
                );
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to create chart");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the chart: {ex.Message}"));
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] Chart model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                if (id != model.Id)
                {
                    return BadRequest(HttpResponseModel<object>.Error("ID mismatch"));
                }

                var existing = await repository.GetByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Chart not found"));
                }
                // Preserve original creation audit fields
                model.CreatedDate = existing.CreatedDate;
                model.CreatedBy = existing.CreatedBy;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                var success = await repository.UpdateAsync(model);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to update the chart"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to update chart {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the chart: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id)
        {
            try
            {
                var existing = await repository.GetByIdAsync(id);
                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Chart not found"));
                }

                var success = await repository.DeleteAsync(id);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete the chart"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, $"Failed to delete chart {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the chart: {ex.Message}"));
            }
        }

        [HttpDelete("batch")]
        public async Task<IActionResult> DeleteBatch([FromBody] List<string> ids)
        {
            try
            {
                if (ids == null || ids.Count == 0)
                {
                    return BadRequest(HttpResponseModel<object>.Error("No IDs provided for deletion"));
                }

                var success = await repository.DeleteBatchAsync(ids);
                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete the selected charts"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to batch delete charts");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the charts: {ex.Message}"));
            }
        }
    }
}
