using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using System;
using System.Threading.Tasks;
using System.Linq.Expressions;
using Microsoft.AspNetCore.Authorization;
using SqlSugar;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class CurrencyCodeController(
        IRepository<CurrencyCode, string> repository,
        ILogger<CurrencyCodeController> logger,
        ISqlSugarClient db)
        : ControllerBase {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<CurrencyCode>>>> GetAll() {
            try {
                var items = await repository.GetDataAsync();
                return HttpResponseModel<List<CurrencyCode>>.Success(items);
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to retrieve currency code list");
                return StatusCode(500,
                    HttpResponseModel<List<CurrencyCode>>.Error(
                        $"An error occurred while retrieving currency codes: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<CurrencyCode>>> GetById(string id) {
            try {
                var item = await repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<CurrencyCode>.Error("Currency code not found"));
                }

                return HttpResponseModel<CurrencyCode>.Success(item);
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to retrieve currency code {id}");
                return StatusCode(500,
                    HttpResponseModel<CurrencyCode>.Error(
                        $"An error occurred while retrieving the currency code: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<CurrencyCode>>> Create([FromBody] CurrencyCode model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var exists = await repository.AnyAsync(p => p.Id == model.Id);
                if (exists) {
                    return BadRequest(HttpResponseModel<object>.Error("Currency code already exists"));
                }

                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await repository.AddAndReturnAsync(model);
                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<CurrencyCode>.Success(result, "Currency code created successfully")
                );
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to create currency code");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while creating the currency code: {ex.Message}"));
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] CurrencyCode model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                if (id != model.Id) {
                    return BadRequest(HttpResponseModel<object>.Error("ID mismatch"));
                }

                var existing = await repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Currency code not found"));
                }

                // Preserve original creation audit fields
                model.CreatedDate = existing.CreatedDate;
                model.CreatedBy = existing.CreatedBy;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                var success = await repository.UpdateAsync(model);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to update currency code"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to update currency code {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while updating the currency code: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id) {
            try {
                var existing = await repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Currency code not found"));
                }

                var success = await repository.DeleteAsync(id);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete currency code"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to delete currency code {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while deleting the currency code: {ex.Message}"));
            }
        }

        [HttpPost("batch")]
        public async Task<IActionResult> BatchDelete([FromBody] List<string>? ids) {
            try {
                if (ids is null || !ids.Any()) {
                    return BadRequest(HttpResponseModel<object>.Error("No IDs provided for deletion"));
                }

                var table = await repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count) {
                    return NotFound(HttpResponseModel<object>.Error("One or more currency not found"));
                }

                var success = await repository.DeleteBatchAsync(ids);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete selected currency codes"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to batch delete currency codes");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while deleting the currency codes: {ex.Message}"));
            }
        }
    }
}