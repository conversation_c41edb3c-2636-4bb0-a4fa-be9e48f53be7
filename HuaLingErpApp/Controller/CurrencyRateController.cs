using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HuaLingErpApp.Shared.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using SqlSugar;
using System.Security.Claims;

namespace HuaLingErpApp.Controller
{
    [ApiController]
    [Route("api/currencyrates")]
    [Authorize]
    public class CurrencyRateController(ISqlSugarClient db, ILogger<CurrencyRateController> logger) : ControllerBase {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");
        private readonly ILogger<CurrencyRateController> _logger = logger;

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<CurrencyRate>>>> GetAll()
        {
            try
            {
                var items = await _db.Queryable<CurrencyRate>()
                    .OrderBy(x => x.FromCurrCode)
                    .OrderBy(x => x.ToCurrCode)
                    .OrderByDescending(x => x.EffDate)
                    .ToListAsync();
                
                return Ok(HttpResponseModel<List<CurrencyRate>>.Success(items));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to retrieve currency rates list");
                return StatusCode(500,
                    HttpResponseModel<List<CurrencyRate>>.Error($"An error occurred while retrieving currency rates: {ex.Message}"));
            }
        }

        [HttpGet("{fromCurrCode}/{toCurrCode}/{effDate}")]
        public async Task<ActionResult<HttpResponseModel<CurrencyRate>>> GetById(string fromCurrCode, string toCurrCode, string effDate)
        {
            try
            {
                if (!DateTime.TryParse(effDate, out var parsedDate))
                {
                    return BadRequest(HttpResponseModel<CurrencyRate>.Error("Invalid date format"));
                }

                var item = await _db.Queryable<CurrencyRate>()
                    .Where(x => x.FromCurrCode == fromCurrCode && 
                               x.ToCurrCode == toCurrCode && 
                               x.EffDate == parsedDate)
                    .FirstAsync();

                if (item == null)
                {
                    return NotFound(HttpResponseModel<CurrencyRate>.Error("Currency rate not found"));
                }

                return Ok(HttpResponseModel<CurrencyRate>.Success(item));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to retrieve currency rate {fromCurrCode}/{toCurrCode}/{effDate}");
                return StatusCode(500,
                    HttpResponseModel<CurrencyRate>.Error($"An error occurred while retrieving the currency rate: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<CurrencyRate>>> Create([FromBody] CurrencyRate model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                // Check if the currency rate already exists
                var exists = await _db.Queryable<CurrencyRate>()
                    .Where(x => x.FromCurrCode == model.FromCurrCode && 
                               x.ToCurrCode == model.ToCurrCode && 
                               x.EffDate == model.EffDate)
                    .AnyAsync();

                if (exists)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Currency rate for this date already exists"));
                }

                // Set audit fields
                var currentUser = User.FindFirst(ClaimTypes.Name)?.Value ?? "System";
                model.CreatedBy = currentUser;
                model.CreatedDate = DateTime.Now;
                model.RecordDate = DateTime.Now;

                var result = await _db.Insertable(model).ExecuteReturnEntityAsync();
                
                return CreatedAtAction(
                    nameof(GetById),
                    new { fromCurrCode = result.FromCurrCode, toCurrCode = result.ToCurrCode, effDate = result.EffDate?.ToString("yyyy-MM-dd") },
                    HttpResponseModel<CurrencyRate>.Success(result, "Currency rate created successfully")
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create currency rate");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the currency rate: {ex.Message}"));
            }
        }

        [HttpPut("{fromCurrCode}/{toCurrCode}/{effDate}")]
        public async Task<IActionResult> Update(string fromCurrCode, string toCurrCode, string effDate, [FromBody] CurrencyRate model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                if (!DateTime.TryParse(effDate, out var parsedDate))
                {
                    return BadRequest(HttpResponseModel<object>.Error("Invalid date format"));
                }

                if (fromCurrCode != model.FromCurrCode || toCurrCode != model.ToCurrCode || parsedDate != model.EffDate)
                {
                    return BadRequest(HttpResponseModel<object>.Error("URL parameters do not match model data"));
                }

                var existing = await _db.Queryable<CurrencyRate>()
                    .Where(x => x.FromCurrCode == fromCurrCode && 
                               x.ToCurrCode == toCurrCode && 
                               x.EffDate == parsedDate)
                    .FirstAsync();

                if (existing == null)
                {
                    return NotFound(HttpResponseModel<object>.Error("Currency rate not found"));
                }

                // Preserve original creation audit fields
                model.CreatedDate = existing.CreatedDate;
                model.CreatedBy = existing.CreatedBy;

                // Set modification audit fields
                var currentUser = User.FindFirst(ClaimTypes.Name)?.Value ?? "System";
                model.ModifiedBy = currentUser;
                model.RecordDate = DateTime.Now;

                var success = await _db.Updateable(model)
                    .Where(x => x.FromCurrCode == fromCurrCode && 
                               x.ToCurrCode == toCurrCode && 
                               x.EffDate == parsedDate)
                    .ExecuteCommandAsync() > 0;

                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to update the currency rate"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to update currency rate {fromCurrCode}/{toCurrCode}/{effDate}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the currency rate: {ex.Message}"));
            }
        }

        [HttpDelete("{fromCurrCode}/{toCurrCode}/{effDate}")]
        public async Task<IActionResult> Delete(string fromCurrCode, string toCurrCode, string effDate)
        {
            try
            {
                if (!DateTime.TryParse(effDate, out var parsedDate))
                {
                    return BadRequest(HttpResponseModel<object>.Error("Invalid date format"));
                }

                var exists = await _db.Queryable<CurrencyRate>()
                    .Where(x => x.FromCurrCode == fromCurrCode && 
                               x.ToCurrCode == toCurrCode && 
                               x.EffDate == parsedDate)
                    .AnyAsync();

                if (!exists)
                {
                    return NotFound(HttpResponseModel<object>.Error("Currency rate not found"));
                }

                var success = await _db.Deleteable<CurrencyRate>()
                    .Where(x => x.FromCurrCode == fromCurrCode && 
                               x.ToCurrCode == toCurrCode && 
                               x.EffDate == parsedDate)
                    .ExecuteCommandAsync() > 0;

                if (!success)
                {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete the currency rate"));
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Failed to delete currency rate {fromCurrCode}/{toCurrCode}/{effDate}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the currency rate: {ex.Message}"));
            }
        }

        [HttpDelete("batch")]
        public async Task<IActionResult> DeleteBatch([FromBody] List<CurrencyRate> items)
        {
            try
            {
                if (items == null || items.Count == 0)
                {
                    return BadRequest(HttpResponseModel<object>.Error("No items provided for deletion"));
                }

                var deleteCount = 0;
                var notFoundCount = 0;

                foreach (var item in items)
                {
                    // Check if the currency rate exists
                    var exists = await _db.Queryable<CurrencyRate>()
                        .Where(x => x.FromCurrCode == item.FromCurrCode &&
                                   x.ToCurrCode == item.ToCurrCode &&
                                   x.EffDate == item.EffDate)
                        .AnyAsync();

                    if (!exists)
                    {
                        notFoundCount++;
                        continue;
                    }

                    var success = await _db.Deleteable<CurrencyRate>()
                        .Where(x => x.FromCurrCode == item.FromCurrCode &&
                                   x.ToCurrCode == item.ToCurrCode &&
                                   x.EffDate == item.EffDate)
                        .ExecuteCommandAsync() > 0;

                    if (success) deleteCount++;
                }

                var message = $"Successfully deleted {deleteCount} currency rate(s)";
                if (notFoundCount > 0)
                {
                    message += $", {notFoundCount} item(s) not found";
                }

                return Ok(HttpResponseModel<object>.Success(null, message));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete currency rates batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting currency rates: {ex.Message}"));
            }
        }
    }
}
