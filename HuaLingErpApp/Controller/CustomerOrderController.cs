using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using SqlSugar;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class CustomerOrderController(
        IRepository<CustomerOrder, int> repository,
        ILogger<CustomerOrderController> logger,
        ISqlSugarClient db)
        : ControllerBase {
        private readonly IRepository<CustomerOrder, int> _repository = repository;
        private readonly ILogger<CustomerOrderController> _logger = logger;
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        // GET: api/customerorder
        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<CustomerOrder>>>> GetAll() {
            try {
                var items = await _repository.GetDataAsync();
                return Ok(HttpResponseModel<List<CustomerOrder>>.Success(items));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to retrieve customer order list");
                return StatusCode(500,
                    HttpResponseModel<List<CustomerOrder>>.Error(
                        $"An error occurred while retrieving customer orders: {ex.Message}"));
            }
        }

        // GET: api/customerorder/5
        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<CustomerOrder>>> GetById(int id) {
            try {
                var item = await _repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<CustomerOrder>.Error("Customer order not found"));
                }

                return Ok(HttpResponseModel<CustomerOrder>.Success(item));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to retrieve customer order {id}");
                return StatusCode(500,
                    HttpResponseModel<CustomerOrder>.Error(
                        $"An error occurred while retrieving the customer order: {ex.Message}"));
            }
        }

        // POST: api/customerorder
        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<CustomerOrder>>> Create([FromBody] CustomerOrder model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                // Check if CO number already exists
                var exists = await _repository.AnyAsync(p => p.CoNum == model.CoNum);
                if (exists) {
                    return BadRequest(HttpResponseModel<object>.Error("CO number already exists"));
                }

                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await _repository.AddAndReturnAsync(model);

                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<CustomerOrder>.Success(result, "Customer order created successfully")
                );
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to create customer order");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while creating the customer order: {ex.Message}"));
            }
        }

        // PUT: api/customerorder/5
        [HttpPut("{id}")]
        public async Task<ActionResult<HttpResponseModel<bool>>> Update(int id, [FromBody] CustomerOrder model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var existing = await _repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Customer order not found"));
                }

                // Check if CO number is used by other records
                var exists = await _repository.AnyAsync(p => p.Id != id && p.CoNum == model.CoNum);
                if (exists) {
                    return BadRequest(HttpResponseModel<object>.Error("CO number is already in use by another record"));
                }

                // Update business fields
                existing.CoNum = model.CoNum;
                existing.CustNum = model.CustNum;
                existing.CurrCode = model.CurrCode;
                existing.TermsCode = model.TermsCode;
                existing.TaxCode = model.TaxCode;
                existing.Contact = model.Contact;
                existing.Phone = model.Phone;
                existing.Email = model.Email;
                existing.Addr = model.Addr;
                existing.CoDate = model.CoDate;
                existing.Status = model.Status;
                existing.TaxRate = model.TaxRate;
                existing.ExchRate = model.ExchRate;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                // CreatedBy and CreatedDate will be preserved
                var success = await _repository.UpdateAsync(existing);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to update customer order"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Customer order updated successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to update customer order {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while updating the customer order: {ex.Message}"));
            }
        }

        // DELETE: api/customerorder/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<HttpResponseModel<bool>>> Delete(int id) {
            try {
                var item = await _repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<object>.Error("Customer order not found"));
                }

                var success = await _repository.DeleteAsync(id);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete customer order"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Customer order deleted successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to delete customer order {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while deleting the customer order: {ex.Message}"));
            }
        }

        // DELETE: api/customerorder/batch
        [HttpDelete("batch")]
        public async Task<ActionResult<HttpResponseModel<bool>>> DeleteBatch([FromBody] List<int>? ids) {
            try {
                if (ids is null || ids.Count == 0) {
                    return BadRequest(HttpResponseModel<object>.Error("No IDs provided for deletion"));
                }

                var table = await _repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count) {
                    return NotFound(HttpResponseModel<object>.Error("One or more customer orders not found"));
                }

                var success = await _repository.DeleteBatchAsync(ids);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete customer orders"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Customer orders deleted successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to delete customer orders in batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting customer orders: {ex.Message}"));
            }
        }

        /// <summary>
        /// 获取所有客户订单号
        /// </summary>
        /// <returns>客户订单号列表</returns>
        [HttpGet("conums")]
        public async Task<ActionResult<HttpResponseModel<List<string?>>>> GetAllCoNums() {
            try {
                // 使用 Select 只查询 CoNum 字段
                var co = await _repository.GetDataAsync();

                var coNums = co.Select(x => x.CoNum)
                    .Where(coNum => !string.IsNullOrEmpty(coNum))
                    .OrderBy(coNum => coNum).ToList();
                return Ok(HttpResponseModel<List<string?>>.Success(coNums));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "An error occurred while retrieving customer order numbers");
                return StatusCode(500,
                    HttpResponseModel<List<CustomerOrder>>.Error(
                        $"An error occurred while retrieving customer order numbers: {ex.Message}"));
            }
        }

        /// <summary>
        /// 测试审计功能 - 获取客户订单的审计信息
        /// </summary>
        /// <param name="id">客户订单ID</param>
        /// <returns>包含审计信息的客户订单详情</returns>
        [HttpGet("{id}/audit")]
        public async Task<ActionResult<HttpResponseModel<object>>> GetAuditInfo(int id) {
            try {
                var item = await _repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<object>.Error("Customer order not found"));
                }

                var auditInfo = new {
                    Id = item.Id,
                    CoNum = item.CoNum,
                    AuditInfo = new {
                        CreatedBy = item.CreatedBy,
                        CreatedDate = item.CreatedDate,
                        ModifiedBy = item.ModifiedBy,
                        RecordDate = item.RecordDate
                    },
                    BusinessData = new {
                        CustNum = item.CustNum,
                        Status = item.Status,
                        Contact = item.Contact
                    }
                };

                return Ok(HttpResponseModel<object>.Success(auditInfo, "Audit information retrieved successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to retrieve audit info for customer order {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while retrieving audit information: {ex.Message}"));
            }
        }
    }
}