using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using SqlSugar;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class CustomerOrderLinesController(
        IRepository<CoItem, int> repository,
        ILogger<CustomerOrderLinesController> logger,
        ISqlSugarClient db)
        : ControllerBase {
        private readonly IRepository<CoItem, int> _repository = repository;
        private readonly ILogger<CustomerOrderLinesController> _logger = logger;
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        // GET: api/customerorderlines
        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<CoItem>>>> GetAll() {
            try {
                var items = await _repository.GetDataAsync();
                return Ok(HttpResponseModel<List<CoItem>>.Success(items));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to retrieve customer order lines");
                return StatusCode(500,
                    HttpResponseModel<List<CoItem>>.Error(
                        $"An error occurred while retrieving customer order lines: {ex.Message}"));
            }
        }

        // GET: api/customerorderlines/5
        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<CoItem>>> GetById(int id) {
            try {
                var item = await _repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<CoItem>.Error("Customer order line not found"));
                }

                return HttpResponseModel<CoItem>.Success(item);
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to retrieve customer order line {id}");
                return StatusCode(500,
                    HttpResponseModel<CoItem>.Error(
                        $"An error occurred while retrieving the customer order line: {ex.Message}"));
            }
        }

        // GET: api/customerorderlines/byconum/CO12345
        [HttpGet("byconum/{coNum}")]
        public async Task<ActionResult<HttpResponseModel<List<CoItem>>>> GetByCoNum(string coNum) {
            try {
                var items = await _repository.QueryAsync(x => x.CoNum == coNum);
                if (items.Count == 0) {
                    return NotFound(
                        HttpResponseModel<List<CoItem>>.Error($"No customer order lines found for CO number: {coNum}"));
                }

                return Ok(HttpResponseModel<List<CoItem>>.Success(items));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to retrieve customer order lines for CO number: {coNum}");
                return StatusCode(500,
                    HttpResponseModel<List<CoItem>>.Error(
                        $"An error occurred while retrieving customer order lines for CO number: {coNum}"));
            }
        }

        // POST: api/customerorderlines
        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<CoItem>>> Create([FromBody] CoItem model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                // Check if CO line already exists for this CO
                var exists = await _repository.AnyAsync(p => p.CoNum == model.CoNum && p.CoLine == model.CoLine);
                if (exists) {
                    return BadRequest(
                        HttpResponseModel<object>.Error("A line with this CO number and line number already exists"));
                }

                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await _repository.AddAndReturnAsync(model);

                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<CoItem>.Success(result, "Customer order line created successfully")
                );
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to create customer order line");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while creating the customer order line: {ex.Message}"));
            }
        }

        // PUT: api/customerorderlines/5
        [HttpPut("{id}")]
        public async Task<ActionResult<HttpResponseModel<bool>>> Update(int id, [FromBody] CoItem model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var existing = await _repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Customer order line not found"));
                }

                // Check if CO number and line combination is used by other records
                var exists =
                    await _repository.AnyAsync(p => p.Id != id && p.CoNum == model.CoNum && p.CoLine == model.CoLine);
                if (exists) {
                    return BadRequest(
                        HttpResponseModel<object>.Error("This CO number and line combination is already in use"));
                }

                // Update fields
                existing.CoNum = model.CoNum;
                existing.CoLine = model.CoLine;
                existing.Item = model.Item;
                existing.QtyOrdered = model.QtyOrdered;
                existing.QtyShipped = model.QtyShipped;
                existing.QtyInvoiced = model.QtyInvoiced;
                existing.UnitPrice = model.UnitPrice;
                existing.Status = model.Status;
                existing.DueDate = model.DueDate;
                existing.UnitCostShipped = model.UnitCostShipped;
                existing.Location = model.Location;
                existing.UnitOfMeasure = model.UnitOfMeasure;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                // CreatedBy and CreatedDate will be preserved
                var success = await _repository.UpdateAsync(existing);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to update customer order line"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Customer order line updated successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to update customer order line {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while updating the customer order line: {ex.Message}"));
            }
        }

        // DELETE: api/customerorderlines/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<HttpResponseModel<bool>>> Delete(int id) {
            try {
                var item = await _repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<object>.Error("Customer order line not found"));
                }

                // Add business logic validations here if needed
                // For example, prevent deletion if already shipped

                var success = await _repository.DeleteAsync(id);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete customer order line"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Customer order line deleted successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to delete customer order line {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while deleting the customer order line: {ex.Message}"));
            }
        }

        // DELETE: api/customerorderlines/batch
        [HttpDelete("batch")]
        public async Task<ActionResult<HttpResponseModel<bool>>> DeleteBatch([FromBody] List<int>? ids) {
            try {
                if (ids is null || ids.Count == 0) {
                    return BadRequest(HttpResponseModel<object>.Error("No line items selected for deletion"));
                }

                // Add any business validations here before deletion
                var table = await _repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count) {
                    return NotFound(HttpResponseModel<object>.Error("One or more line items not found"));
                }

                var success = await _repository.DeleteBatchAsync(ids);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete selected line items"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Selected line items deleted successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to delete customer order lines in batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the line items: {ex.Message}"));
            }
        }
    }
}