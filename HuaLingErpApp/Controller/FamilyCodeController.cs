using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using SqlSugar;
using Microsoft.AspNetCore.Authorization;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class FamilyCodeController(
        IRepository<FamilyCode, string> repository,
        ILogger<FamilyCodeController> logger,
        ISqlSugarClient db)
        : ControllerBase {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<FamilyCode>>>> GetAll() {
            try {
                var items = await repository.GetDataAsync();
                return Ok(HttpResponseModel<List<FamilyCode>>.Success(items));
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to retrieve units of measure list");
                return StatusCode(500,
                    HttpResponseModel<List<FamilyCode>>.Error(
                        $"An error occurred while retrieving FamilyCode: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<FamilyCode>>> GetById(string id) {
            try {
                var item = await repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<FamilyCode>.Error("FamilyCode not found"));
                }

                return Ok(HttpResponseModel<FamilyCode>.Success(item));
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to retrieve FamilyCode {id}");
                return StatusCode(500,
                    HttpResponseModel<FamilyCode>.Error(
                        $"An error occurred while retrieving the FamilyCode: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<FamilyCode>>> Create([FromBody] FamilyCode model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var exists = await repository.AnyAsync(p => p.Id == model.Id);
                if (exists) {
                    return BadRequest(HttpResponseModel<object>.Error("FamilyCode already exists"));
                }

                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await repository.AddAndReturnAsync(model);
                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<FamilyCode>.Success(result, "FamilyCode created successfully")
                );
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to create FamilyCode");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the FamilyCode: {ex.Message}"));
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] FamilyCode model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                if (id != model.Id) {
                    return BadRequest(HttpResponseModel<object>.Error("ID mismatch"));
                }

                var existing = await repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("FamilyCode not found"));
                }

                // Preserve original creation audit fields
                model.CreatedDate = existing.CreatedDate;
                model.CreatedBy = existing.CreatedBy;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                var success = await repository.UpdateAsync(model);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to update the FamilyCode"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to update FamilyCode {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the FamilyCode: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id) {
            try {
                var existing = await repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("FamilyCode not found"));
                }

                var success = await repository.DeleteAsync(id);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete the FamilyCode"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to delete FamilyCode {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the FamilyCode: {ex.Message}"));
            }
        }

        [HttpDelete("batch")]
        public async Task<IActionResult> DeleteBatch([FromBody] List<string>? ids) {
            try {
                if (ids is null || ids.Count == 0) {
                    return BadRequest(HttpResponseModel<object>.Error("No FamilyCode IDs provided for batch deletion"));
                }

                var table = await repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count) {
                    return NotFound(HttpResponseModel<object>.Error("One or more FamilyCode not found"));
                }

                var success = await repository.DeleteBatchAsync(ids);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete one or more FamilyCode"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to delete FamilyCode in batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while deleting FamilyCode in batch: {ex.Message}"));
            }
        }
    }
}