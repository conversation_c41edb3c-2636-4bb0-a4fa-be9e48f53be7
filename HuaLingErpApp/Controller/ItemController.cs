using System.Security.Claims;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MiniExcelLibs;
using SqlSugar;

namespace HuaLingErpApp.Controller;

[ApiController]
[Route("api/items")]
[Authorize] // Require authentication for all actions
public class ItemController(
    IRepository<Item, string> repository,
    ILogger<ItemController> logger,
    ISqlSugarClient db)
    : ControllerBase {
    private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

    [HttpGet]
    public async Task<ActionResult<HttpResponseModel<List<Item>>>> GetAll() {
        try {
            var items = await repository.GetDataAsync();
            return Ok(HttpResponseModel<List<Item>>.Success(items));
        }
        catch (Exception ex) {
            logger.LogError(ex, "Failed to retrieve items list");
            return StatusCode(500,
                HttpResponseModel<List<Item>>.Error($"An error occurred while retrieving items: {ex.Message}"));
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<HttpResponseModel<Item>>> GetById(string id) {
        try {
            var item = await repository.GetByIdAsync(id);
            if (item == null) return NotFound(HttpResponseModel<Item>.Error("Item not found"));
            return Ok(HttpResponseModel<Item>.Success(item));
        }
        catch (Exception ex) {
            logger.LogError(ex, $"Failed to retrieve item {id}");
            return StatusCode(500,
                HttpResponseModel<Item>.Error($"An error occurred while retrieving the item: {ex.Message}"));
        }
    }

    [HttpGet("{id}/inventory")]
    public async Task<ActionResult<HttpResponseModel<List<ItemInventoryDto>>>> GetItemInventory(string id) {
        try {
            // 1. 获取物料基本信息
            var item = await repository.GetByIdAsync(id);
            if (item == null) return NotFound(HttpResponseModel<List<ItemInventoryDto>>.Error("Item not found"));

            // 2. 获取库存信息
            var itemLocations = await _db.Queryable<ItemLocation>()
                .Where(il => il.Item == id)
                .Select(il => new {
                    il.Location,
                    il.QuantityOnHand
                })
                .ToListAsync();

            // 3. 组合结果
            var result = itemLocations.Select(il => new ItemInventoryDto {
                Item = new Item {
                    Id = item.Id,
                    Description = item.Description,
                    UnitOfMeasure = item.UnitOfMeasure
                },
                Location = il.Location,
                QuantityOnHand = il.QuantityOnHand
            }).ToList();

            return Ok(HttpResponseModel<List<ItemInventoryDto>>.Success(result));
        }
        catch (Exception ex) {
            logger.LogError(ex, $"Failed to retrieve inventory for item {id}");
            return StatusCode(500,
                HttpResponseModel<List<ItemInventoryDto>>.Error(
                    $"An error occurred while retrieving the item inventory: {ex.Message}"));
        }
    }

    [HttpPost]
    public async Task<ActionResult<HttpResponseModel<Item>>> Create([FromBody] Item model) {
        try {
            if (!ModelState.IsValid)
                return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));

            var exists = await repository.AnyAsync(p => p.Id == model.Id);
            if (exists) return BadRequest(HttpResponseModel<object>.Error("Item already exists"));

            // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
            var result = await repository.AddAndReturnAsync(model);
            return CreatedAtAction(
                nameof(GetById),
                new { id = result.Id },
                HttpResponseModel<Item>.Success(result, "Item created successfully")
            );
        }
        catch (Exception ex) {
            logger.LogError(ex, "Failed to create item");
            return StatusCode(500,
                HttpResponseModel<object>.Error($"An error occurred while creating the item: {ex.Message}"));
        }
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> Update(string id, [FromBody] Item model) {
        try {
            if (!ModelState.IsValid)
                return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));

            if (id != model.Id) return BadRequest(HttpResponseModel<object>.Error("ID mismatch"));

            var existing = await repository.GetByIdAsync(id);
            if (existing == null) return NotFound(HttpResponseModel<object>.Error("Item not found"));

            // Preserve original creation audit fields
            model.CreatedDate = existing.CreatedDate;
            model.CreatedBy = existing.CreatedBy;

            // ModifiedBy and RecordDate will be automatically set by AuditableRepository
            var success = await repository.UpdateAsync(model);
            if (!success)
                return StatusCode(500,
                    HttpResponseModel<object>.Error("Failed to update the item"));

            return NoContent();
        }
        catch (Exception ex) {
            logger.LogError(ex, $"Failed to update item {id}");
            return StatusCode(500,
                HttpResponseModel<object>.Error($"An error occurred while updating the item: {ex.Message}"));
        }
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> Delete(string id) {
        try {
            var existing = await repository.GetByIdAsync(id);
            if (existing == null) return NotFound(HttpResponseModel<object>.Error("Item not found"));

            var success = await repository.DeleteAsync(id);
            if (!success)
                return StatusCode(500,
                    HttpResponseModel<object>.Error("Failed to delete the item"));

            return NoContent();
        }
        catch (Exception ex) {
            logger.LogError(ex, $"Failed to delete item {id}");
            return StatusCode(500,
                HttpResponseModel<object>.Error($"An error occurred while deleting the item: {ex.Message}"));
        }
    }

    [HttpDelete("batch")]
    public async Task<ActionResult<HttpResponseModel<bool>>> DeleteBatch([FromBody] List<string>? ids) {
        try {
            if (ids is null || ids.Count == 0)
                return BadRequest(HttpResponseModel<object>.Error("No IDs provided for deletion"));

            // Add any business logic checks here before batch deletion
            var table = await repository
                .QueryAsync(x => ids.Contains(x.Id));
            var existingCount = table.Count;

            if (existingCount != ids.Count)
                return NotFound(HttpResponseModel<object>.Error("One or more items not found"));
            var success = await repository.DeleteBatchAsync(ids);
            if (!success) return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete items"));

            return Ok(HttpResponseModel<bool>.Success(true, "Items deleted successfully"));
        }
        catch (Exception ex) {
            logger.LogError(ex, "Failed to delete items in batch");
            return StatusCode(500,
                HttpResponseModel<object>.Error($"An error occurred while deleting items: {ex.Message}"));
        }
    }

    /// <summary>
    ///     获取物料的审计信息
    /// </summary>
    /// <param name="id">物料代码</param>
    /// <returns>包含审计信息的物料详情</returns>
    [HttpGet("{id}/audit")]
    public async Task<ActionResult<HttpResponseModel<object>>> GetAuditInfo(string id) {
        try {
            var item = await repository.GetByIdAsync(id);
            if (item == null) return NotFound(HttpResponseModel<object>.Error("Item not found"));

            var auditInfo = new {
                item.Id,
                item.Description,
                AuditInfo = new {
                    item.CreatedBy,
                    item.CreatedDate,
                    item.ModifiedBy,
                    item.RecordDate
                }
            };

            return Ok(HttpResponseModel<object>.Success(auditInfo, "Item audit information retrieved successfully"));
        }
        catch (Exception ex) {
            logger.LogError(ex, $"Failed to retrieve audit info for item {id}");
            return StatusCode(500,
                HttpResponseModel<object>.Error(
                    $"An error occurred while retrieving item audit information: {ex.Message}"));
        }
    }

    #region Excel Import/Export

    [HttpGet("template")]
    public IActionResult DownloadTemplate() {
        try {
            // 创建模板数据 - 使用一致的数据类型
            var templateData = new List<object> {
                // 示例数据行
                new {
                    ItemCode = "ITM001",
                    Description = "Steel Pipe 10mm",
                    UnitOfMeasure = "PCS",
                    MaterialType = "Raw Material",
                    Source = "Purchased",
                    ABCCode = "A",
                    UnitCost = "10.50",
                    UnitPrice = "15.00",
                    Status = "Active"
                },
                // 空行供用户填写
                new {
                    ItemCode = "",
                    Description = "",
                    UnitOfMeasure = "",
                    MaterialType = "",
                    Source = "",
                    ABCCode = "",
                    UnitCost = "",
                    UnitPrice = "",
                    Status = ""
                }
            };

            using var stream = new MemoryStream();
            stream.SaveAs(templateData);
            var fileBytes = stream.ToArray();

            // 设置正确的响应头
            Response.Headers.Append("Content-Disposition", "attachment; filename=\"ItemTemplate.xlsx\"");

            return File(fileBytes,
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "ItemTemplate.xlsx");
        }
        catch (Exception ex) {
            logger.LogError(ex, "Failed to generate Excel template");
            return StatusCode(500, $"Failed to generate template: {ex.Message}");
        }
    }

    [HttpPost("validate")]
    public async Task<ActionResult<HttpResponseModel<ValidationResult>>> ValidateExcel(IFormFile? file,
        [FromForm] string mode = "Import") {
        try {
            if (file == null || file.Length == 0)
                return BadRequest(HttpResponseModel<ValidationResult>.Error("No file uploaded"));

            // 解析模式
            var importMode = Enum.TryParse<ImportMode>(mode, true, out var parsedMode) ? parsedMode : ImportMode.Import;

            var validationResult = await ValidateExcelFile(file, importMode);
            return Ok(HttpResponseModel<ValidationResult>.Success(validationResult));
        }
        catch (Exception ex) {
            logger.LogError(ex, "Failed to validate Excel file");
            return StatusCode(500,
                HttpResponseModel<ValidationResult>.Error(
                    $"An error occurred while validating the file: {ex.Message}"));
        }
    }

    [HttpPost("import")]
    public async Task<ActionResult<HttpResponseModel<ImportResult>>> ImportExcel(IFormFile? file) {
        try {
            if (file == null || file.Length == 0)
                return BadRequest(HttpResponseModel<ImportResult>.Error("No file uploaded"));

            var importResult = await ImportExcelFile(file, ImportMode.Import);
            return Ok(HttpResponseModel<ImportResult>.Success(importResult));
        }
        catch (Exception ex) {
            logger.LogError(ex, "Failed to import Excel file");
            return StatusCode(500,
                HttpResponseModel<ImportResult>.Error($"An error occurred while importing the file: {ex.Message}"));
        }
    }

    [HttpPost("update")]
    public async Task<ActionResult<HttpResponseModel<ImportResult>>> UpdateExcel(IFormFile? file) {
        try {
            if (file == null || file.Length == 0)
                return BadRequest(HttpResponseModel<ImportResult>.Error("No file uploaded"));

            var updateResult = await ImportExcelFile(file, ImportMode.Update);
            return Ok(HttpResponseModel<ImportResult>.Success(updateResult));
        }
        catch (Exception ex) {
            logger.LogError(ex, "Failed to update Excel file");
            return StatusCode(500,
                HttpResponseModel<ImportResult>.Error($"An error occurred while updating the file: {ex.Message}"));
        }
    }

    #endregion

    #region Private Helper Methods

    private async Task<ValidationResult> ValidateExcelFile(IFormFile? file, ImportMode mode = ImportMode.Import) {
        var result = new ValidationResult();
        var errors = new List<ValidationError>();

        try {
            using var stream = new MemoryStream();
            await file?.CopyToAsync(stream)!;
            stream.Position = 0;

            // 使用MiniExcel读取数据
            var rows = stream.Query<ItemImportDto>().ToList();

            if (rows.Count == 0) {
                errors.Add(new ValidationError {
                    RowNumber = 0,
                    Field = "File",
                    Message = "No data rows found in the Excel file",
                    Value = ""
                });
                result.Errors = errors;
                return result;
            }

            result.TotalRows = rows.Count;

            // 获取现有的Item Code用于重复检查
            var existingItemCodes = await _db.Queryable<Item>()
                .Select(x => x.Id)
                .ToListAsync();

            // 获取有效的Unit of Measure
            var validUnits = await _db.Queryable<UnitOfMeasure>()
                .Select(x => x.Id)
                .ToListAsync();

            var itemCodesInFile = new HashSet<string>();

            for (var i = 0; i < rows.Count; i++) {
                var row = rows[i];
                var rowNumber = i + 2; // Excel行号从2开始（跳过表头）
                var rowErrors = ValidateRowData(row, rowNumber, existingItemCodes, validUnits, itemCodesInFile, mode);
                errors.AddRange(rowErrors);
            }

            result.Errors = errors;
            result.ErrorCount = errors.Count;

            // 计算有错误的行数（而不是错误总数）
            var errorRows = errors.Select(e => e.RowNumber).Distinct().Count();
            result.ValidCount = result.TotalRows - errorRows;

            return result;
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error validating Excel file");
            errors.Add(new ValidationError {
                RowNumber = 0,
                Field = "File",
                Message = $"Error reading Excel file: {ex.Message}",
                Value = ""
            });
            result.Errors = errors;
            return result;
        }
    }

    private static List<ValidationError> ValidateRowData(ItemImportDto row, int rowNumber,
        List<string> existingItemCodes, List<string> validUnits, HashSet<string> itemCodesInFile, ImportMode mode) {
        var errors = new List<ValidationError>();

        // 获取单元格值
        var itemCode = row.ItemCode.Trim();
        var description = row.Description.Trim();
        var unitOfMeasure = row.UnitOfMeasure.Trim();
        var materialType = row.MaterialType.Trim();
        var source = row.Source.Trim();
        var abcCode = row.ABCCode.Trim();
        var unitCostText = row.UnitCost.Trim();
        var unitPriceText = row.UnitPrice.Trim();
        var status = row.Status.Trim();

        // 验证必填字段
        if (string.IsNullOrEmpty(itemCode))
            errors.Add(new ValidationError {
                RowNumber = rowNumber, Field = "Item Code", Message = "Item Code is required", Value = itemCode
            });

        if (string.IsNullOrEmpty(description))
            errors.Add(new ValidationError {
                RowNumber = rowNumber, Field = "Description", Message = "Description is required",
                Value = description
            });

        if (string.IsNullOrEmpty(unitOfMeasure))
            errors.Add(new ValidationError {
                RowNumber = rowNumber, Field = "Unit of Measure", Message = "Unit of Measure is required",
                Value = unitOfMeasure ?? ""
            });

        if (string.IsNullOrEmpty(materialType))
            errors.Add(new ValidationError {
                RowNumber = rowNumber, Field = "Material Type", Message = "Material Type is required",
                Value = materialType ?? ""
            });

        if (string.IsNullOrEmpty(source))
            errors.Add(new ValidationError
                { RowNumber = rowNumber, Field = "Source", Message = "Source is required", Value = source ?? "" });

        if (string.IsNullOrEmpty(abcCode))
            errors.Add(new ValidationError
                { RowNumber = rowNumber, Field = "ABC Code", Message = "ABC Code is required", Value = abcCode ?? "" });

        if (string.IsNullOrEmpty(status))
            errors.Add(new ValidationError
                { RowNumber = rowNumber, Field = "Status", Message = "Status is required", Value = status ?? "" });

        // 验证Item Code唯一性
        if (!string.IsNullOrEmpty(itemCode)) {
            // 在导入模式下检查数据库重复，在更新模式下跳过
            if (mode == ImportMode.Import && existingItemCodes.Contains(itemCode))
                errors.Add(new ValidationError {
                    RowNumber = rowNumber, Field = "Item Code", Message = "Item Code already exists in database",
                    Value = itemCode
                });

            // 检查文件内重复
            if (!itemCodesInFile.Add(itemCode))
                errors.Add(new ValidationError {
                    RowNumber = rowNumber,
                    Field = "Item Code",
                    Message = "Duplicate Item Code in file",
                    Value = itemCode
                });
        }

        // 验证Unit of Measure存在性
        if (!string.IsNullOrEmpty(unitOfMeasure) && !validUnits.Contains(unitOfMeasure))
            errors.Add(new ValidationError {
                RowNumber = rowNumber, Field = "Unit of Measure", Message = "Unit of Measure not found in system",
                Value = unitOfMeasure
            });

        // 验证Source枚举值
        if (!string.IsNullOrEmpty(source)) {
            var validSources = new[] { "Purchased", "Manufactured", "Transferred" };
            if (!validSources.Any(s => string.Equals(s, source, StringComparison.OrdinalIgnoreCase)))
                errors.Add(new ValidationError {
                    RowNumber = rowNumber, Field = "Source",
                    Message = "Source must be Purchased, Manufactured, or Transferred", Value = source
                });
        }

        // 验证ABC Code枚举值
        if (!string.IsNullOrEmpty(abcCode)) {
            var validAbcCodes = new[] { "A", "B", "C" };
            if (!validAbcCodes.Any(s => string.Equals(s, abcCode, StringComparison.OrdinalIgnoreCase)))
                errors.Add(new ValidationError {
                    RowNumber = rowNumber, Field = "ABC Code", Message = "ABC Code must be A, B, or C", Value = abcCode
                });
        }

        // 验证Status枚举值
        if (!string.IsNullOrEmpty(status)) {
            var validStatuses = new[] { "Active", "Disabled" };
            if (!validStatuses.Any(s => string.Equals(s, status, StringComparison.OrdinalIgnoreCase)))
                errors.Add(new ValidationError {
                    RowNumber = rowNumber, Field = "Status", Message = "Status must be Active or Disabled",
                    Value = status
                });
        }

        // 验证数值字段
        if (!string.IsNullOrEmpty(unitCostText) && !decimal.TryParse(unitCostText, out _))
            errors.Add(new ValidationError {
                RowNumber = rowNumber, Field = "Unit Cost", Message = "Unit Cost must be a valid number",
                Value = unitCostText
            });

        if (!string.IsNullOrEmpty(unitPriceText) && !decimal.TryParse(unitPriceText, out _))
            errors.Add(new ValidationError {
                RowNumber = rowNumber, Field = "Unit Price", Message = "Unit Price must be a valid number",
                Value = unitPriceText
            });

        return errors;
    }

    private async Task<ImportResult> ImportExcelFile(IFormFile? file, ImportMode mode = ImportMode.Import) {
        var result = new ImportResult();
        var messages = new List<string>();

        logger.LogInformation("Starting Excel import for file: {FileName}", file?.FileName);

        try {
            using var stream = new MemoryStream();
            await file.CopyToAsync(stream);
            stream.Position = 0;

            // 使用MiniExcel读取数据
            var rows = stream.Query<ItemImportDto>().ToList();
            result.TotalRows = rows.Count;
            logger.LogInformation("Read {RowCount} rows from Excel file", rows.Count);

            if (rows.Count == 0) {
                result.Messages.Add("No data rows found in the Excel file");
                return result;
            }

            // 获取现有的Item Code用于重复检查
            var existingItemCodes = await _db.Queryable<Item>()
                .Select(x => x.Id)
                .ToListAsync();

            var itemsToInsert = new List<Item>();
            var itemsToUpdate = new List<Item>();
            var skippedCount = 0;
            var invalidCount = 0;

            for (var i = 0; i < rows.Count; i++) {
                try {
                    var row = rows[i];
                    var rowNumber = i + 2; // Excel行号从2开始（跳过表头）

                    // 检查基本数据有效性
                    var itemCode = TruncateString(row.ItemCode?.Trim() ?? "", 30);
                    var description = TruncateString(row.Description?.Trim() ?? "", 60);

                    if (string.IsNullOrEmpty(itemCode) || string.IsNullOrEmpty(description)) {
                        messages.Add($"Row {rowNumber}: Missing required fields (ItemCode or Description)");
                        invalidCount++;
                        continue;
                    }

                    var itemExists = existingItemCodes.Contains(itemCode);

                    if (mode == ImportMode.Import) {
                        // 导入模式：只处理新的Item Code
                        if (itemExists) {
                            messages.Add($"Row {rowNumber}: Item Code '{itemCode}' already exists, skipped");
                            skippedCount++;
                            continue;
                        }

                        // 创建新Item对象
                        var item = CreateItemFromRowData(row);
                        if (item != null) {
                            itemsToInsert.Add(item);
                            logger.LogDebug("Successfully prepared item for insertion: {ItemCode}", itemCode);
                        }
                        else {
                            messages.Add($"Row {rowNumber}: Failed to create item from data");
                            invalidCount++;
                        }
                    }
                    else if (mode == ImportMode.Update) {
                        // 更新模式：只处理已存在的Item Code
                        if (!itemExists) {
                            messages.Add($"Row {rowNumber}: Item Code '{itemCode}' does not exist, skipped");
                            skippedCount++;
                            continue;
                        }

                        // 创建更新Item对象
                        var item = CreateItemFromRowData(row);
                        if (item != null) {
                            itemsToUpdate.Add(item);
                            logger.LogDebug("Successfully prepared item for update: {ItemCode}", itemCode);
                        }
                        else {
                            messages.Add($"Row {rowNumber}: Failed to create item from data");
                            invalidCount++;
                        }
                    }
                }
                catch (Exception ex) {
                    logger.LogError(ex, "Failed to process row {RowIndex}", i + 1);
                    messages.Add($"Row {i + 2}: Failed to process - {ex.Message}");
                    invalidCount++;
                }
            }

            result.SkippedCount = skippedCount + invalidCount;

            // 执行数据库操作
            if (mode == ImportMode.Import && itemsToInsert.Any()) {
                try {
                    await _db.Insertable(itemsToInsert).ExecuteCommandAsync();
                    result.SuccessCount = itemsToInsert.Count;
                    logger.LogInformation("Successfully imported {Count} items", itemsToInsert.Count);
                    messages.Add($"Successfully imported {itemsToInsert.Count} items");
                }
                catch (Exception ex) {
                    logger.LogError(ex, "Failed to insert items to database");
                    messages.Add($"Database error: {ex.Message}");
                    result.ErrorCount = itemsToInsert.Count;
                }
            }
            else if (mode == ImportMode.Update && itemsToUpdate.Any()) {
                try {
                    await _db.Updateable(itemsToUpdate).ExecuteCommandAsync();
                    result.SuccessCount = itemsToUpdate.Count;
                    logger.LogInformation("Successfully updated {Count} items", itemsToUpdate.Count);
                    messages.Add($"Successfully updated {itemsToUpdate.Count} items");
                }
                catch (Exception ex) {
                    logger.LogError(ex, "Failed to update items in database");
                    messages.Add($"Database error: {ex.Message}");
                    result.ErrorCount = itemsToUpdate.Count;
                }
            }
            else {
                var operationType = mode == ImportMode.Import ? "import" : "update";
                messages.Add($"No items to {operationType}");
            }

            // 添加汇总信息
            if (skippedCount > 0) {
                var reason = mode == ImportMode.Import ? "already exist or not found" : "not found or already exist";
                messages.Add($"{skippedCount} items skipped ({reason})");
            }

            if (invalidCount > 0) {
                messages.Add($"{invalidCount} items skipped (invalid data)");
            }

            var operationName = mode == ImportMode.Import ? "Import" : "Update";
            logger.LogInformation(
                "{Operation} completed: {SuccessCount} processed, {SkippedCount} skipped, {ErrorCount} errors",
                operationName, result.SuccessCount, result.SkippedCount, result.ErrorCount);

            result.Messages = messages;
            return result;
        }
        catch (Exception ex) {
            logger.LogError(ex, "Error importing Excel file");
            result.Messages.Add($"Error importing file: {ex.Message}");
            result.ErrorCount = result.TotalRows;
            return result;
        }
    }

    private Item? CreateItemFromRowData(ItemImportDto row) {
        // 获取并截断字段数据以符合数据库约束
        var itemCode = TruncateString(row.ItemCode?.Trim() ?? "", 30);
        var description = TruncateString(row.Description?.Trim() ?? "", 60);
        var unitOfMeasure = TruncateString(row.UnitOfMeasure?.Trim() ?? "", 3);
        var materialType = TruncateString(row.MaterialType?.Trim() ?? "", 10);
        var source = TruncateString(row.Source?.Trim() ?? "", 10);
        var abcCode = TruncateString(row.ABCCode?.Trim() ?? "", 1);
        var unitCostText = row.UnitCost?.Trim() ?? "";
        var unitPriceText = row.UnitPrice?.Trim() ?? "";
        var status = row.Status?.Trim() ?? "";

        logger.LogDebug("CreateItemFromRowData: ItemCode='{ItemCode}', Description='{Description}'", itemCode,
            description);

        if (string.IsNullOrEmpty(itemCode) || string.IsNullOrEmpty(description)) {
            logger.LogWarning(
                "Skipping row due to missing ItemCode or Description: ItemCode='{ItemCode}', Description='{Description}'",
                itemCode, description);
            return null;
        }

        decimal.TryParse(unitCostText, out var unitCost);
        decimal.TryParse(unitPriceText, out var unitPrice);

        // 转换Status: Active -> "1", Disabled -> "0"
        var statusValue = string.Equals(status, "Active", StringComparison.OrdinalIgnoreCase) ? "1" : "0";

        // 获取当前用户信息
        var currentUser = GetCurrentUser();
        return new Item {
            Id = itemCode,
            Description = description,
            UnitOfMeasure = unitOfMeasure ?? "",
            MaterialType = materialType ?? "",
            PMTCode = source ?? "",
            ABCCode = abcCode ?? "",
            UnitCost = unitCost,
            UnitPrice = unitPrice,
            Status = statusValue,
            CreatedDate = DateTime.Now,
            RecordDate = DateTime.Now,
            CreatedBy = currentUser,
            ModifiedBy = currentUser
        };
    }

    /// <summary>
    ///     获取当前用户信息
    /// </summary>
    private string GetCurrentUser() {
        var user = HttpContext?.User;
        if (user?.Identity?.IsAuthenticated == true)
            return user.FindFirst(ClaimTypes.Name)?.Value
                   ?? user.Identity.Name ?? user.FindFirst(ClaimTypes.Email)?.Value
                   ?? "System";
        return "System";
    }

    /// <summary>
    /// 截断字符串到指定长度
    /// </summary>
    private static string TruncateString(string input, int maxLength) {
        if (string.IsNullOrEmpty(input)) return string.Empty;
        return input.Length <= maxLength ? input : input.Substring(0, maxLength);
    }

    #endregion
}