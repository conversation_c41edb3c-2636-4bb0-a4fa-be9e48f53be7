using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using Microsoft.AspNetCore.Authorization;
using SqlSugar;
using MiniExcelLibs;
using System.Security.Claims;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/itemlocations")]
    [Authorize]
    public class ItemLocationController(
        IRepository<ItemLocation, int> repository,
        ILogger<ItemLocationController> logger,
        ISqlSugarClient db)
        : ControllerBase {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<ItemLocation>>>> GetAll() {
            try {
                var items = await repository.GetDataAsync();
                return Ok(HttpResponseModel<List<ItemLocation>>.Success(items));
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to retrieve item locations list");
                return StatusCode(500,
                    HttpResponseModel<List<ItemLocation>>.Error(
                        $"An error occurred while retrieving item locations: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<ItemLocation>>> GetById(int id) {
            try {
                var item = await repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<ItemLocation>.Error("Item location not found"));
                }

                return Ok(HttpResponseModel<ItemLocation>.Success(item));
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to retrieve item location {id}");
                return StatusCode(500,
                    HttpResponseModel<ItemLocation>.Error(
                        $"An error occurred while retrieving the item location: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<ItemLocation>>> Create([FromBody] ItemLocation model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                // Check if the item-location combination already exists
                var exists = await repository
                    .AnyAsync(x => x.Item == model.Item && x.Location == model.Location);

                if (exists) {
                    return BadRequest(HttpResponseModel<object>.Error("Item-location combination already exists"));
                }

                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await repository.AddAndReturnAsync(model);
                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<ItemLocation>.Success(result, "Item location created successfully")
                );
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to create item location");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while creating the item location: {ex.Message}"));
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] ItemLocation model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                if (id != model.Id) {
                    return BadRequest(HttpResponseModel<object>.Error("ID mismatch"));
                }

                var existing = await repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Item location not found"));
                }

                // Check if the item-location combination already exists for a different record
                var duplicate = await repository
                    .AnyAsync(x => x.Id != id &&
                                   x.Item == model.Item &&
                                   x.Location == model.Location);

                if (duplicate) {
                    return BadRequest(HttpResponseModel<object>.Error("Item-location combination already exists"));
                }

                // Preserve original creation audit fields
                model.CreatedDate = existing.CreatedDate;
                model.CreatedBy = existing.CreatedBy;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                var success = await repository.UpdateAsync(model);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to update the item location"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to update item location {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while updating the item location: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id) {
            try {
                var existing = await repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Item location not found"));
                }

                var success = await repository.DeleteAsync(id);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete the item location"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to delete item location {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while deleting the item location: {ex.Message}"));
            }
        }

        [HttpDelete("batch")]
        public async Task<IActionResult> DeleteBatch([FromBody] List<int>? ids) {
            try {
                if (ids is null || !ids.Any()) {
                    return BadRequest(
                        HttpResponseModel<object>.Error("No item location IDs provided for batch deletion"));
                }

                var table = await repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count) {
                    return NotFound(HttpResponseModel<object>.Error("One or more item locations not found"));
                }

                var success = await repository.DeleteBatchAsync(ids);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete one or more item locations"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to delete item locations in batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while deleting item locations in batch: {ex.Message}"));
            }
        }

        [HttpGet("by-item/{item}")]
        public async Task<ActionResult<HttpResponseModel<List<ItemLocation>>>> GetByItem(string item) {
            try {
                var items = await repository
                    .QueryAsync(x => x.Item == item);

                return Ok(HttpResponseModel<List<ItemLocation>>.Success(items));
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to retrieve locations for item {item}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while retrieving item locations: {ex.Message}"));
            }
        }

        [HttpGet("by-location/{location}")]
        public async Task<ActionResult<HttpResponseModel<List<ItemLocation>>>> GetByLocation(string location) {
            try {
                var items = await repository
                    .QueryAsync(x => x.Location == location);

                return Ok(HttpResponseModel<List<ItemLocation>>.Success(items));
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to retrieve items for location {location}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while retrieving location items: {ex.Message}"));
            }
        }

        #region Excel Import Methods

        [HttpGet("template")]
        public IActionResult DownloadTemplate() {
            try {
                // 创建模板数据
                var templateData = new List<object> {
                    // 示例数据行
                    new {
                        Item = "ITEM001",
                        Location = "WH01",
                        Account = "1000",
                        QuantityOnHand = "100.50000",
                        Rank = "1",
                        UnitOfMeasure = "PCS",
                        AccountUnit1 = "U001",
                        AccountUnit2 = "U002"
                    },
                    // 空行供用户填写
                    new {
                        Item = "",
                        Location = "",
                        Account = "",
                        QuantityOnHand = "",
                        Rank = "",
                        UnitOfMeasure = "",
                        AccountUnit1 = "",
                        AccountUnit2 = ""
                    }
                };

                using var stream = new MemoryStream();
                stream.SaveAs(templateData);
                var fileBytes = stream.ToArray();

                // 设置正确的响应头
                Response.Headers.Append("Content-Disposition", "attachment; filename=\"ItemLocationTemplate.xlsx\"");

                return File(fileBytes,
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    "ItemLocationTemplate.xlsx");
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to generate Excel template");
                return StatusCode(500, $"Failed to generate template: {ex.Message}");
            }
        }

        [HttpPost("validate")]
        public async Task<ActionResult<HttpResponseModel<ValidationResult>>> ValidateExcel(IFormFile? file,
            [FromForm] string mode = "Import") {
            try {
                if (file == null || file.Length == 0)
                    return BadRequest(HttpResponseModel<ValidationResult>.Error("No file uploaded"));

                // 解析模式
                var importMode = Enum.TryParse<ImportMode>(mode, true, out var parsedMode)
                    ? parsedMode
                    : ImportMode.Import;

                var validationResult = await ValidateExcelFile(file, importMode);
                return Ok(HttpResponseModel<ValidationResult>.Success(validationResult));
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to validate Excel file");
                return StatusCode(500,
                    HttpResponseModel<ValidationResult>.Error(
                        $"An error occurred while validating the file: {ex.Message}"));
            }
        }

        [HttpPost("import")]
        public async Task<ActionResult<HttpResponseModel<ImportResult>>> ImportExcel(IFormFile? file) {
            try {
                if (file == null || file.Length == 0)
                    return BadRequest(HttpResponseModel<ImportResult>.Error("No file uploaded"));

                var importResult = await ImportExcelFile(file, ImportMode.Import);
                return Ok(HttpResponseModel<ImportResult>.Success(importResult));
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to import Excel file");
                return StatusCode(500,
                    HttpResponseModel<ImportResult>.Error($"An error occurred while importing the file: {ex.Message}"));
            }
        }

        [HttpPost("update")]
        public async Task<ActionResult<HttpResponseModel<ImportResult>>> UpdateExcel(IFormFile? file) {
            try {
                if (file == null || file.Length == 0)
                    return BadRequest(HttpResponseModel<ImportResult>.Error("No file uploaded"));

                var updateResult = await ImportExcelFile(file, ImportMode.Update);
                return Ok(HttpResponseModel<ImportResult>.Success(updateResult));
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to update Excel file");
                return StatusCode(500,
                    HttpResponseModel<ImportResult>.Error($"An error occurred while updating the file: {ex.Message}"));
            }
        }

        #endregion

        #region Private Helper Methods

        private async Task<ValidationResult> ValidateExcelFile(IFormFile? file, ImportMode mode) {
            var result = new ValidationResult();
            var errors = new List<ValidationError>();

            try {
                using var stream = new MemoryStream();
                await file?.CopyToAsync(stream)!;
                stream.Position = 0;

                // 使用MiniExcel读取数据
                var rows = stream.Query<ItemLocationImportDto>().ToList();

                if (rows.Count == 0) {
                    errors.Add(new ValidationError {
                        RowNumber = 0,
                        Field = "File",
                        Message = "No data rows found in the Excel file",
                        Value = ""
                    });
                    result.Errors = errors;
                    return result;
                }

                result.TotalRows = rows.Count;

                // 获取现有的Item+Location组合用于重复检查
                var existingCombinations = await _db.Queryable<ItemLocation>()
                    .Select(x => new { x.Item, x.Location })
                    .ToListAsync();

                // 转换为元组列表
                var existingCombinationTuples = existingCombinations
                    .Select(x => (x.Item, x.Location))
                    .ToList();

                // 获取有效的Item、Location、UnitOfMeasure
                var validItems = await _db.Queryable<Item>()
                    .Select(x => x.Id)
                    .ToListAsync();

                var validLocations = await _db.Queryable<Location>()
                    .Select(x => x.Id)
                    .ToListAsync();

                var validUnits = await _db.Queryable<UnitOfMeasure>()
                    .Select(x => x.Id)
                    .ToListAsync();

                var combinationsInFile = new HashSet<string>();

                for (var i = 0; i < rows.Count; i++) {
                    var row = rows[i];
                    var rowNumber = i + 2; // Excel行号从2开始（跳过表头）
                    var rowErrors = ValidateRowData(row, rowNumber, existingCombinationTuples, validItems,
                        validLocations, validUnits, combinationsInFile, mode);
                    errors.AddRange(rowErrors);
                }

                result.Errors = errors;
                result.ErrorCount = errors.Count;

                // 计算有错误的行数（而不是错误总数）
                var errorRows = errors.Select(e => e.RowNumber).Distinct().Count();
                result.ValidCount = result.TotalRows - errorRows;

                return result;
            }
            catch (Exception ex) {
                logger.LogError(ex, "Error validating Excel file");
                errors.Add(new ValidationError {
                    RowNumber = 0,
                    Field = "File",
                    Message = $"Error reading Excel file: {ex.Message}",
                    Value = ""
                });
                result.Errors = errors;
                return result;
            }
        }

        private static List<ValidationError> ValidateRowData(ItemLocationImportDto row, int rowNumber,
            List<(string Item, string Location)> existingCombinations, List<string> validItems,
            List<string> validLocations,
            List<string> validUnits, HashSet<string> combinationsInFile, ImportMode mode) {
            var errors = new List<ValidationError>();

            // 获取单元格值
            var item = row.Item?.Trim() ?? "";
            var location = row.Location?.Trim() ?? "";
            var account = row.Account?.Trim() ?? "";
            var quantityText = row.QuantityOnHand?.Trim() ?? "";
            var rankText = row.Rank?.Trim() ?? "";
            var unitOfMeasure = row.UnitOfMeasure?.Trim() ?? "";
            var accountUnit1 = row.AccountUnit1?.Trim() ?? "";
            var accountUnit2 = row.AccountUnit2?.Trim() ?? "";

            // 验证必填字段
            if (string.IsNullOrEmpty(item))
                errors.Add(new ValidationError {
                    RowNumber = rowNumber,
                    Field = "Item",
                    Message = "Item is required",
                    Value = item
                });

            if (string.IsNullOrEmpty(location))
                errors.Add(new ValidationError {
                    RowNumber = rowNumber,
                    Field = "Location",
                    Message = "Location is required",
                    Value = location
                });

            // 验证Item存在性
            if (!string.IsNullOrEmpty(item) && !validItems.Contains(item))
                errors.Add(new ValidationError {
                    RowNumber = rowNumber,
                    Field = "Item",
                    Message = "Item not found in system",
                    Value = item
                });

            // 验证Location存在性
            if (!string.IsNullOrEmpty(location) && !validLocations.Contains(location))
                errors.Add(new ValidationError {
                    RowNumber = rowNumber,
                    Field = "Location",
                    Message = "Location not found in system",
                    Value = location
                });

            // 验证Item+Location组合唯一性
            if (!string.IsNullOrEmpty(item) && !string.IsNullOrEmpty(location)) {
                var combinationKey = $"{item}_{location}";

                // 在导入模式下检查数据库重复，在更新模式下跳过
                if (mode == ImportMode.Import &&
                    existingCombinations.Any(x => x.Item == item && x.Location == location))
                    errors.Add(new ValidationError {
                        RowNumber = rowNumber,
                        Field = "Item+Location",
                        Message = "Item+Location combination already exists in database",
                        Value = combinationKey
                    });

                // 检查文件内重复
                if (!combinationsInFile.Add(combinationKey))
                    errors.Add(new ValidationError {
                        RowNumber = rowNumber,
                        Field = "Item+Location",
                        Message = "Duplicate Item+Location combination in file",
                        Value = combinationKey
                    });
            }

            // 验证数量
            if (!string.IsNullOrEmpty(quantityText) && !decimal.TryParse(quantityText, out _))
                errors.Add(new ValidationError {
                    RowNumber = rowNumber,
                    Field = "QuantityOnHand",
                    Message = "QuantityOnHand must be a valid number",
                    Value = quantityText
                });

            // 验证等级
            if (!string.IsNullOrEmpty(rankText) && !byte.TryParse(rankText, out _))
                errors.Add(new ValidationError {
                    RowNumber = rowNumber,
                    Field = "Rank",
                    Message = "Rank must be a valid number (0-255)",
                    Value = rankText
                });

            // 验证Unit of Measure存在性
            if (!string.IsNullOrEmpty(unitOfMeasure) && !validUnits.Contains(unitOfMeasure))
                errors.Add(new ValidationError {
                    RowNumber = rowNumber,
                    Field = "UnitOfMeasure",
                    Message = "Unit of Measure not found in system",
                    Value = unitOfMeasure
                });

            return errors;
        }

        private async Task<ImportResult> ImportExcelFile(IFormFile? file, ImportMode mode = ImportMode.Import) {
            var result = new ImportResult();
            var messages = new List<string>();

            logger.LogInformation("Starting Excel import for file: {FileName}", file?.FileName);

            try {
                using var stream = new MemoryStream();
                await file.CopyToAsync(stream);
                stream.Position = 0;

                // 使用MiniExcel读取数据
                var rows = stream.Query<ItemLocationImportDto>().ToList();
                result.TotalRows = rows.Count;
                logger.LogInformation("Read {RowCount} rows from Excel file", rows.Count);

                if (rows.Count == 0) {
                    result.Messages.Add("No data rows found in the Excel file");
                    return result;
                }

                // 获取现有的Item+Location组合用于重复检查
                var existingCombinations = await _db.Queryable<ItemLocation>()
                    .Select(x => new { x.Item, x.Location })
                    .ToListAsync();

                var itemsToInsert = new List<ItemLocation>();
                var itemsToUpdate = new List<ItemLocation>();
                var skippedCount = 0;
                var invalidCount = 0;

                // 首先验证所有行，获取有效和无效的记录
                var validationResult = await ValidateExcelFile(file, mode);

                // 只处理有效的记录
                var validRows = new List<(ItemLocationImportDto row, int rowNumber)>();
                var combinationsInFile = new HashSet<string>();

                for (var i = 0; i < rows.Count; i++) {
                    var row = rows[i];
                    var rowNumber = i + 2;

                    // 检查这一行是否有验证错误
                    var hasErrors = validationResult.Errors.Any(e => e.RowNumber == rowNumber);

                    if (!hasErrors) {
                        var item = row.Item?.Trim() ?? "";
                        var location = row.Location?.Trim() ?? "";
                        var combinationKey = $"{item}_{location}";

                        // 检查文件内重复（在有效记录中）
                        if (!combinationsInFile.Add(combinationKey)) {
                            messages.Add($"Row {rowNumber}: Duplicate in file, skipped");
                            skippedCount++;
                            continue;
                        }

                        validRows.Add((row, rowNumber));
                    }
                    else {
                        var errorMessages = validationResult.Errors
                            .Where(e => e.RowNumber == rowNumber)
                            .Select(e => e.Message);
                        messages.Add(
                            $"Row {rowNumber}: Skipped due to validation errors - {string.Join(", ", errorMessages)}");
                        skippedCount++;
                    }
                }

                logger.LogInformation("Processing {ValidCount} valid rows out of {TotalCount} total rows",
                    validRows.Count, rows.Count);

                // 处理有效的记录
                foreach (var (row, rowNumber) in validRows) {
                    try {
                        var item = row.Item?.Trim() ?? "";
                        var location = row.Location?.Trim() ?? "";
                        var combinationExists = existingCombinations.Any(x => x.Item == item && x.Location == location);

                        if (mode == ImportMode.Import) {
                            // 导入模式：只处理不存在的Item+Location组合
                            if (combinationExists) {
                                messages.Add(
                                    $"Row {rowNumber}: Item+Location combination '{item}+{location}' already exists, skipped");
                                skippedCount++;
                                continue;
                            }

                            // 创建新ItemLocation对象
                            var itemLocation = CreateItemLocationFromRowData(row);
                            if (itemLocation != null) {
                                itemsToInsert.Add(itemLocation);
                                logger.LogDebug("Successfully prepared item location for insert: {Item}+{Location}",
                                    item, location);
                            }
                            else {
                                messages.Add($"Row {rowNumber}: Failed to create item location from data");
                                invalidCount++;
                            }
                        }
                        else if (mode == ImportMode.Update) {
                            // 更新模式：只处理已存在的Item+Location组合
                            if (!combinationExists) {
                                messages.Add(
                                    $"Row {rowNumber}: Item+Location combination '{item}+{location}' does not exist, skipped");
                                skippedCount++;
                                continue;
                            }

                            // 创建更新ItemLocation对象
                            var itemLocation = CreateItemLocationFromRowData(row);
                            if (itemLocation != null) {
                                itemsToUpdate.Add(itemLocation);
                                logger.LogDebug("Successfully prepared item location for update: {Item}+{Location}",
                                    item, location);
                            }
                            else {
                                messages.Add($"Row {rowNumber}: Failed to create item location from data");
                                invalidCount++;
                            }
                        }
                    }
                    catch (Exception ex) {
                        logger.LogError(ex, "Failed to process row {RowNumber}", rowNumber);
                        messages.Add($"Row {rowNumber}: Failed to process - {ex.Message}");
                        invalidCount++;
                    }
                }

                // 执行批量插入
                if (itemsToInsert.Count > 0) {
                    logger.LogInformation("Inserting {Count} item locations", itemsToInsert.Count);
                    try {
                        var insertCount = await _db.Insertable(itemsToInsert).ExecuteCommandAsync();
                        result.SuccessCount += insertCount;
                        logger.LogInformation("Successfully inserted {Count} item locations", insertCount);
                    }
                    catch (Exception ex) {
                        logger.LogError(ex, "Failed to insert item locations");
                        messages.Add($"Database error during insert: {ex.Message}");
                        result.ErrorCount += itemsToInsert.Count;
                    }
                }

                // 执行批量更新
                if (itemsToUpdate.Count > 0) {
                    logger.LogInformation("Updating {Count} item locations", itemsToUpdate.Count);
                    try {
                        var updateSuccessCount = 0;
                        foreach (var item in itemsToUpdate) {
                            try {
                                var updateCount = await _db.Updateable(item)
                                    .Where(x => x.Item == item.Item && x.Location == item.Location)
                                    .ExecuteCommandAsync();
                                if (updateCount > 0) {
                                    updateSuccessCount++;
                                }
                                else {
                                    messages.Add($"Failed to update {item.Item}+{item.Location}: Record not found");
                                }
                            }
                            catch (Exception ex) {
                                logger.LogError(ex, "Failed to update item location {Item}+{Location}", item.Item,
                                    item.Location);
                                messages.Add($"Failed to update {item.Item}+{item.Location}: {ex.Message}");
                                result.ErrorCount++;
                            }
                        }

                        result.SuccessCount += updateSuccessCount;
                        logger.LogInformation("Successfully updated {Count} item locations", updateSuccessCount);
                    }
                    catch (Exception ex) {
                        logger.LogError(ex, "Failed to update item locations");
                        messages.Add($"Database error during update: {ex.Message}");
                        result.ErrorCount += itemsToUpdate.Count;
                    }
                }

                result.SkippedCount = skippedCount;
                result.ErrorCount = invalidCount;
                result.Messages = messages;

                logger.LogInformation("Import completed. Success: {Success}, Skipped: {Skipped}, Errors: {Errors}",
                    result.SuccessCount, result.SkippedCount, result.ErrorCount);

                return result;
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to import Excel file");
                result.Messages.Add($"Import failed: {ex.Message}");
                result.ErrorCount = result.TotalRows;
                return result;
            }
        }

        private ItemLocation? CreateItemLocationFromRowData(ItemLocationImportDto row) {
            // 获取并截断字段数据以符合数据库约束
            var item = TruncateString(row.Item?.Trim() ?? "", 30);
            var location = TruncateString(row.Location?.Trim() ?? "", 10);
            var account = TruncateString(row.Account?.Trim() ?? "", 8);
            var quantityText = row.QuantityOnHand?.Trim() ?? "";
            var rankText = row.Rank?.Trim() ?? "";
            var unitOfMeasure = TruncateString(row.UnitOfMeasure?.Trim() ?? "", 3);
            var accountUnit1 = TruncateString(row.AccountUnit1?.Trim() ?? "", 4);
            var accountUnit2 = TruncateString(row.AccountUnit2?.Trim() ?? "", 4);

            logger.LogDebug("CreateItemLocationFromRowData: Item='{Item}', Location='{Location}'", item, location);

            if (string.IsNullOrEmpty(item) || string.IsNullOrEmpty(location)) {
                logger.LogWarning("Item or Location is empty, cannot create ItemLocation");
                return null;
            }

            decimal.TryParse(quantityText, out var quantity);
            byte.TryParse(rankText, out var rank);

            // 获取当前用户信息
            var currentUser = GetCurrentUser();
            return new ItemLocation {
                Item = item,
                Location = location,
                Account = string.IsNullOrEmpty(account) ? null : account,
                QuantityOnHand = quantity,
                Rank = rank == 0 ? null : rank,
                UnitOfMeasure = string.IsNullOrEmpty(unitOfMeasure) ? null : unitOfMeasure,
                AccountUnit1 = string.IsNullOrEmpty(accountUnit1) ? null : accountUnit1,
                AccountUnit2 = string.IsNullOrEmpty(accountUnit2) ? null : accountUnit2,
                CreatedDate = DateTime.Now,
                RecordDate = DateTime.Now,
                CreatedBy = currentUser,
                ModifiedBy = currentUser
            };
        }

        private static string TruncateString(string input, int maxLength) {
            return string.IsNullOrEmpty(input) ? input : input.Length <= maxLength ? input : input[..maxLength];
        }

        private string GetCurrentUser() {
            return User.FindFirst(ClaimTypes.Name)?.Value ?? "System";
        }

        #endregion
    }
}