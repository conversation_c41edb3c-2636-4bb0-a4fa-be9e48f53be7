using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using SqlSugar;
using Microsoft.AspNetCore.Authorization;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class OrderShippingController(
        ILogger<OrderShippingController> logger,
        ISqlSugarClient db,
        IRepository<CoItem, int> coItemRepository,
        IRepository<CustomerOrder, int> coRepository)
        : ControllerBase {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        // GET: api/ordershipping/co-list
        [HttpGet("co-list")]
        public async Task<ActionResult<HttpResponseModel<List<CoInfoDto>>>> GetCoList() {
            try {
                // 获取所有客户订单号
                var coList = await _db.Queryable<CustomerOrder>()
                    .OrderByDescending(x => x.CoNum)
                    .Select(x => new CoInfoDto {
                        CoNum = x.CoNum,
                        CustNum = x.CustNum,
                        CoDate = x.CoDate,
                        Status = x.Status
                    })
                    .ToListAsync();

                return HttpResponseModel<List<CoInfoDto>>.Success(coList);
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to retrieve CO list");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while retrieving CO list: {ex.Message}"));
            }
        }

        // POST: api/ordershipping/process-sp
        [HttpPost("process-sp")]
        public async Task<ActionResult<HttpResponseModel<List<OrderShippingDto>>>> ProcessShippingSP(
            [FromBody] List<OrderShippingDto> shippingItems) {
            if (shippingItems == null || !shippingItems.Any()) {
                return BadRequest(HttpResponseModel<object>.Error("No shipping items provided"));
            }

            try {
                var results = new List<OrderShippingDto>();

                foreach (var item in shippingItems) {
                    var CoNumP = new SugarParameter("@co_num", item.CoNum);
                    var CoLineP = new SugarParameter("@co_line", item.CoLine);
                    var TransDateP = new SugarParameter("@trans_date", item.TransDate);
                    var LocationP = new SugarParameter("@loc", item.Location);
                    var QtyShippingP = new SugarParameter("@qty_shipping", item.QtyShipping);
                    var ReasonCodeP = new SugarParameter("@reason_code", item.ReasonCode);
                    var UnitOfMeasureP = new SugarParameter("@u_m", item.UnitOfMeasure);
                    var InfoP = new SugarParameter("@info", null, true); // 设置为output

                    await _db.Ado.UseStoredProcedure().GetDataTableAsync("OrderShippingSP",
                        CoNumP, CoLineP, TransDateP, LocationP, QtyShippingP, ReasonCodeP, UnitOfMeasureP, InfoP);

                    // 更新 Info 字段
                    item.Info = InfoP.Value?.ToString();
                    results.Add(item);
                }

                return Ok(HttpResponseModel<List<OrderShippingDto>>.Success(results,
                    "Stored procedure executed successfully"));
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to execute stored procedure");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while executing stored procedure: {ex.Message}"));
            }
        }
    }
}