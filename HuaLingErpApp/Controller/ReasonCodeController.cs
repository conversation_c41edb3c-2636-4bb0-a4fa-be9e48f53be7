using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using System;
using System.Threading.Tasks;
using System.Linq.Expressions;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/reasoncode")]
    [Authorize]
    public class ReasonCodeController(
        IRepository<ReasonCode, int> repository,
        ILogger<ReasonCodeController> logger,
        ISqlSugarClient db)
        : ControllerBase {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<ReasonCode>>>> GetAll() {
            try {
                var items = await repository.GetDataAsync();
                return Ok(HttpResponseModel<List<ReasonCode>>.Success(items));
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to retrieve reason code list");
                return StatusCode(500,
                    HttpResponseModel<List<ReasonCode>>.Error(
                        $"An error occurred while retrieving reason codes: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<ReasonCode>>> GetById(int id) {
            try {
                var item = await repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<ReasonCode>.Error("Reason code not found"));
                }

                return Ok(HttpResponseModel<ReasonCode>.Success(item));
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to retrieve reason code {id}");
                return StatusCode(500,
                    HttpResponseModel<ReasonCode>.Error(
                        $"An error occurred while retrieving the reason code: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<ReasonCode>>> Create([FromBody] ReasonCode model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                // Check if reason code with the same code and class already exists
                var exists = await _db.Queryable<ReasonCode>()
                    .AnyAsync(rc => rc.ReasonCodeValue == model.ReasonCodeValue &&
                                    rc.ReasonClass == model.ReasonClass);
                if (exists) {
                    return BadRequest(
                        HttpResponseModel<object>.Error("Reason code with the same code and class already exists"));
                }


                var result = await repository.AddAndReturnAsync(model);
                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<ReasonCode>.Success(result, "Reason code created successfully")
                );
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to create reason code");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the reason code: {ex.Message}"));
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(int id, [FromBody] ReasonCode model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                if (id != model.Id) {
                    return BadRequest(HttpResponseModel<object>.Error("ID mismatch"));
                }

                var existing = await repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Reason code not found"));
                }

                // Check if another reason code with same code and class already exists
                var exists = await _db.Queryable<ReasonCode>()
                    .AnyAsync(rc => rc.Id != id &&
                                    rc.ReasonCodeValue == model.ReasonCodeValue &&
                                    rc.ReasonClass == model.ReasonClass);
                if (exists) {
                    return BadRequest(
                        HttpResponseModel<object>.Error(
                            "Another reason code with the same code and class already exists"));
                }

                model.CreatedDate = existing.CreatedDate;
                model.CreatedBy = existing.CreatedBy;

                var success = await repository.UpdateAsync(model);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to update reason code"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to update reason code {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the reason code: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(int id) {
            try {
                var existing = await repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Reason code not found"));
                }

                var success = await repository.DeleteAsync(id);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete reason code"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to delete reason code {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the reason code: {ex.Message}"));
            }
        }

        [HttpPost("batch")]
        public async Task<IActionResult> BatchDelete([FromBody] List<int>? ids) {
            try {
                if (ids == null || !ids.Any()) {
                    return BadRequest(HttpResponseModel<object>.Error("No IDs provided for deletion"));
                }

                var existingItems = await repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = existingItems.Count;

                if (existingCount != ids.Count) {
                    return NotFound(HttpResponseModel<object>.Error("One or more reason codes not found"));
                }

                var success = await repository.DeleteBatchAsync(ids);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete selected reason codes"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to batch delete reason codes");
                return StatusCode(500,
                    HttpResponseModel<object>.Error(
                        $"An error occurred while deleting the reason codes: {ex.Message}"));
            }
        }

        [HttpGet("by-class/{reasonClass}")]
        public async Task<ActionResult<HttpResponseModel<List<ReasonCode>>>> GetByReasonClass(string reasonClass) {
            try {
                var items = await _db.Queryable<ReasonCode>()
                    .Where(rc => rc.ReasonClass == reasonClass)
                    .OrderBy(rc => rc.ReasonCodeValue)
                    .ToListAsync();

                return Ok(HttpResponseModel<List<ReasonCode>>.Success(items));
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to retrieve reason codes for class {reasonClass}");
                return StatusCode(500,
                    HttpResponseModel<List<ReasonCode>>.Error(
                        $"An error occurred while retrieving reason codes: {ex.Message}"));
            }
        }
    }
}