using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using SqlSugar;
using Microsoft.AspNetCore.Authorization;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/taxcode")]
    [Authorize]
    public class TaxCodeController(
        IRepository<TaxCode, string> repository,
        ILogger<TaxCodeController> logger,
        ISqlSugarClient db)
        : ControllerBase {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<TaxCode>>>> GetAll() {
            try {
                var items = await repository.GetDataAsync();
                return Ok(HttpResponseModel<List<TaxCode>>.Success(items));
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to retrieve tax code list");
                return StatusCode(500,
                    HttpResponseModel<List<TaxCode>>.Error(
                        $"An error occurred while retrieving tax codes: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<TaxCode>>> GetById(string id) {
            try {
                var item = await repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<TaxCode>.Error("Tax code not found"));
                }

                return Ok(HttpResponseModel<TaxCode>.Success(item));
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to retrieve tax code {id}");
                return StatusCode(500,
                    HttpResponseModel<TaxCode>.Error($"An error occurred while retrieving the tax code: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<TaxCode>>> Create([FromBody] TaxCode model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var exists = await repository.AnyAsync(p => p.Id == model.Id);
                if (exists) {
                    return BadRequest(HttpResponseModel<object>.Error("Tax code already exists"));
                }

                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await repository.AddAndReturnAsync(model);
                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<TaxCode>.Success(result, "Tax code created successfully")
                );
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to create tax code");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the tax code: {ex.Message}"));
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] TaxCode model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                if (id != model.Id) {
                    return BadRequest(HttpResponseModel<object>.Error("ID mismatch"));
                }

                var existing = await repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Tax code not found"));
                }

                // Preserve original creation audit fields
                model.CreatedDate = existing.CreatedDate;
                model.CreatedBy = existing.CreatedBy;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                var success = await repository.UpdateAsync(model);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to update tax code"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to update tax code {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the tax code: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id) {
            try {
                var existing = await repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Tax code not found"));
                }

                var success = await repository.DeleteAsync(id);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete tax code"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to delete tax code {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the tax code: {ex.Message}"));
            }
        }

        [HttpPost("batch")]
        public async Task<IActionResult> BatchDelete([FromBody] List<string> ids) {
            try {
                if (ids == null || !ids.Any()) {
                    return BadRequest(HttpResponseModel<object>.Error("No IDs provided for deletion"));
                }

                var table = await repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count) {
                    return NotFound(HttpResponseModel<object>.Error("One or more tax code not found"));
                }

                var success = await repository.DeleteBatchAsync(ids);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete selected tax codes"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to batch delete tax codes");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the tax codes: {ex.Message}"));
            }
        }
    }
}