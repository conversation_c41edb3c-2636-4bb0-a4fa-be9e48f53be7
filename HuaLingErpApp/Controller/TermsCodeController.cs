using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using System;
using System.Threading.Tasks;
using System.Linq.Expressions;
using SqlSugar;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Authorization;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/termscode")]
    [Authorize]
    public class TermsCodeController(
        IRepository<TermsCode, string> repository,
        ILogger<TermsCodeController> logger,
        ISqlSugarClient db)
        : ControllerBase {
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<TermsCode>>>> GetAll() {
            try {
                var items = await repository.GetDataAsync();
                return Ok(HttpResponseModel<List<TermsCode>>.Success(items));
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to retrieve terms code list");
                return StatusCode(500,
                    HttpResponseModel<List<TermsCode>>.Error(
                        $"An error occurred while retrieving terms codes: {ex.Message}"));
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<TermsCode>>> GetById(string id) {
            try {
                var item = await repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<TermsCode>.Error("Terms code not found"));
                }

                return Ok(HttpResponseModel<TermsCode>.Success(item));
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to retrieve terms code {id}");
                return StatusCode(500,
                    HttpResponseModel<TermsCode>.Error(
                        $"An error occurred while retrieving the terms code: {ex.Message}"));
            }
        }

        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<TermsCode>>> Create([FromBody] TermsCode model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var exists = await repository.AnyAsync(p => p.Id == model.Id);
                if (exists) {
                    return BadRequest(HttpResponseModel<object>.Error("Terms code already exists"));
                }

                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await repository.AddAndReturnAsync(model);
                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<TermsCode>.Success(result, "Terms code created successfully")
                );
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to create terms code");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the terms code: {ex.Message}"));
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] TermsCode model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                if (id != model.Id) {
                    return BadRequest(HttpResponseModel<object>.Error("ID mismatch"));
                }

                var existing = await repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Terms code not found"));
                }

                // Preserve original creation audit fields
                model.CreatedDate = existing.CreatedDate;
                model.CreatedBy = existing.CreatedBy;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                var success = await repository.UpdateAsync(model);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to update terms code"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to update terms code {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the terms code: {ex.Message}"));
            }
        }

        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(string id) {
            try {
                var existing = await repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Terms code not found"));
                }

                var success = await repository.DeleteAsync(id);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete terms code"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, $"Failed to delete terms code {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the terms code: {ex.Message}"));
            }
        }

        [HttpPost("batch")]
        public async Task<IActionResult> BatchDelete([FromBody] List<string>? ids) {
            try {
                if (ids == null || !ids.Any()) {
                    return BadRequest(HttpResponseModel<object>.Error("No IDs provided for deletion"));
                }

                var table = await repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count) {
                    return NotFound(HttpResponseModel<object>.Error("One or more terms code not found"));
                }

                var success = await repository.DeleteBatchAsync(ids);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete selected terms codes"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                logger.LogError(ex, "Failed to batch delete terms codes");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the terms codes: {ex.Message}"));
            }
        }
    }
}