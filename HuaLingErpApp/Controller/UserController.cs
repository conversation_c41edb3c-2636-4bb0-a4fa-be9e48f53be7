using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HuaLingErpApp.Services;
using HuaLingErpApp.Shared.Models;
using HuaLingErpApp.Data;
using System.Security.Claims;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/users")]
    [Authorize(Roles = "Administrator")]
    public class UserController : ControllerBase {
        private readonly IAuthService _authService;
        private readonly IRepository<Role, int> _roleRepository;
        private readonly ILogger<UserController> _logger;

        public UserController(
            IAuthService authService,
            IRepository<Role, int> roleRepository,
            ILogger<UserController> logger) {
            _authService = authService;
            _roleRepository = roleRepository;
            _logger = logger;
        }

        /// <summary>
        /// Get all users (Administrator only)
        /// </summary>
        /// <returns>List of all users</returns>
        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<UserInfo>>>> GetAllUsers() {
            try {
                var users = await _authService.GetAllUsersAsync();
                return Ok(HttpResponseModel<List<UserInfo>>.Success(users));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while getting all users");
                return StatusCode(500,
                    HttpResponseModel<List<UserInfo>>.Error($"An error occurred while retrieving users: {ex.Message}"));
            }
        }

        /// <summary>
        /// Get user by ID (Administrator only)
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User information</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<UserInfo>>> GetUserById(int id) {
            try {
                var user = await _authService.GetUserInfoAsync(id);
                if (user == null) {
                    return NotFound(HttpResponseModel<UserInfo>.Error("User not found"));
                }

                return Ok(HttpResponseModel<UserInfo>.Success(user));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while getting user by ID");
                return StatusCode(500,
                    HttpResponseModel<UserInfo>.Error($"An error occurred while retrieving user: {ex.Message}"));
            }
        }

        /// <summary>
        /// Create new user (Administrator only)
        /// </summary>
        /// <param name="request">Create user request</param>
        /// <returns>Create user response</returns>
        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<object>>> CreateUser([FromBody] CreateUserRequest request) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Invalid request data"));
                }

                var currentUserName = User.FindFirst(ClaimTypes.Name)?.Value ?? "System";
                var result = await _authService.CreateUserAsync(request, currentUserName);

                if (result) {
                    return Ok(HttpResponseModel<object>.Success(null, "User created successfully"));
                }
                else {
                    return BadRequest(
                        HttpResponseModel<object>.Error("Failed to create user. Username or email may already exist."));
                }
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while creating user");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating user: {ex.Message}"));
            }
        }

        /// <summary>
        /// Update user (Administrator only)
        /// </summary>
        /// <param name="id">User ID</param>
        /// <param name="request">Update user request</param>
        /// <returns>Update user response</returns>
        [HttpPut("{id}")]
        public async Task<ActionResult<HttpResponseModel<object>>> UpdateUser(int id,
            [FromBody] UpdateUserRequest request) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Invalid request data"));
                }

                request.Id = id;
                var currentUserName = User.FindFirst(ClaimTypes.Name)?.Value ?? "System";
                var result = await _authService.UpdateUserAsync(request, currentUserName);

                if (result) {
                    return Ok(HttpResponseModel<object>.Success(null, "User updated successfully"));
                }
                else {
                    return BadRequest(
                        HttpResponseModel<object>.Error("Failed to update user. Email may already be in use."));
                }
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while updating user");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating user: {ex.Message}"));
            }
        }

        /// <summary>
        /// Delete user (Administrator only)
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>Delete user response</returns>
        [HttpDelete("{id}")]
        public async Task<ActionResult<HttpResponseModel<object>>> DeleteUser(int id) {
            try {
                // Prevent deleting current user
                var currentUserIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (currentUserIdClaim != null && int.TryParse(currentUserIdClaim.Value, out int currentUserId)) {
                    if (currentUserId == id) {
                        return BadRequest(HttpResponseModel<object>.Error("Cannot delete your own account"));
                    }
                }

                var result = await _authService.DeleteUserAsync(id);

                if (result) {
                    return Ok(HttpResponseModel<object>.Success(null, "User deleted successfully"));
                }
                else {
                    return BadRequest(HttpResponseModel<object>.Error("Failed to delete user"));
                }
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while deleting user");
                return StatusCode(500, HttpResponseModel<object>.Error("An error occurred while deleting user"));
            }
        }

        /// <summary>
        /// Delete multiple users (Administrator only)
        /// </summary>
        /// <param name="request">Batch delete request containing user IDs</param>
        /// <returns>Batch delete response</returns>
        [HttpDelete("batch")]
        public async Task<IActionResult> DeleteUsers([FromBody] List<int>? ids) {
            try {
                if (ids is null || ids.Count == 0) {
                    return BadRequest(HttpResponseModel<object>.Error("No user IDs provided"));
                }

                // Get current user ID to prevent self-deletion
                var currentUserIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                int? currentUserId = null;
                if (currentUserIdClaim != null && int.TryParse(currentUserIdClaim.Value, out int userId)) {
                    currentUserId = userId;
                }

                // Check if current user is in the deletion list
                if (currentUserId.HasValue && ids.Contains(currentUserId.Value)) {
                    return BadRequest(HttpResponseModel<object>.Error("Cannot delete your own account"));
                }

                var table = await _authService.GetAllUsersAsync();
                var existingCount = table.Count(x => ids.Contains(x.Id));
                if (existingCount != ids.Count) {
                    return NotFound(HttpResponseModel<object>.Error("One or more users not found"));
                }

                var success = await _authService.DeleteBatchUsersAsync(ids);
                if (!success) {
                    return StatusCode(500,
                        HttpResponseModel<object>.Error("Failed to delete one or more users"));
                }

                return NoContent();
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to delete users in batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting users in batch: {ex.Message}"));
            }
        }


        /// <summary>
        /// Get all roles (Administrator only)
        /// </summary>
        /// <returns>List of all roles</returns>
        [HttpGet("roles")]
        public async Task<ActionResult<HttpResponseModel<List<Role>>>> GetAllRoles() {
            try {
                var roles = await _roleRepository.GetDataAsync();
                return Ok(HttpResponseModel<List<Role>>.Success(roles));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while getting all roles");
                return StatusCode(500,
                    HttpResponseModel<List<Role>>.Error($"An error occurred while retrieving roles: {ex.Message}"));
            }
        }

        /// <summary>
        /// Admin reset user password (Administrator only)
        /// </summary>
        [HttpPost("{id}/reset-password")]
        [Authorize(Roles = "Administrator")]
        public async Task<ActionResult<HttpResponseModel<object>>> AdminResetPassword(int id,
            [FromBody] AdminResetPasswordRequest request) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Invalid request data"));
                }

                request.UserId = id;
                var currentUserName = User.FindFirst(ClaimTypes.Name)?.Value ?? "System";
                var result = await _authService.AdminResetPasswordAsync(request, currentUserName);

                if (result) {
                    return Ok(HttpResponseModel<object>.Success(null, "Password reset successfully"));
                }
                else {
                    return BadRequest(HttpResponseModel<object>.Error("Failed to reset password"));
                }
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while resetting password");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while resetting password: {ex.Message}"));
            }
        }
    }
}