using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using SqlSugar;

namespace HuaLingErpApp.Controller {
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class VendorsController(
        IRepository<Vendor, int> repository,
        ILogger<VendorsController> logger,
        ISqlSugarClient db)
        : ControllerBase {
        private readonly IRepository<Vendor, int> _repository = repository;
        private readonly ILogger<VendorsController> _logger = logger;
        private readonly ISqlSugarClient _db = db.AsTenant().GetConnection("Default");

        // GET: api/vendors
        [HttpGet]
        public async Task<ActionResult<HttpResponseModel<List<Vendor>>>> GetAll() {
            try {
                var items = await _repository.GetDataAsync();
                return Ok(HttpResponseModel<List<Vendor>>.Success(items));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to retrieve vendors list");
                return StatusCode(500,
                    HttpResponseModel<List<Vendor>>.Error($"An error occurred while retrieving vendors: {ex.Message}"));
            }
        }

        // GET: api/vendors/5
        [HttpGet("{id}")]
        public async Task<ActionResult<HttpResponseModel<Vendor>>> GetById(int id) {
            try {
                var item = await _repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<Vendor>.Error("Vendor not found"));
                }

                return Ok(HttpResponseModel<Vendor>.Success(item));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to retrieve vendor {id}");
                return StatusCode(500,
                    HttpResponseModel<Vendor>.Error("An error occurred while retrieving the vendor"));
            }
        }

        // GET: api/vendors/byvendnum/V123
        [HttpGet("byvendnum/{vendNum}")]
        public async Task<ActionResult<HttpResponseModel<Vendor>>> GetByVendNum(string vendNum) {
            try {
                var item = await _repository.QueryAsync(v => v.VendNum == vendNum);
                if (item == null || item.Count == 0) {
                    return NotFound(HttpResponseModel<Vendor>.Error("Vendor not found"));
                }

                return Ok(HttpResponseModel<Vendor>.Success(item.First()));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to retrieve vendor {vendNum}");
                return StatusCode(500,
                    HttpResponseModel<Vendor>.Error($"An error occurred while retrieving the vendor: {ex.Message}"));
            }
        }

        // POST: api/vendors
        [HttpPost]
        public async Task<ActionResult<HttpResponseModel<Vendor>>> Create([FromBody] Vendor model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                // Check if vendor number already exists
                var exists = await _repository.AnyAsync(v => v.VendNum == model.VendNum);
                if (exists) {
                    return BadRequest(HttpResponseModel<object>.Error("Vendor number already exists"));
                }

                // Audit fields (CreatedBy, CreatedDate, ModifiedBy, RecordDate) will be automatically set by AuditableRepository
                var result = await _repository.AddAndReturnAsync(model);

                return CreatedAtAction(
                    nameof(GetById),
                    new { id = result.Id },
                    HttpResponseModel<Vendor>.Success(result, "Vendor created successfully")
                );
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to create vendor");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while creating the vendor: {ex.Message}"));
            }
        }

        // PUT: api/vendors/5
        [HttpPut("{id}")]
        public async Task<ActionResult<HttpResponseModel<bool>>> Update(int id, [FromBody] Vendor model) {
            try {
                if (!ModelState.IsValid) {
                    return BadRequest(HttpResponseModel<object>.Error("Request data validation failed"));
                }

                var existing = await _repository.GetByIdAsync(id);
                if (existing == null) {
                    return NotFound(HttpResponseModel<object>.Error("Vendor not found"));
                }

                // Check if vendor number is used by other records
                var exists = await _repository.AnyAsync(v => v.Id != id && v.VendNum == model.VendNum);
                if (exists) {
                    return BadRequest(
                        HttpResponseModel<object>.Error("Vendor number is already in use by another record"));
                }

                // Update fields
                existing.VendNum = model.VendNum;
                existing.Name = model.Name;
                existing.CurrencyCode = model.CurrencyCode;
                existing.TermsCode = model.TermsCode;
                existing.TaxCode = model.TaxCode;
                existing.Contact = model.Contact;
                existing.Phone = model.Phone;
                existing.Email = model.Email;
                existing.Address = model.Address;

                // ModifiedBy and RecordDate will be automatically set by AuditableRepository
                // CreatedBy and CreatedDate will be preserved
                var success = await _repository.UpdateAsync(existing);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to update vendor"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Vendor updated successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to update vendor {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while updating the vendor: {ex.Message}"));
            }
        }

        // DELETE: api/vendors/5
        [HttpDelete("{id}")]
        public async Task<ActionResult<HttpResponseModel<bool>>> Delete(int id) {
            try {
                var item = await _repository.GetByIdAsync(id);
                if (item == null) {
                    return NotFound(HttpResponseModel<object>.Error("Vendor not found"));
                }

                // Add any business logic checks here before deletion
                // For example, check if the vendor has associated purchase orders

                var success = await _repository.DeleteAsync(id);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete vendor"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Vendor deleted successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, $"Failed to delete vendor {id}");
                return StatusCode(500,
                    HttpResponseModel<object>.Error($"An error occurred while deleting the vendor: {ex.Message}"));
            }
        }

        // DELETE: api/vendors/batch
        [HttpDelete("batch")]
        public async Task<ActionResult<HttpResponseModel<bool>>> DeleteBatch([FromBody] List<int>? ids) {
            try {
                if (ids is null || ids.Count == 0) {
                    return BadRequest(HttpResponseModel<object>.Error("No IDs provided for deletion"));
                }

                // Add any business logic checks here before batch deletion
                var table = await _repository
                    .QueryAsync(x => ids.Contains(x.Id));
                var existingCount = table.Count;

                if (existingCount != ids.Count) {
                    return NotFound(HttpResponseModel<object>.Error("One or more vendors not found"));
                }

                var success = await _repository.DeleteBatchAsync(ids);
                if (!success) {
                    return StatusCode(500, HttpResponseModel<bool>.Error("Failed to delete vendors"));
                }

                return Ok(HttpResponseModel<bool>.Success(true, "Vendors deleted successfully"));
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to delete vendors in batch");
                return StatusCode(500,
                    HttpResponseModel<object>.Error("An error occurred while deleting vendors"));
            }
        }

        // GET: api/vendors/search?searchText=...
        [HttpGet("search")]
        public async Task<ActionResult<HttpResponseModel<List<Vendor>>>> Search([FromQuery] string searchText) {
            try {
                if (string.IsNullOrWhiteSpace(searchText)) {
                    // Return top 10 or an empty list if no search text
                    var topVendors = await _repository.GetDataAsync();
                    return HttpResponseModel<List<Vendor>>.Success(topVendors);
                }

                var items = await _repository.QueryAsync(v =>
                    v.VendNum.Contains(searchText) ||
                    (v.Name != null && v.Name.Contains(searchText))
                );
                return HttpResponseModel<List<Vendor>>.Success(items);
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Failed to search for vendors");
                return StatusCode(500,
                    HttpResponseModel<List<Vendor>>.Error(
                        $"An error occurred while searching for vendors: {ex.Message}"));
            }
        }
    }
}