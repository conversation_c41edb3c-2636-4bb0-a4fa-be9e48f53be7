using SqlSugar;
using System.Linq.Expressions;
using System.Security.Claims;
using HuaLingErpApp.Shared.Models;
using Microsoft.AspNetCore.Http;

namespace HuaLingErpApp.Data;

public interface IRepository<TEntity, TKey> where TEntity : class, IEntity<TKey>
    where TKey : IEquatable<TKey> {
    Task<List<TEntity>> GetDataAsync();
    Task<TEntity?> GetByIdAsync(TKey id);
    Task<bool> AddAsync(TEntity entity);
    Task<TEntity> AddAndReturnAsync(TEntity entity);
    Task<bool> UpdateAsync(TEntity entity);
    Task<bool> DeleteAsync(TKey id);
    Task<bool> DeleteBatchAsync(List<TKey>? ids);
    Task<List<TEntity>> QueryAsync(Expression<Func<TEntity, bool>> whereExpression);
    Task<bool> AnyAsync(Expression<Func<TEntity, bool>> whereExpression);
}

/// <summary>
/// 支持审计字段自动填充的仓储实现
/// </summary>
public class AuditableRepository<TEntity, TKey> : IRepository<TEntity, TKey>
    where TEntity : class, IEntity<TKey>, new()
    where TKey : IEquatable<TKey> {
    private readonly ISqlSugarClient _db;
    private readonly IHttpContextAccessor _httpContextAccessor;

    public AuditableRepository(ISqlSugarClient db, IHttpContextAccessor httpContextAccessor) {
        _db = db.AsTenant().GetConnection("Default");
        _httpContextAccessor = httpContextAccessor;
    }

    private ISugarQueryable<TEntity> GetQueryable() => _db.Queryable<TEntity>();

    /// <summary>
    /// 获取当前用户信息
    /// </summary>
    private string GetCurrentUser() {
        var user = _httpContextAccessor.HttpContext?.User;
        if (user?.Identity?.IsAuthenticated == true) {
            return user.FindFirst(ClaimTypes.Name)?.Value
                   ?? user.Identity.Name ?? user.FindFirst(ClaimTypes.Email)?.Value
                   ?? "System";
        }

        return "System";
    }

    /// <summary>
    /// 设置创建审计字段
    /// </summary>
    private void SetCreateAuditFields(TEntity entity) {
        var currentUser = GetCurrentUser();
        var now = DateTime.Now;

        entity.CreatedDate = now;
        entity.RecordDate = now;
        entity.CreatedBy = currentUser;
        entity.ModifiedBy = currentUser;
    }

    /// <summary>
    /// 设置更新审计字段
    /// </summary>
    private void SetUpdateAuditFields(TEntity entity) {
        var currentUser = GetCurrentUser();

        entity.RecordDate = DateTime.Now;
        entity.ModifiedBy = currentUser;
        // 注意：不更新 CreatedDate 和 CreatedBy
    }

    public async Task<List<TEntity>> GetDataAsync() {
        return await GetQueryable().ToListAsync();
    }

    public async Task<TEntity?> GetByIdAsync(TKey id) {
        return await GetQueryable().FirstAsync(x => x.Id.Equals(id));
    }

    public async Task<bool> AddAsync(TEntity entity) {
        SetCreateAuditFields(entity);
        return await _db.Insertable(entity).ExecuteCommandIdentityIntoEntityAsync();
    }

    public async Task<TEntity> AddAndReturnAsync(TEntity entity) {
        SetCreateAuditFields(entity);
        return await _db.Insertable(entity).ExecuteReturnEntityAsync();
    }

    public async Task<bool> UpdateAsync(TEntity entity) {
        SetUpdateAuditFields(entity);
        return await _db.Updateable(entity).ExecuteCommandHasChangeAsync();
    }

    public async Task<bool> DeleteAsync(TKey id) {
        return await _db.Deleteable<TEntity>().In(id).ExecuteCommandHasChangeAsync();
    }

    public async Task<bool> DeleteBatchAsync(List<TKey>? ids) {
        if (ids is null || ids.Count == 0) {
            return false;
        }

        return await _db.Deleteable<TEntity>().Where(x => ids.Contains(x.Id)).ExecuteCommandHasChangeAsync();
    }

    public async Task<List<TEntity>> QueryAsync(Expression<Func<TEntity, bool>> whereExpression) {
        return await GetQueryable().Where(whereExpression).ToListAsync();
    }

    public async Task<bool> AnyAsync(Expression<Func<TEntity, bool>> whereExpression) {
        return await GetQueryable().AnyAsync(whereExpression);
    }
}