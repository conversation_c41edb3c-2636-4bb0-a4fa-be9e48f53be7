using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using SqlSugar;

namespace HuaLingErpApp.Services {
    public interface IAuthService {
        Task<LoginResponse> LoginAsync(LoginRequest request, HttpContext httpContext);
        Task LogoutAsync(HttpContext httpContext);
        Task<bool> CreateUserAsync(CreateUserRequest request, string createdBy);
        Task<bool> UpdateUserAsync(UpdateUserRequest request, string modifiedBy);
        Task<bool> ChangePasswordAsync(ChangePasswordRequest request);
        Task<UserInfo?> GetUserInfoAsync(int userId);
        Task<List<UserInfo>> GetAllUsersAsync();
        Task<bool> DeleteUserAsync(int userId);
        Task<bool> DeleteBatchUsersAsync(List<int> userIds);
        Task LogLoginAttemptAsync(string userName, bool isSuccess, string? failureReason, HttpContext httpContext);
        string HashPassword(string password);
        bool VerifyPassword(string password, string hashedPassword);
        Task<bool> AdminResetPasswordAsync(AdminResetPasswordRequest request, string modifiedBy);
        Task<bool> VerifyCurrentPasswordAsync(int userId, string password);
    }

    public class AuthService : IAuthService {
        private readonly ISqlSugarClient _db;
        private readonly IRepository<User, int> _userRepository;
        private readonly IRepository<Role, int> _roleRepository;
        private readonly IRepository<UserRole, int> _userRoleRepository;
        private readonly IRepository<LoginLog, int> _loginLogRepository;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            ISqlSugarClient db,
            IRepository<User, int> userRepository,
            IRepository<Role, int> roleRepository,
            IRepository<UserRole, int> userRoleRepository,
            IRepository<LoginLog, int> loginLogRepository,
            ILogger<AuthService> logger) {
            _db = db.AsTenant().GetConnection("Default");
            _userRepository = userRepository;
            _roleRepository = roleRepository;
            _userRoleRepository = userRoleRepository;
            _loginLogRepository = loginLogRepository;
            _logger = logger;
        }

        public async Task<LoginResponse> LoginAsync(LoginRequest request, HttpContext httpContext) {
            try {
                // 查找用户
                var users = await _userRepository.QueryAsync(u => u.UserName == request.UserName && u.IsActive);
                var user = users.FirstOrDefault();

                if (user == null) {
                    await LogLoginAttemptAsync(request.UserName, false, "User not found or disabled", httpContext);
                    return new LoginResponse { Success = false, Message = "Invalid username or password" };
                }

                // Verify password
                if (!VerifyPassword(request.Password, user.PasswordHash)) {
                    await LogLoginAttemptAsync(request.UserName, false, "Invalid password", httpContext);
                    return new LoginResponse { Success = false, Message = "Invalid username or password" };
                }

                // Get user roles
                var userRoles = await _db.Queryable<UserRole, Role>((ur, r) => new object[] {
                        JoinType.Inner, ur.RoleId == r.Id
                    })
                    .Where((ur, r) => ur.UserId == user.Id)
                    .Select((ur, r) => r.Name)
                    .ToListAsync();

                // Create identity claims
                var claims = new List<Claim> {
                    new(ClaimTypes.NameIdentifier, user.Id.ToString()),
                    new(ClaimTypes.Name, user.UserName),
                    new(ClaimTypes.Email, user.Email),
                    new("DisplayName", user.DisplayName ?? user.UserName)
                };

                // Add role claims
                foreach (var role in userRoles) {
                    claims.Add(new Claim(ClaimTypes.Role, role));
                }

                var claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);
                var authProperties = new AuthenticationProperties {
                    IsPersistent = request.RememberMe, // 只有勾选 "Keep me signed in" 才持久化
                    ExpiresUtc =
                        request.RememberMe ? DateTimeOffset.UtcNow.AddDays(7) : null // 不勾选时成为会话 Cookie，浏览器关闭时自动登出
                };

                await httpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme,
                    new ClaimsPrincipal(claimsIdentity), authProperties);

                // Update last login time
                user.LastLoginTime = DateTime.Now;
                user.ModifiedBy = user.UserName;
                user.RecordDate = DateTime.Now;
                await _userRepository.UpdateAsync(user);

                // Log successful login
                await LogLoginAttemptAsync(request.UserName, true, null, httpContext);

                var userInfo = new UserInfo {
                    Id = user.Id,
                    UserName = user.UserName,
                    Email = user.Email,
                    DisplayName = user.DisplayName,
                    PhoneNumber = user.PhoneNumber,
                    LastLoginTime = user.LastLoginTime,
                    Roles = userRoles
                };

                return new LoginResponse { Success = true, Message = "Login successful", User = userInfo };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred during login process");
                await LogLoginAttemptAsync(request.UserName, false, "System error", httpContext);
                return new LoginResponse { Success = false, Message = "Login failed, please try again later" };
            }
        }

        public async Task LogoutAsync(HttpContext httpContext) {
            await httpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
        }

        public async Task<bool> CreateUserAsync(CreateUserRequest request, string createdBy) {
            try {
                // Check if username already exists
                var existingUsers = await _userRepository.QueryAsync(u => u.UserName == request.UserName);
                if (existingUsers.Any()) {
                    return false;
                }

                // Check if email already exists
                var existingEmails = await _userRepository.QueryAsync(u => u.Email == request.Email);
                if (existingEmails.Any()) {
                    return false;
                }

                var user = new User {
                    UserName = request.UserName,
                    Email = request.Email,
                    PasswordHash = HashPassword(request.Password),
                    DisplayName = request.DisplayName,
                    PhoneNumber = request.PhoneNumber,
                    IsActive = true,
                    CreatedBy = createdBy,
                    ModifiedBy = createdBy,
                    CreatedDate = DateTime.Now,
                    RecordDate = DateTime.Now
                };

                var newUser = await _userRepository.AddAndReturnAsync(user);

                // Add user roles
                foreach (var roleId in request.RoleIds) {
                    var userRole = new UserRole {
                        UserId = newUser.Id,
                        RoleId = roleId,
                        CreatedBy = createdBy,
                        CreatedDate = DateTime.Now
                    };
                    await _db.Insertable(userRole).ExecuteCommandAsync();
                }

                return true;
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while creating user");
                return false;
            }
        }

        public async Task<bool> UpdateUserAsync(UpdateUserRequest request, string modifiedBy) {
            try {
                var user = await _userRepository.GetByIdAsync(request.Id);
                if (user == null) return false;

                // Check if email is used by other users
                var existingEmails =
                    await _userRepository.QueryAsync(u => u.Email == request.Email && u.Id != request.Id);
                if (existingEmails.Any()) {
                    return false;
                }

                user.Email = request.Email;
                user.DisplayName = request.DisplayName;
                user.PhoneNumber = request.PhoneNumber;
                user.IsActive = request.IsActive;
                user.ModifiedBy = modifiedBy;
                user.RecordDate = DateTime.Now;

                await _userRepository.UpdateAsync(user);

                // Update user roles
                await _db.Deleteable<UserRole>().Where(ur => ur.UserId == request.Id).ExecuteCommandAsync();
                foreach (var roleId in request.RoleIds) {
                    var userRole = new UserRole {
                        UserId = request.Id,
                        RoleId = roleId,
                        CreatedBy = modifiedBy,
                        CreatedDate = DateTime.Now
                    };
                    await _db.Insertable(userRole).ExecuteCommandAsync();
                }

                return true;
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while updating user");
                return false;
            }
        }

        public async Task<bool> ChangePasswordAsync(ChangePasswordRequest request) {
            try {
                var user = await _userRepository.GetByIdAsync(request.UserId);
                if (user == null) return false;

                if (!VerifyPassword(request.CurrentPassword, user.PasswordHash)) {
                    return false;
                }

                user.PasswordHash = HashPassword(request.NewPassword);
                user.ModifiedBy = user.UserName;
                user.RecordDate = DateTime.Now;

                await _userRepository.UpdateAsync(user);
                return true;
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while changing password");
                return false;
            }
        }

        public async Task<UserInfo?> GetUserInfoAsync(int userId) {
            try {
                var user = await _userRepository.GetByIdAsync(userId);
                if (user == null) return null;

                var userRoles = await _db.Queryable<UserRole, Role>((ur, r) => new object[] {
                        JoinType.Inner, ur.RoleId == r.Id
                    })
                    .Where((ur, r) => ur.UserId == userId)
                    .Select((ur, r) => r.Name)
                    .ToListAsync();

                return new UserInfo {
                    Id = user.Id,
                    UserName = user.UserName,
                    Email = user.Email,
                    DisplayName = user.DisplayName,
                    PhoneNumber = user.PhoneNumber,
                    LastLoginTime = user.LastLoginTime,
                    Roles = userRoles
                };
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while getting user information");
                return null;
            }
        }

        public async Task<List<UserInfo>> GetAllUsersAsync() {
            try {
                var users = await _userRepository.GetDataAsync();
                var userInfos = new List<UserInfo>();

                foreach (var user in users) {
                    var userRoles = await _db.Queryable<UserRole, Role>((ur, r) => new object[] {
                            JoinType.Inner, ur.RoleId == r.Id
                        })
                        .Where((ur, r) => ur.UserId == user.Id)
                        .Select((ur, r) => r.Name)
                        .ToListAsync();

                    userInfos.Add(new UserInfo {
                        Id = user.Id,
                        UserName = user.UserName,
                        Email = user.Email,
                        DisplayName = user.DisplayName,
                        PhoneNumber = user.PhoneNumber,
                        LastLoginTime = user.LastLoginTime,
                        Roles = userRoles
                    });
                }

                return userInfos;
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while getting all users");
                return new List<UserInfo>();
            }
        }

        public async Task<bool> DeleteUserAsync(int userId) {
            try {
                // Delete user role associations
                await _db.Deleteable<UserRole>().Where(ur => ur.UserId == userId).ExecuteCommandAsync();

                // Delete user
                await _userRepository.DeleteAsync(userId);
                return true;
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while deleting user");
                return false;
            }
        }

        public async Task<bool> DeleteBatchUsersAsync(List<int> userIds) {
            try {
                // Delete user role associations
                await _db.Deleteable<UserRole>().Where(ur => userIds.Contains(ur.UserId)).ExecuteCommandAsync();

                // Delete users
                await _userRepository.DeleteBatchAsync(userIds);
                return true;
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while deleting users");
                return false;
            }
        }

        public async Task LogLoginAttemptAsync(string userName, bool isSuccess, string? failureReason,
            HttpContext httpContext) {
            try {
                var loginLog = new LoginLog {
                    UserId = 0, // If login fails, there might be no user ID
                    UserName = userName,
                    LoginTime = DateTime.Now,
                    IpAddress = httpContext.Connection.RemoteIpAddress?.ToString(),
                    UserAgent = httpContext.Request.Headers.UserAgent.ToString(),
                    IsSuccess = isSuccess,
                    FailureReason = failureReason,
                    CreatedBy = "System",
                    ModifiedBy = "System",
                    CreatedDate = DateTime.Now,
                    RecordDate = DateTime.Now
                };

                await _loginLogRepository.AddAsync(loginLog);
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while logging login attempt");
            }
        }

        public string HashPassword(string password) {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "HuaLingERP_Salt"));
            return Convert.ToBase64String(hashedBytes);
        }

        public bool VerifyPassword(string password, string hashedPassword) {
            var hashedInput = HashPassword(password);
            return hashedInput == hashedPassword;
        }

        public async Task<bool> AdminResetPasswordAsync(AdminResetPasswordRequest request, string modifiedBy) {
            try {
                var user = await _userRepository.GetByIdAsync(request.UserId);
                if (user == null) return false;

                user.PasswordHash = HashPassword(request.NewPassword);
                user.ModifiedBy = modifiedBy;
                user.RecordDate = DateTime.Now;

                await _userRepository.UpdateAsync(user);
                return true;
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while admin resetting password");
                return false;
            }
        }

        public async Task<bool> VerifyCurrentPasswordAsync(int userId, string password) {
            try {
                var user = await _userRepository.GetByIdAsync(userId);
                if (user == null) return false;

                return VerifyPassword(password, user.PasswordHash);
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while verifying current password");
                return false;
            }
        }
    }
}