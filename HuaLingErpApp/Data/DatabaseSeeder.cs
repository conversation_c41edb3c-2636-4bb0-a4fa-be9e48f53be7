using HuaLingErpApp.Data;
using HuaLingErpApp.Shared.Models;
using SqlSugar;

namespace HuaLingErpApp.Services {
    public interface IDatabaseSeeder {
        Task SeedAsync();
    }

    public class DatabaseSeeder : IDatabaseSeeder {
        private readonly ISqlSugarClient _db;
        private readonly IAuthService _authService;
        private readonly ILogger<DatabaseSeeder> _logger;

        public DatabaseSeeder(
            ISqlSugarClient db,
            IAuthService authService,
            ILogger<DatabaseSeeder> logger) {
            _db = db.AsTenant().GetConnection("Default");
            _authService = authService;
            _logger = logger;
        }

        public async Task SeedAsync() {
            try {
                _logger.LogInformation("Starting database seeding...");

                // Create tables if they don't exist
                await CreateTablesAsync();

                // Seed roles
                await SeedRolesAsync();

                // Seed admin user
                await SeedAdminUserAsync();

                _logger.LogInformation("Database seeding completed successfully");
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred during database seeding");
                throw;
            }
        }

        private async Task CreateTablesAsync() {
            try {
                _logger.LogInformation("Creating database tables...");

                // Create tables
                _db.CodeFirst.InitTables<User>();
                _db.CodeFirst.InitTables<Role>();
                _db.CodeFirst.InitTables<UserRole>();
                _db.CodeFirst.InitTables<LoginLog>();

                _logger.LogInformation("Database tables created successfully");
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while creating database tables");
                throw;
            }
        }

        private async Task SeedRolesAsync() {
            try {
                _logger.LogInformation("Starting to create system roles...");

                foreach (var roleKvp in SystemRoles.RoleDescriptions) {
                    var existingRole = await _db.Queryable<Role>()
                        .Where(r => r.Name == roleKvp.Key)
                        .FirstAsync();

                    if (existingRole == null) {
                        var role = new Role {
                            Name = roleKvp.Key,
                            Description = roleKvp.Value,
                            CreatedBy = "System",
                            ModifiedBy = "System",
                            CreatedDate = DateTime.Now,
                            RecordDate = DateTime.Now
                        };

                        await _db.Insertable(role).ExecuteCommandAsync();
                        _logger.LogInformation($"Role created successfully: \"{roleKvp.Key}\" - \"{roleKvp.Value}\"");
                    }
                    else {
                        _logger.LogInformation($"Role already exists: \"{roleKvp.Key}\"");
                    }
                }
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while creating system roles");
                throw;
            }
        }

        private async Task SeedAdminUserAsync() {
            try {
                _logger.LogInformation("Starting to create admin user...");

                // Check if admin user already exists
                var existingAdmin = await _db.Queryable<User>()
                    .Where(u => u.UserName == "admin")
                    .FirstAsync();

                if (existingAdmin == null) {
                    // Get Administrator role
                    var adminRole = await _db.Queryable<Role>()
                        .Where(r => r.Name == SystemRoles.Administrator)
                        .FirstAsync();

                    if (adminRole == null) {
                        throw new InvalidOperationException(
                            "Administrator role not found. Please ensure roles are seeded first.");
                    }

                    // Create admin user
                    var createUserRequest = new CreateUserRequest {
                        UserName = "admin",
                        Email = "<EMAIL>",
                        Password = "Admin123!", // Default password - should be changed after first login
                        DisplayName = "System Administrator",
                        PhoneNumber = null,
                        RoleIds = new List<int> { adminRole.Id }
                    };

                    var result = await _authService.CreateUserAsync(createUserRequest, "System");

                    if (result) {
                        _logger.LogInformation("Admin user created successfully: \"<EMAIL>\"");
                        _logger.LogWarning(
                            "Default admin password is 'Admin123!' - Please change it after first login for security");
                    }
                    else {
                        _logger.LogError("Failed to create admin user");
                    }
                }
                else {
                    _logger.LogInformation("Admin user already exists");
                }
            }
            catch (Exception ex) {
                _logger.LogError(ex, "Error occurred while creating admin user");
                throw;
            }
        }
    }
}