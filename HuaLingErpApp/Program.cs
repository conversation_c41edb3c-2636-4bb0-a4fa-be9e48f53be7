using System.Text.Json.Serialization;
using System.Globalization;
using Hangfire;
using HuaLingErpApp.Client.Pages;
using HuaLingErpApp.Components;
using HuaLingErpApp.Data;
using HuaLingErpApp.Shared;
using HuaLingErpApp.Shared.Models;
using HuaLingErpApp.Services;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Localization;
using Serilog;
using Serilog.Events;
using SqlSugar;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllers()
    .AddJsonOptions(options => {
        // 处理循环引用
        options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
        // 使用驼峰命名
        options.JsonSerializerOptions.PropertyNamingPolicy = null;
    });
// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents()
    .AddInteractiveWebAssemblyComponents();

// Register IHttpContextAccessor for accessing HTTP context in services
builder.Services.AddHttpContextAccessor();

// Configure localization to English
builder.Services.Configure<RequestLocalizationOptions>(options => {
    var supportedCultures = new[] { "en-US" };
    options.SetDefaultCulture("en-US")
        .AddSupportedCultures(supportedCultures)
        .AddSupportedUICultures(supportedCultures)
        .RequestCultureProviders.Clear(); // Clear all providers to force en-US

    // Add only the default culture provider
    options.RequestCultureProviders.Add(new CustomRequestCultureProvider(context => {
        return Task.FromResult(new ProviderCultureResult("en-US"));
    }));
});

// Set thread culture explicitly
CultureInfo.DefaultThreadCurrentCulture = new CultureInfo("en-US");
CultureInfo.DefaultThreadCurrentUICulture = new CultureInfo("en-US");

// add BootstrapBlazor components with English locale
builder.Services.AddBootstrapBlazor(localizationConfigure: options => {
    options.ResourcesPath = "Resources";
    // Force English locale for BootstrapBlazor components
});
builder.Services.AddBootstrapBlazorTableExportService();

// Add Authentication
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options => {
        options.LoginPath = "/login";
        options.LogoutPath = "/logout";
        options.AccessDeniedPath = "/access-denied";
        // Don't set ExpireTimeSpan here - let AuthService control the expiration based on RememberMe
        // options.ExpireTimeSpan = TimeSpan.FromHours(8);
        options.SlidingExpiration = false; // Disable sliding expiration to respect RememberMe settings
        options.Cookie.HttpOnly = true;
        options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
        options.Cookie.SameSite = SameSiteMode.Lax;
        // 确保会话 Cookie 在浏览器关闭时被删除
        options.Cookie.IsEssential = true;

        // 添加这个：当服务器无法验证Cookie时的处理
        options.Events.OnValidatePrincipal = async context => {
            // 如果无法验证用户身份，清除Cookie
            if (context.Principal?.Identity?.IsAuthenticated != true) {
                context.RejectPrincipal();
                await context.HttpContext.SignOutAsync();
            }
        };

        // Configure API responses for authentication failures
        options.Events.OnRedirectToLogin = context => {
            // Check if this is an API request
            if (context.Request.Path.StartsWithSegments("/api")) {
                // Return JSON 401 instead of redirect for API calls
                context.Response.StatusCode = 401;
                context.Response.ContentType = "application/json";
                var response = new {
                    success = false,
                    message = "Authentication required",
                    statusCode = 401
                };
                return context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
            }

            // For non-API requests, use default redirect behavior
            context.Response.Redirect(context.RedirectUri);
            return Task.CompletedTask;
        };

        options.Events.OnRedirectToAccessDenied = context => {
            // Check if this is an API request
            if (context.Request.Path.StartsWithSegments("/api")) {
                // Return JSON 403 instead of redirect for API calls
                context.Response.StatusCode = 403;
                context.Response.ContentType = "application/json";
                var response = new {
                    success = false,
                    message = "Access denied",
                    statusCode = 403
                };
                return context.Response.WriteAsync(System.Text.Json.JsonSerializer.Serialize(response));
            }

            // For non-API requests, use default redirect behavior
            context.Response.Redirect(context.RedirectUri);
            return Task.CompletedTask;
        };
    });

builder.Services.AddAuthorization();

var connectionString = builder.Configuration.GetConnectionString("Default") ??
                       throw new InvalidOperationException("Connection string 'DefaultConnection' not found.");
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Information()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Warning)
    .MinimumLevel.Override("System", LogEventLevel.Warning)
    .Filter.ByExcluding(logEvent =>
        logEvent.Exception != null &&
        logEvent.Exception.Message.Contains("JavaScript interop calls cannot be issued at this time"))
    .WriteTo.File("logs/app.log",
        outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level}] {SourceContext}: {Message}{NewLine}{Exception}",
        rollingInterval: RollingInterval.Day,
        retainedFileCountLimit: 30)
    .CreateLogger();
builder.Host.UseSerilog();

builder.Services.AddHangfire(configuration => configuration
    .SetDataCompatibilityLevel(CompatibilityLevel.Version_180)
    .UseSimpleAssemblyNameTypeSerializer()
    .UseRecommendedSerializerSettings()
    .UseSqlServerStorage(builder.Configuration.GetConnectionString("Default")));

builder.Services.AddHangfireServer(options => {
    options.WorkerCount = 3; //Environment.ProcessorCount * 1;
    options.Queues = ["alpha"];
});

builder.Services.AddHangfireServer(options => {
    options.WorkerCount = 3;
    options.Queues = ["default"];
});


builder.Services.AddScoped<ISqlSugarClient>(s => {
    SqlSugarScope sqlSugar = new(
        [
            new ConnectionConfig {
                ConfigId = "Default",
                DbType = SqlSugar.DbType.SqlServer,
                ConnectionString = builder.Configuration.GetConnectionString("Default"),
                IsAutoCloseConnection = true
            }
        ],
        db => {
            // Basic SQL logging (optional)
            db.Aop.OnLogExecuting = (sql, pars) => {
                // Log SQL execution if needed
            };
        });
    return sqlSugar;
});

// Register auditable repository (replaces the original repository)
builder.Services.AddScoped(typeof(IRepository<,>), typeof(AuditableRepository<,>));
builder.Services.AddScoped<ApiService>();

// Register authentication services
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddScoped<IDatabaseSeeder, DatabaseSeeder>();

// Register current user service (still needed for other purposes)
builder.Services.AddScoped<ICurrentUserService, CurrentUserService>();


var app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment()) {
    app.UseWebAssemblyDebugging();
}
else {
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
}

app.UseHangfireDashboard("/hangfire", new DashboardOptions {
});
app.UseStaticFiles();
app.UseAntiforgery();

// Add localization middleware
app.UseRequestLocalization();

// Add authentication and authorization middleware
app.UseAuthentication();
app.UseAuthorization();

app.MapControllers();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddInteractiveWebAssemblyRenderMode()
    .AddAdditionalAssemblies(typeof(Counter).Assembly);

// Initialize database with default data
using (var scope = app.Services.CreateScope()) {
    var seeder = scope.ServiceProvider.GetRequiredService<IDatabaseSeeder>();
    await seeder.SeedAsync();
}

app.Run();