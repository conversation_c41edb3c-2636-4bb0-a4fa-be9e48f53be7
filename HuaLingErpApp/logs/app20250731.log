2025-07-31 08:08:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 08:08:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 08:08:33 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 08:08:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 08:08:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 08:08:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 08:08:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 08:08:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 08:08:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc successfully announced in 107.6546 ms
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 successfully announced in 107.6901 ms
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc all the dispatchers started
2025-07-31 08:08:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 all the dispatchers started
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 caught stopping signal...
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc caught stopping signal...
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc All dispatchers stopped
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc successfully reported itself as stopped in 2.6934 ms
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:3e9af2bc has been stopped in total 122.9174 ms
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 All dispatchers stopped
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 successfully reported itself as stopped in 0.8225 ms
2025-07-31 08:29:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2100:a2e1f990 has been stopped in total 149.3254 ms
2025-07-31 08:29:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 08:29:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 08:29:34 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 08:29:34 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 08:29:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 08:29:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 08:29:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 08:29:34 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 08:29:34 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 08:29:34 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 08:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 successfully announced in 97.1832 ms
2025-07-31 08:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 successfully announced in 98.0787 ms
2025-07-31 08:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 08:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 08:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 all the dispatchers started
2025-07-31 08:29:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 all the dispatchers started
2025-07-31 08:31:26 [Error] HuaLingErpApp.Controller.MatlTranController: Failed to retrieve material transactions summary
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 192
ClientConnectionId:c2d222bd-9599-4f8e-a5a9-c1f30608a49a
Error Number:8114,State:5,Class:16
2025-07-31 08:32:14 [Error] HuaLingErpApp.Controller.MatlTranController: Failed to retrieve material transactions summary
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 192
ClientConnectionId:d82df15b-5b1b-47ee-b101-3c6becf6a967
Error Number:8114,State:5,Class:16
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 caught stopping signal...
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 caught stopping signal...
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 caught stopped signal...
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 caught stopped signal...
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 All dispatchers stopped
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 All dispatchers stopped
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 successfully reported itself as stopped in 1.8536 ms
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:3b7eeb76 has been stopped in total 931.119 ms
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 successfully reported itself as stopped in 0.6093 ms
2025-07-31 08:37:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15528:c1b62e09 has been stopped in total 932.3395 ms
2025-07-31 08:38:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 08:38:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 08:38:28 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 08:38:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 08:38:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 08:38:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 08:38:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 08:38:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 08:38:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 08:38:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 08:38:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 08:38:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 08:38:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 08:38:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 08:38:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 08:38:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 08:38:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 08:38:29 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 08:38:29 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 08:38:29 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 08:38:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:e5c5e1dd successfully announced in 105.4609 ms
2025-07-31 08:38:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:ac4e0b2d successfully announced in 105.4499 ms
2025-07-31 08:38:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:e5c5e1dd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 08:38:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:ac4e0b2d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 08:38:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:ac4e0b2d all the dispatchers started
2025-07-31 08:38:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:e5c5e1dd all the dispatchers started
2025-07-31 08:39:07 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total quantity, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 199
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8114,State:5,Class:16
2025-07-31 08:39:07 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total cost, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 209
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8114,State:5,Class:16
2025-07-31 08:39:07 [Error] HuaLingErpApp.Controller.MatlTranController: Failed to retrieve material transactions summary
Microsoft.Data.SqlClient.SqlException (0x80131904): 没有为 'CountTable' 的列 1 指定任何列名称。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetCountAsync()
   at SqlSugar.QueryableProvider`1.CountAsync()
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 217
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8155,State:2,Class:16
2025-07-31 08:39:13 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total quantity, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 199
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8114,State:5,Class:16
2025-07-31 08:39:13 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total cost, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 209
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8114,State:5,Class:16
2025-07-31 08:39:13 [Error] HuaLingErpApp.Controller.MatlTranController: Failed to retrieve material transactions summary
Microsoft.Data.SqlClient.SqlException (0x80131904): 没有为 'CountTable' 的列 1 指定任何列名称。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetCountAsync()
   at SqlSugar.QueryableProvider`1.CountAsync()
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 217
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8155,State:2,Class:16
2025-07-31 08:39:14 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total quantity, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 199
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8114,State:5,Class:16
2025-07-31 08:39:14 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total cost, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 209
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8114,State:5,Class:16
2025-07-31 08:39:14 [Error] HuaLingErpApp.Controller.MatlTranController: Failed to retrieve material transactions summary
Microsoft.Data.SqlClient.SqlException (0x80131904): 没有为 'CountTable' 的列 1 指定任何列名称。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetCountAsync()
   at SqlSugar.QueryableProvider`1.CountAsync()
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 217
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8155,State:2,Class:16
2025-07-31 08:39:16 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total quantity, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 199
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8114,State:5,Class:16
2025-07-31 08:39:16 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total cost, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 209
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8114,State:5,Class:16
2025-07-31 08:39:16 [Error] HuaLingErpApp.Controller.MatlTranController: Failed to retrieve material transactions summary
Microsoft.Data.SqlClient.SqlException (0x80131904): 没有为 'CountTable' 的列 1 指定任何列名称。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetCountAsync()
   at SqlSugar.QueryableProvider`1.CountAsync()
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 217
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8155,State:2,Class:16
2025-07-31 08:39:27 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total quantity, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 199
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8114,State:5,Class:16
2025-07-31 08:39:27 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total cost, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 209
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8114,State:5,Class:16
2025-07-31 08:39:27 [Error] HuaLingErpApp.Controller.MatlTranController: Failed to retrieve material transactions summary
Microsoft.Data.SqlClient.SqlException (0x80131904): 没有为 'CountTable' 的列 1 指定任何列名称。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetCountAsync()
   at SqlSugar.QueryableProvider`1.CountAsync()
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 217
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8155,State:2,Class:16
2025-07-31 08:39:43 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total quantity, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 199
ClientConnectionId:67331961-4983-409e-94e6-6e4a118aba77
Error Number:8114,State:5,Class:16
2025-07-31 08:39:43 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total cost, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 209
ClientConnectionId:67331961-4983-409e-94e6-6e4a118aba77
Error Number:8114,State:5,Class:16
2025-07-31 08:39:43 [Error] HuaLingErpApp.Controller.MatlTranController: Failed to retrieve material transactions summary
Microsoft.Data.SqlClient.SqlException (0x80131904): 没有为 'CountTable' 的列 1 指定任何列名称。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetCountAsync()
   at SqlSugar.QueryableProvider`1.CountAsync()
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 217
ClientConnectionId:67331961-4983-409e-94e6-6e4a118aba77
Error Number:8155,State:2,Class:16
2025-07-31 08:39:49 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total quantity, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 199
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8114,State:5,Class:16
2025-07-31 08:39:49 [Warning] HuaLingErpApp.Controller.MatlTranController: Error calculating total cost, using 0
Microsoft.Data.SqlClient.SqlException (0x80131904): 从数据类型 varchar 转换为 numeric 时出错。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at SqlSugar.DbBindAccessory.GetValueTypeListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.DbBindProvider.DataReaderToListAsync[T](Type type, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](Boolean isComplexModel, Type entityType, IDataReader dataReader)
   at SqlSugar.QueryableProvider`1.GetDataAsync[TResult](KeyValuePair`2 sqlObj)
   at SqlSugar.QueryableProvider`1._ToListAsync[TResult]()
   at SqlSugar.QueryableProvider`1.SumAsync[TResult](String sumField)
   at SqlSugar.QueryableProvider`1._SumAsync[TResult](Expression expression)
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 209
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8114,State:5,Class:16
2025-07-31 08:39:49 [Error] HuaLingErpApp.Controller.MatlTranController: Failed to retrieve material transactions summary
Microsoft.Data.SqlClient.SqlException (0x80131904): 没有为 'CountTable' 的列 1 指定任何列名称。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.GetScalarAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.QueryableProvider`1.GetCountAsync()
   at SqlSugar.QueryableProvider`1.CountAsync()
   at HuaLingErpApp.Controller.MatlTranController.GetSummary(Nullable`1 startDate, Nullable`1 endDate) in C:\HuaLingErpApp\HuaLingErpApp\Controller\MatlTranController.cs:line 217
ClientConnectionId:33610f44-dd6a-42b3-ba2b-87d5954f1c37
Error Number:8155,State:2,Class:16
2025-07-31 09:04:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:e5c5e1dd caught stopping signal...
2025-07-31 09:04:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:ac4e0b2d caught stopping signal...
2025-07-31 09:04:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:e5c5e1dd All dispatchers stopped
2025-07-31 09:04:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:ac4e0b2d All dispatchers stopped
2025-07-31 09:04:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:e5c5e1dd successfully reported itself as stopped in 2.1053 ms
2025-07-31 09:04:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:e5c5e1dd has been stopped in total 463.0354 ms
2025-07-31 09:04:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:ac4e0b2d successfully reported itself as stopped in 0.7214 ms
2025-07-31 09:04:16 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13400:ac4e0b2d has been stopped in total 461.6005 ms
2025-07-31 09:06:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:06:14 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:06:14 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:06:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:06:14 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:06:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:06:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:06:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:06:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:06:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:06:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:06:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:06:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:06:15 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:06:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:06:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:06:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:06:15 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:06:15 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:06:15 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:36bb3bd2 successfully announced in 103.0885 ms
2025-07-31 09:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:45cf0578 successfully announced in 103.0984 ms
2025-07-31 09:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:36bb3bd2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:45cf0578 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:45cf0578 all the dispatchers started
2025-07-31 09:06:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:36bb3bd2 all the dispatchers started
2025-07-31 09:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:45cf0578 caught stopping signal...
2025-07-31 09:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:36bb3bd2 caught stopping signal...
2025-07-31 09:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:36bb3bd2 All dispatchers stopped
2025-07-31 09:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:36bb3bd2 successfully reported itself as stopped in 2.0701 ms
2025-07-31 09:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:36bb3bd2 has been stopped in total 360.5467 ms
2025-07-31 09:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:45cf0578 All dispatchers stopped
2025-07-31 09:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:45cf0578 successfully reported itself as stopped in 0.8324 ms
2025-07-31 09:11:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9968:45cf0578 has been stopped in total 371.1683 ms
2025-07-31 09:11:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:11:44 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:11:44 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:11:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:11:44 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:11:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:11:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:11:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:11:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:11:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:11:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:11:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:11:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:11:45 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:11:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:11:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:11:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:11:45 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:11:45 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:11:45 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:11:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:41f8231d successfully announced in 105.2876 ms
2025-07-31 09:11:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:900c0aa2 successfully announced in 105.0656 ms
2025-07-31 09:11:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:41f8231d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:11:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:900c0aa2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:11:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:900c0aa2 all the dispatchers started
2025-07-31 09:11:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:41f8231d all the dispatchers started
2025-07-31 09:12:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:41f8231d caught stopping signal...
2025-07-31 09:12:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:900c0aa2 caught stopping signal...
2025-07-31 09:12:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:41f8231d All dispatchers stopped
2025-07-31 09:12:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:41f8231d successfully reported itself as stopped in 1.5341 ms
2025-07-31 09:12:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:41f8231d has been stopped in total 157.0092 ms
2025-07-31 09:12:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:900c0aa2 All dispatchers stopped
2025-07-31 09:12:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:900c0aa2 successfully reported itself as stopped in 0.8126 ms
2025-07-31 09:12:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6932:900c0aa2 has been stopped in total 207.3469 ms
2025-07-31 09:12:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:12:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:12:58 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:12:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:12:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:12:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:12:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:12:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:12:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:12:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:09906fa7 successfully announced in 104.1858 ms
2025-07-31 09:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:01fcd87c successfully announced in 104.1759 ms
2025-07-31 09:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:09906fa7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:01fcd87c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:09906fa7 all the dispatchers started
2025-07-31 09:12:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:01fcd87c all the dispatchers started
2025-07-31 09:13:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:01fcd87c caught stopping signal...
2025-07-31 09:13:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:09906fa7 caught stopping signal...
2025-07-31 09:13:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:01fcd87c caught stopped signal...
2025-07-31 09:13:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:09906fa7 caught stopped signal...
2025-07-31 09:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:09906fa7 All dispatchers stopped
2025-07-31 09:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:01fcd87c All dispatchers stopped
2025-07-31 09:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:09906fa7 successfully reported itself as stopped in 2.5278 ms
2025-07-31 09:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:01fcd87c successfully reported itself as stopped in 0.78 ms
2025-07-31 09:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:09906fa7 has been stopped in total 806.8137 ms
2025-07-31 09:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21816:01fcd87c has been stopped in total 807.0896 ms
2025-07-31 09:13:40 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:13:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:13:41 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:13:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:13:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:13:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:13:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:13:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:13:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:13:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:13:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:13:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:13:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:13:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:13:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:13:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:13:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:13:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:13:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:13:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:13:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:90425b8c successfully announced in 104.9227 ms
2025-07-31 09:13:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:94e8ab36 successfully announced in 104.9392 ms
2025-07-31 09:13:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:90425b8c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:13:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:94e8ab36 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:13:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:94e8ab36 all the dispatchers started
2025-07-31 09:13:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:90425b8c all the dispatchers started
2025-07-31 09:14:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:94e8ab36 caught stopping signal...
2025-07-31 09:14:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:90425b8c caught stopping signal...
2025-07-31 09:14:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:94e8ab36 All dispatchers stopped
2025-07-31 09:14:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:94e8ab36 successfully reported itself as stopped in 1.7088 ms
2025-07-31 09:14:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:94e8ab36 has been stopped in total 375.9267 ms
2025-07-31 09:14:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:90425b8c All dispatchers stopped
2025-07-31 09:14:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:90425b8c successfully reported itself as stopped in 0.7965 ms
2025-07-31 09:14:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15848:90425b8c has been stopped in total 425.6906 ms
2025-07-31 09:15:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:15:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:15:30 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:15:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:15:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:15:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:15:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:15:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:15:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:15:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:15:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:e344fa41 successfully announced in 105.1484 ms
2025-07-31 09:15:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:15ba0fbd successfully announced in 105.1832 ms
2025-07-31 09:15:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:e344fa41 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:15:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:15ba0fbd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:15:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:e344fa41 all the dispatchers started
2025-07-31 09:15:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:15ba0fbd all the dispatchers started
2025-07-31 09:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:15ba0fbd caught stopping signal...
2025-07-31 09:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:e344fa41 caught stopping signal...
2025-07-31 09:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:15ba0fbd All dispatchers stopped
2025-07-31 09:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:e344fa41 All dispatchers stopped
2025-07-31 09:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:15ba0fbd successfully reported itself as stopped in 2.0558 ms
2025-07-31 09:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:15ba0fbd has been stopped in total 110.8913 ms
2025-07-31 09:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:e344fa41 successfully reported itself as stopped in 1.1779 ms
2025-07-31 09:18:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18668:e344fa41 has been stopped in total 110.9617 ms
2025-07-31 09:19:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:19:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:19:05 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:19:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:19:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:19:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:19:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:19:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:19:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:19:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:19:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:19:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:19:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:19:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:19:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:19:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:19:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:19:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:19:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:19:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:19:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:b6312dba successfully announced in 106.5323 ms
2025-07-31 09:19:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:4110a40e successfully announced in 106.551 ms
2025-07-31 09:19:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:b6312dba is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:19:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:4110a40e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:19:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:b6312dba all the dispatchers started
2025-07-31 09:19:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:4110a40e all the dispatchers started
2025-07-31 09:25:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:b6312dba caught stopping signal...
2025-07-31 09:25:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:4110a40e caught stopping signal...
2025-07-31 09:25:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:4110a40e All dispatchers stopped
2025-07-31 09:25:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:4110a40e successfully reported itself as stopped in 1.7378 ms
2025-07-31 09:25:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:4110a40e has been stopped in total 295.4741 ms
2025-07-31 09:25:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:b6312dba All dispatchers stopped
2025-07-31 09:25:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:b6312dba successfully reported itself as stopped in 1.0761 ms
2025-07-31 09:25:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13904:b6312dba has been stopped in total 370.6231 ms
2025-07-31 09:26:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:26:47 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:26:48 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:26:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:26:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:26:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:26:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:26:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:26:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:26:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:26:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:26:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:26:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:26:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:26:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:26:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:26:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:26:48 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:26:48 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:26:48 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:26:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:605cd33c successfully announced in 106.9892 ms
2025-07-31 09:26:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:02b9f5cc successfully announced in 108.8116 ms
2025-07-31 09:26:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:02b9f5cc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:26:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:605cd33c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:26:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:605cd33c all the dispatchers started
2025-07-31 09:26:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:02b9f5cc all the dispatchers started
2025-07-31 09:30:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:02b9f5cc caught stopping signal...
2025-07-31 09:30:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:605cd33c caught stopping signal...
2025-07-31 09:30:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:605cd33c caught stopped signal...
2025-07-31 09:30:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:02b9f5cc caught stopped signal...
2025-07-31 09:30:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:605cd33c All dispatchers stopped
2025-07-31 09:30:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:605cd33c successfully reported itself as stopped in 1.7169 ms
2025-07-31 09:30:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:605cd33c has been stopped in total 584.9265 ms
2025-07-31 09:30:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:02b9f5cc All dispatchers stopped
2025-07-31 09:30:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:02b9f5cc successfully reported itself as stopped in 0.6895 ms
2025-07-31 09:30:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21512:02b9f5cc has been stopped in total 587.2227 ms
2025-07-31 09:30:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:30:58 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:30:58 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:30:58 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:30:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:30:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:30:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:30:59 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:30:59 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:30:59 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:044a91f3 successfully announced in 101.4613 ms
2025-07-31 09:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:8ee22723 successfully announced in 101.4767 ms
2025-07-31 09:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:044a91f3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:8ee22723 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:8ee22723 all the dispatchers started
2025-07-31 09:30:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:044a91f3 all the dispatchers started
2025-07-31 09:35:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:044a91f3 caught stopping signal...
2025-07-31 09:35:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:8ee22723 caught stopping signal...
2025-07-31 09:35:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:8ee22723 caught stopped signal...
2025-07-31 09:35:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:044a91f3 caught stopped signal...
2025-07-31 09:35:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:8ee22723 All dispatchers stopped
2025-07-31 09:35:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:044a91f3 All dispatchers stopped
2025-07-31 09:35:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:044a91f3 successfully reported itself as stopped in 0.7448 ms
2025-07-31 09:35:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:8ee22723 successfully reported itself as stopped in 1.7378 ms
2025-07-31 09:35:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:044a91f3 has been stopped in total 961.973 ms
2025-07-31 09:35:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15652:8ee22723 has been stopped in total 961.6429 ms
2025-07-31 09:35:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:35:45 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:35:45 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:35:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:35:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:35:46 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:35:46 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:aab7c14d successfully announced in 104.2976 ms
2025-07-31 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:4d08215d successfully announced in 104.2807 ms
2025-07-31 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:aab7c14d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:4d08215d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:4d08215d all the dispatchers started
2025-07-31 09:35:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:aab7c14d all the dispatchers started
2025-07-31 09:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:aab7c14d caught stopping signal...
2025-07-31 09:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:4d08215d caught stopping signal...
2025-07-31 09:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:aab7c14d All dispatchers stopped
2025-07-31 09:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:4d08215d All dispatchers stopped
2025-07-31 09:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:aab7c14d successfully reported itself as stopped in 1.8122 ms
2025-07-31 09:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:aab7c14d has been stopped in total 426.0065 ms
2025-07-31 09:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:4d08215d successfully reported itself as stopped in 0.7371 ms
2025-07-31 09:42:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22268:4d08215d has been stopped in total 426.3124 ms
2025-07-31 09:42:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:42:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:42:28 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:42:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:42:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:42:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:42:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:42:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:42:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:42:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:c1c13c6f successfully announced in 105.2524 ms
2025-07-31 09:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:771a5cdc successfully announced in 105.2693 ms
2025-07-31 09:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:771a5cdc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:c1c13c6f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:c1c13c6f all the dispatchers started
2025-07-31 09:42:29 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:771a5cdc all the dispatchers started
2025-07-31 09:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:c1c13c6f caught stopping signal...
2025-07-31 09:46:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:771a5cdc caught stopping signal...
2025-07-31 09:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:771a5cdc caught stopped signal...
2025-07-31 09:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:c1c13c6f caught stopped signal...
2025-07-31 09:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:c1c13c6f All dispatchers stopped
2025-07-31 09:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:771a5cdc All dispatchers stopped
2025-07-31 09:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:c1c13c6f successfully reported itself as stopped in 2.1265 ms
2025-07-31 09:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:c1c13c6f has been stopped in total 961.5725 ms
2025-07-31 09:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:771a5cdc successfully reported itself as stopped in 0.8097 ms
2025-07-31 09:46:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1148:771a5cdc has been stopped in total 961.7484 ms
2025-07-31 09:46:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:46:18 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:46:18 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:46:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:46:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:46:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:46:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:46:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:46:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:46:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:46:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:46:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:46:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:46:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:46:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:46:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:46:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:46:19 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:46:19 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:46:19 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:46:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c4a06ee3 successfully announced in 76.9314 ms
2025-07-31 09:46:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c4a06ee3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:46:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:3d141537 successfully announced in 138.6232 ms
2025-07-31 09:46:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:3d141537 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:46:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c4a06ee3 all the dispatchers started
2025-07-31 09:46:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:3d141537 all the dispatchers started
2025-07-31 09:47:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:3d141537 caught stopping signal...
2025-07-31 09:47:19 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c4a06ee3 caught stopping signal...
2025-07-31 09:47:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:3d141537 caught stopped signal...
2025-07-31 09:47:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c4a06ee3 caught stopped signal...
2025-07-31 09:47:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:3d141537 All dispatchers stopped
2025-07-31 09:47:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:3d141537 successfully reported itself as stopped in 3.6108 ms
2025-07-31 09:47:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:3d141537 has been stopped in total 940.9481 ms
2025-07-31 09:47:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c4a06ee3 All dispatchers stopped
2025-07-31 09:47:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c4a06ee3 successfully reported itself as stopped in 0.6844 ms
2025-07-31 09:47:20 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18560:c4a06ee3 has been stopped in total 943.9083 ms
2025-07-31 09:47:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:47:32 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:47:33 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:47:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:47:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:47:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:47:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:47:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:47:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:47:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:47:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:47:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:47:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:47:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:47:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:47:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:47:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:47:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:47:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:47:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:47:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:d4d0dd66 successfully announced in 105.5617 ms
2025-07-31 09:47:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:aed976b2 successfully announced in 105.5536 ms
2025-07-31 09:47:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:aed976b2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:47:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:d4d0dd66 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:47:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:aed976b2 all the dispatchers started
2025-07-31 09:47:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:d4d0dd66 all the dispatchers started
2025-07-31 09:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:aed976b2 caught stopping signal...
2025-07-31 09:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:d4d0dd66 caught stopping signal...
2025-07-31 09:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:aed976b2 All dispatchers stopped
2025-07-31 09:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:d4d0dd66 All dispatchers stopped
2025-07-31 09:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:aed976b2 successfully reported itself as stopped in 1.8561 ms
2025-07-31 09:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:aed976b2 has been stopped in total 312.8152 ms
2025-07-31 09:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:d4d0dd66 successfully reported itself as stopped in 3.029 ms
2025-07-31 09:50:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:608:d4d0dd66 has been stopped in total 315.1638 ms
2025-07-31 09:50:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 09:50:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 09:50:38 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 09:50:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 09:50:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 09:50:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 09:50:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 09:50:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 09:50:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 09:50:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 09:50:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 09:50:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 09:50:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 09:50:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:50:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:50:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:50:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 09:50:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 09:50:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 09:50:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:ba522e66 successfully announced in 107.5263 ms
2025-07-31 09:50:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:67062ab3 successfully announced in 107.2222 ms
2025-07-31 09:50:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:67062ab3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:50:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:ba522e66 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 09:50:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:ba522e66 all the dispatchers started
2025-07-31 09:50:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:67062ab3 all the dispatchers started
2025-07-31 10:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:67062ab3 caught stopping signal...
2025-07-31 10:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:ba522e66 caught stopping signal...
2025-07-31 10:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:ba522e66 caught stopped signal...
2025-07-31 10:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:67062ab3 caught stopped signal...
2025-07-31 10:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:ba522e66 All dispatchers stopped
2025-07-31 10:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:ba522e66 successfully reported itself as stopped in 1.984 ms
2025-07-31 10:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:ba522e66 has been stopped in total 596.8602 ms
2025-07-31 10:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:67062ab3 All dispatchers stopped
2025-07-31 10:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:67062ab3 successfully reported itself as stopped in 0.7921 ms
2025-07-31 10:13:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17876:67062ab3 has been stopped in total 693.1988 ms
2025-07-31 10:13:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 10:13:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 10:13:27 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 10:13:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 10:13:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 10:13:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 10:13:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 10:13:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 10:13:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 10:13:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 10:13:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 10:13:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 10:13:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 10:13:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 10:13:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:13:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:13:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:13:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:13:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:13:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:7a677133 successfully announced in 103.4307 ms
2025-07-31 10:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:822c65c0 successfully announced in 103.4054 ms
2025-07-31 10:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:7a677133 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:822c65c0 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:7a677133 all the dispatchers started
2025-07-31 10:13:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:822c65c0 all the dispatchers started
2025-07-31 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:7a677133 caught stopping signal...
2025-07-31 10:14:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:822c65c0 caught stopping signal...
2025-07-31 10:14:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:822c65c0 All dispatchers stopped
2025-07-31 10:14:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:822c65c0 successfully reported itself as stopped in 1.6022 ms
2025-07-31 10:14:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:822c65c0 has been stopped in total 313.7132 ms
2025-07-31 10:14:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:7a677133 All dispatchers stopped
2025-07-31 10:14:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:7a677133 successfully reported itself as stopped in 1.1153 ms
2025-07-31 10:14:07 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17380:7a677133 has been stopped in total 391.7996 ms
2025-07-31 10:14:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 10:14:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 10:14:21 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 10:14:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 10:14:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 10:14:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 10:14:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 10:14:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 10:14:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 10:14:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 10:14:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 10:14:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 10:14:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 10:14:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 10:14:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:14:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:14:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:14:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:14:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:14:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:00490218 successfully announced in 101.5232 ms
2025-07-31 10:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:a871e3a7 successfully announced in 101.499 ms
2025-07-31 10:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:a871e3a7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:00490218 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:a871e3a7 all the dispatchers started
2025-07-31 10:14:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:00490218 all the dispatchers started
2025-07-31 10:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:00490218 caught stopping signal...
2025-07-31 10:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:a871e3a7 caught stopping signal...
2025-07-31 10:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:a871e3a7 caught stopped signal...
2025-07-31 10:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:00490218 caught stopped signal...
2025-07-31 10:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:a871e3a7 All dispatchers stopped
2025-07-31 10:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:a871e3a7 successfully reported itself as stopped in 4.2139 ms
2025-07-31 10:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:a871e3a7 has been stopped in total 607.0077 ms
2025-07-31 10:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:00490218 All dispatchers stopped
2025-07-31 10:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:00490218 successfully reported itself as stopped in 0.9566 ms
2025-07-31 10:26:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13744:00490218 has been stopped in total 623.2788 ms
2025-07-31 10:26:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 10:26:55 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 10:26:55 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 10:26:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 10:26:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 10:26:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 10:26:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 10:26:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 10:26:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 10:26:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 10:26:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 10:26:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 10:26:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 10:26:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 10:26:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:26:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:26:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:26:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:26:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:26:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:26:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:d8d4edbb successfully announced in 100.7098 ms
2025-07-31 10:26:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:a5aaef5d successfully announced in 100.5178 ms
2025-07-31 10:26:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:d8d4edbb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:26:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:a5aaef5d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:26:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:a5aaef5d all the dispatchers started
2025-07-31 10:26:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:d8d4edbb all the dispatchers started
2025-07-31 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:a5aaef5d caught stopping signal...
2025-07-31 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:d8d4edbb caught stopping signal...
2025-07-31 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:d8d4edbb All dispatchers stopped
2025-07-31 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:d8d4edbb successfully reported itself as stopped in 2.6901 ms
2025-07-31 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:d8d4edbb has been stopped in total 116.199 ms
2025-07-31 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:a5aaef5d All dispatchers stopped
2025-07-31 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:a5aaef5d successfully reported itself as stopped in 0.7199 ms
2025-07-31 10:32:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:11300:a5aaef5d has been stopped in total 118.6125 ms
2025-07-31 10:32:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 10:32:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 10:32:30 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 10:32:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 10:32:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 10:32:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 10:32:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 10:32:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 10:32:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 10:32:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 10:32:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 10:32:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 10:32:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 10:32:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 10:32:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:32:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:32:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:32:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:32:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:32:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:32:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:af60dd54 successfully announced in 98.7763 ms
2025-07-31 10:32:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:c1d5c5cc successfully announced in 98.8331 ms
2025-07-31 10:32:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:af60dd54 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:32:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:c1d5c5cc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:32:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:c1d5c5cc all the dispatchers started
2025-07-31 10:32:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:af60dd54 all the dispatchers started
2025-07-31 10:40:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:af60dd54 caught stopping signal...
2025-07-31 10:40:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:c1d5c5cc caught stopping signal...
2025-07-31 10:40:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:c1d5c5cc caught stopped signal...
2025-07-31 10:40:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:af60dd54 caught stopped signal...
2025-07-31 10:40:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:c1d5c5cc All dispatchers stopped
2025-07-31 10:40:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:c1d5c5cc successfully reported itself as stopped in 1.5183 ms
2025-07-31 10:40:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:c1d5c5cc has been stopped in total 558.5053 ms
2025-07-31 10:40:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:af60dd54 All dispatchers stopped
2025-07-31 10:40:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:af60dd54 successfully reported itself as stopped in 0.9163 ms
2025-07-31 10:40:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19872:af60dd54 has been stopped in total 624.8661 ms
2025-07-31 10:40:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 10:40:48 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 10:40:48 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 10:40:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 10:40:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 10:40:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 10:40:48 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 10:40:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 10:40:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 10:40:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 10:40:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 10:40:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 10:40:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 10:40:49 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 10:40:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:40:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:40:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:40:49 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:40:49 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:40:49 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:40:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:d003ed45 successfully announced in 104.7406 ms
2025-07-31 10:40:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:3a8c9c0c successfully announced in 104.7468 ms
2025-07-31 10:40:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:d003ed45 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:40:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:3a8c9c0c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:40:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:3a8c9c0c all the dispatchers started
2025-07-31 10:40:49 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:d003ed45 all the dispatchers started
2025-07-31 10:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:3a8c9c0c caught stopping signal...
2025-07-31 10:44:08 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:d003ed45 caught stopping signal...
2025-07-31 10:44:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:d003ed45 caught stopped signal...
2025-07-31 10:44:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:3a8c9c0c caught stopped signal...
2025-07-31 10:44:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:3a8c9c0c All dispatchers stopped
2025-07-31 10:44:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:d003ed45 All dispatchers stopped
2025-07-31 10:44:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:3a8c9c0c successfully reported itself as stopped in 1.9727 ms
2025-07-31 10:44:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:3a8c9c0c has been stopped in total 894.6115 ms
2025-07-31 10:44:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:d003ed45 successfully reported itself as stopped in 0.7463 ms
2025-07-31 10:44:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12680:d003ed45 has been stopped in total 894.0326 ms
2025-07-31 10:44:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 10:44:22 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 10:44:23 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 10:44:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 10:44:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 10:44:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 10:44:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 10:44:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 10:44:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 10:44:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 10:44:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 10:44:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 10:44:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 10:44:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 10:44:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:44:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:44:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:44:23 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:44:23 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:44:23 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:44:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:2fdb0242 successfully announced in 104.5636 ms
2025-07-31 10:44:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:379ddfe1 successfully announced in 104.5522 ms
2025-07-31 10:44:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:379ddfe1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:44:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:2fdb0242 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:44:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:2fdb0242 all the dispatchers started
2025-07-31 10:44:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:379ddfe1 all the dispatchers started
2025-07-31 10:54:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:2fdb0242 caught stopping signal...
2025-07-31 10:54:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:379ddfe1 caught stopping signal...
2025-07-31 10:54:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:379ddfe1 caught stopped signal...
2025-07-31 10:54:02 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:2fdb0242 caught stopped signal...
2025-07-31 10:54:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:2fdb0242 All dispatchers stopped
2025-07-31 10:54:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:2fdb0242 successfully reported itself as stopped in 7.9139 ms
2025-07-31 10:54:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:2fdb0242 has been stopped in total 708.4378 ms
2025-07-31 10:54:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:379ddfe1 All dispatchers stopped
2025-07-31 10:54:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:379ddfe1 successfully reported itself as stopped in 0.7928 ms
2025-07-31 10:54:03 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14068:379ddfe1 has been stopped in total 916.7499 ms
2025-07-31 10:54:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 10:54:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 10:54:17 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 10:54:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 10:54:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 10:54:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 10:54:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 10:54:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 10:54:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 10:54:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 10:54:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 10:54:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 10:54:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 10:54:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 10:54:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:54:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:54:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:54:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:54:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:54:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:54:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:69d18c3b successfully announced in 106.8353 ms
2025-07-31 10:54:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:2d185ec2 successfully announced in 106.8203 ms
2025-07-31 10:54:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:2d185ec2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:54:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:69d18c3b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:54:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:2d185ec2 all the dispatchers started
2025-07-31 10:54:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:69d18c3b all the dispatchers started
2025-07-31 10:57:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:69d18c3b caught stopping signal...
2025-07-31 10:57:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:2d185ec2 caught stopping signal...
2025-07-31 10:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:2d185ec2 caught stopped signal...
2025-07-31 10:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:69d18c3b caught stopped signal...
2025-07-31 10:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:2d185ec2 All dispatchers stopped
2025-07-31 10:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:2d185ec2 successfully reported itself as stopped in 1.6931 ms
2025-07-31 10:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:2d185ec2 has been stopped in total 635.3349 ms
2025-07-31 10:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:69d18c3b All dispatchers stopped
2025-07-31 10:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:69d18c3b successfully reported itself as stopped in 0.7163 ms
2025-07-31 10:57:56 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:14180:69d18c3b has been stopped in total 642.9374 ms
2025-07-31 10:58:08 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 10:58:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 10:58:09 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 10:58:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 10:58:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 10:58:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 10:58:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 10:58:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 10:58:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 10:58:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 10:58:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 10:58:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 10:58:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 10:58:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 10:58:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:58:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:58:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:58:09 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:58:09 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:58:09 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:58:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:1a826235 successfully announced in 104.9556 ms
2025-07-31 10:58:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:8780cba2 successfully announced in 105.3481 ms
2025-07-31 10:58:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:8780cba2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:58:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:1a826235 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:58:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:8780cba2 all the dispatchers started
2025-07-31 10:58:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:1a826235 all the dispatchers started
2025-07-31 10:59:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:1a826235 caught stopping signal...
2025-07-31 10:59:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:8780cba2 caught stopping signal...
2025-07-31 10:59:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:8780cba2 caught stopped signal...
2025-07-31 10:59:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:1a826235 caught stopped signal...
2025-07-31 10:59:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:8780cba2 All dispatchers stopped
2025-07-31 10:59:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:8780cba2 successfully reported itself as stopped in 1.7103 ms
2025-07-31 10:59:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:8780cba2 has been stopped in total 944.2249 ms
2025-07-31 10:59:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:1a826235 All dispatchers stopped
2025-07-31 10:59:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:1a826235 successfully reported itself as stopped in 0.8013 ms
2025-07-31 10:59:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19060:1a826235 has been stopped in total 984.8379 ms
2025-07-31 10:59:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 10:59:46 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 10:59:46 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 10:59:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 10:59:46 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 10:59:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 10:59:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 10:59:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 10:59:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 10:59:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 10:59:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 10:59:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 10:59:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 10:59:47 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 10:59:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:59:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:59:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:59:47 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 10:59:47 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 10:59:47 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 10:59:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:2c3e87c6 successfully announced in 102.4762 ms
2025-07-31 10:59:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:feba995c successfully announced in 102.5198 ms
2025-07-31 10:59:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:feba995c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:59:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:2c3e87c6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 10:59:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:2c3e87c6 all the dispatchers started
2025-07-31 10:59:47 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:feba995c all the dispatchers started
2025-07-31 11:04:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:2c3e87c6 caught stopping signal...
2025-07-31 11:04:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:feba995c caught stopping signal...
2025-07-31 11:04:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:2c3e87c6 All dispatchers stopped
2025-07-31 11:04:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:feba995c All dispatchers stopped
2025-07-31 11:04:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:2c3e87c6 successfully reported itself as stopped in 1.6608 ms
2025-07-31 11:04:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:2c3e87c6 has been stopped in total 224.9631 ms
2025-07-31 11:04:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:feba995c successfully reported itself as stopped in 0.7793 ms
2025-07-31 11:04:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:6688:feba995c has been stopped in total 224.8788 ms
2025-07-31 11:04:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 11:04:36 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 11:04:36 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 11:04:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 11:04:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 11:04:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 11:04:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 11:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 11:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 11:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 11:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 11:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 11:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 11:04:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 11:04:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 11:04:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 11:04:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 11:04:37 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 11:04:37 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 11:04:37 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 11:04:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:bc1407eb successfully announced in 105.0593 ms
2025-07-31 11:04:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:05edf1b4 successfully announced in 105.0703 ms
2025-07-31 11:04:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:bc1407eb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 11:04:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:05edf1b4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 11:04:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:bc1407eb all the dispatchers started
2025-07-31 11:04:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:05edf1b4 all the dispatchers started
2025-07-31 11:21:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:bc1407eb caught stopping signal...
2025-07-31 11:21:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:05edf1b4 caught stopping signal...
2025-07-31 11:21:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:bc1407eb All dispatchers stopped
2025-07-31 11:21:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:bc1407eb successfully reported itself as stopped in 1.8972 ms
2025-07-31 11:21:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:bc1407eb has been stopped in total 187.6081 ms
2025-07-31 11:21:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:05edf1b4 All dispatchers stopped
2025-07-31 11:21:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:05edf1b4 successfully reported itself as stopped in 0.6965 ms
2025-07-31 11:21:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12212:05edf1b4 has been stopped in total 247.7683 ms
2025-07-31 11:21:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 11:21:31 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 11:21:31 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 11:21:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 11:21:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 11:21:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 11:21:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 11:21:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 11:21:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 11:21:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 11:21:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 11:21:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 11:21:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 11:21:32 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 11:21:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 11:21:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 11:21:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 11:21:32 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 11:21:32 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 11:21:32 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 11:21:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:bc1d3d42 successfully announced in 109.2041 ms
2025-07-31 11:21:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:faf1c2dc successfully announced in 109.2264 ms
2025-07-31 11:21:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:faf1c2dc is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 11:21:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:bc1d3d42 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 11:21:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:bc1d3d42 all the dispatchers started
2025-07-31 11:21:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:faf1c2dc all the dispatchers started
2025-07-31 13:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:faf1c2dc caught stopping signal...
2025-07-31 13:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:bc1d3d42 caught stopping signal...
2025-07-31 13:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:bc1d3d42 caught stopped signal...
2025-07-31 13:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:faf1c2dc caught stopped signal...
2025-07-31 13:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:faf1c2dc All dispatchers stopped
2025-07-31 13:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:faf1c2dc successfully reported itself as stopped in 2.7099 ms
2025-07-31 13:17:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:faf1c2dc has been stopped in total 659.9659 ms
2025-07-31 13:17:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:bc1d3d42 All dispatchers stopped
2025-07-31 13:17:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:bc1d3d42 successfully reported itself as stopped in 1.1853 ms
2025-07-31 13:17:35 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7276:bc1d3d42 has been stopped in total 964.307 ms
2025-07-31 13:54:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 13:54:41 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 13:54:41 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 13:54:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 13:54:41 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 13:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 13:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 13:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 13:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 13:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 13:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 13:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 13:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 13:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 13:54:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 13:54:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 13:54:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 13:54:42 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 13:54:42 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 13:54:42 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 13:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:69cabd08 successfully announced in 66.0933 ms
2025-07-31 13:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:310ae604 successfully announced in 59.7538 ms
2025-07-31 13:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:310ae604 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 13:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:69cabd08 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 13:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:310ae604 all the dispatchers started
2025-07-31 13:54:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:69cabd08 all the dispatchers started
2025-07-31 13:54:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:69cabd08 caught stopping signal...
2025-07-31 13:54:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:310ae604 caught stopping signal...
2025-07-31 13:54:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:310ae604 caught stopped signal...
2025-07-31 13:54:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:69cabd08 caught stopped signal...
2025-07-31 13:54:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:69cabd08 All dispatchers stopped
2025-07-31 13:54:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:310ae604 All dispatchers stopped
2025-07-31 13:54:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:69cabd08 successfully reported itself as stopped in 3.1547 ms
2025-07-31 13:54:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:310ae604 successfully reported itself as stopped in 1.064 ms
2025-07-31 13:54:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:69cabd08 has been stopped in total 521.1038 ms
2025-07-31 13:54:48 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19276:310ae604 has been stopped in total 520.9356 ms
2025-07-31 13:54:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 13:54:57 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 13:54:57 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 13:54:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 13:54:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 13:54:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 13:54:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 13:54:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 13:54:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 13:54:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 13:54:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 13:54:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 13:54:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 13:54:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 13:54:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 13:54:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 13:54:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 13:54:57 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 13:54:57 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 13:54:57 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 13:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:5cefeea1 successfully announced in 104.6742 ms
2025-07-31 13:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:6943984a successfully announced in 106.4253 ms
2025-07-31 13:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:6943984a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 13:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:5cefeea1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 13:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:6943984a all the dispatchers started
2025-07-31 13:54:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:5cefeea1 all the dispatchers started
2025-07-31 14:14:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:6943984a caught stopping signal...
2025-07-31 14:14:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:5cefeea1 caught stopping signal...
2025-07-31 14:14:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:5cefeea1 All dispatchers stopped
2025-07-31 14:14:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:5cefeea1 successfully reported itself as stopped in 3.0993 ms
2025-07-31 14:14:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:5cefeea1 has been stopped in total 49.0032 ms
2025-07-31 14:14:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:6943984a All dispatchers stopped
2025-07-31 14:14:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:6943984a successfully reported itself as stopped in 1.2999 ms
2025-07-31 14:14:37 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12172:6943984a has been stopped in total 463.7469 ms
2025-07-31 14:59:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 14:59:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 14:59:20 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 14:59:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 14:59:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 14:59:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 14:59:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 14:59:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 14:59:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 14:59:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 14:59:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 14:59:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 14:59:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 14:59:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 14:59:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 14:59:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 14:59:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 14:59:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 14:59:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 14:59:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 14:59:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:eed20054 successfully announced in 108.8574 ms
2025-07-31 14:59:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:546a2d5f successfully announced in 108.8542 ms
2025-07-31 14:59:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:eed20054 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 14:59:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:546a2d5f is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 14:59:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:546a2d5f all the dispatchers started
2025-07-31 14:59:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:eed20054 all the dispatchers started
2025-07-31 15:04:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:546a2d5f caught stopping signal...
2025-07-31 15:04:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:eed20054 caught stopping signal...
2025-07-31 15:04:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:eed20054 All dispatchers stopped
2025-07-31 15:04:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:546a2d5f All dispatchers stopped
2025-07-31 15:04:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:eed20054 successfully reported itself as stopped in 1.6041 ms
2025-07-31 15:04:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:eed20054 has been stopped in total 34.061 ms
2025-07-31 15:04:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:546a2d5f successfully reported itself as stopped in 1.6979 ms
2025-07-31 15:04:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19444:546a2d5f has been stopped in total 34.8821 ms
2025-07-31 15:04:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 15:04:39 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 15:04:39 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 15:04:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 15:04:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 15:04:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 15:04:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 15:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 15:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 15:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 15:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 15:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 15:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 15:04:40 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 15:04:40 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 15:04:40 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 15:04:40 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 15:04:41 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 15:04:41 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 15:04:41 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 15:04:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:560:408ec1b2 successfully announced in 196.4362 ms
2025-07-31 15:04:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:560:3060659b successfully announced in 196.4384 ms
2025-07-31 15:04:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:560:3060659b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 15:04:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:560:408ec1b2 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 15:04:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:560:408ec1b2 all the dispatchers started
2025-07-31 15:04:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:560:3060659b all the dispatchers started
2025-07-31 15:10:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 15:10:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 15:10:17 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 15:10:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 15:10:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 15:10:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 15:10:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 15:10:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 15:10:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 15:10:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 15:10:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 15:10:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 15:10:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 15:10:18 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 15:10:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 15:10:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 15:10:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 15:10:18 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 15:10:18 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 15:10:18 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 15:10:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:223d54f5 successfully announced in 108.4753 ms
2025-07-31 15:10:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:28c7af8e successfully announced in 109.2092 ms
2025-07-31 15:10:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:223d54f5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 15:10:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:28c7af8e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 15:10:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:28c7af8e all the dispatchers started
2025-07-31 15:10:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:223d54f5 all the dispatchers started
2025-07-31 15:15:18 [Information] Hangfire.Server.ServerWatchdog: 2 servers were removed due to timeout
2025-07-31 15:22:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:28c7af8e caught stopping signal...
2025-07-31 15:22:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:223d54f5 caught stopping signal...
2025-07-31 15:22:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:28c7af8e All dispatchers stopped
2025-07-31 15:22:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:28c7af8e successfully reported itself as stopped in 1.9525 ms
2025-07-31 15:22:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:28c7af8e has been stopped in total 288.067 ms
2025-07-31 15:22:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:223d54f5 All dispatchers stopped
2025-07-31 15:22:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:223d54f5 successfully reported itself as stopped in 1.0046 ms
2025-07-31 15:22:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1144:223d54f5 has been stopped in total 585.7128 ms
2025-07-31 15:23:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 15:23:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 15:23:11 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 15:23:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 15:23:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 15:23:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 15:23:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 15:23:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 15:23:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 15:23:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 15:23:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 15:23:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 15:23:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 15:23:11 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 15:23:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 15:23:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 15:23:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 15:23:11 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 15:23:11 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 15:23:11 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 15:23:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:ae0fd570 successfully announced in 104.4617 ms
2025-07-31 15:23:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:600f8bd1 successfully announced in 104.4504 ms
2025-07-31 15:23:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:600f8bd1 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 15:23:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:ae0fd570 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 15:23:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:600f8bd1 all the dispatchers started
2025-07-31 15:23:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:ae0fd570 all the dispatchers started
2025-07-31 15:25:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:600f8bd1 caught stopping signal...
2025-07-31 15:25:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:ae0fd570 caught stopping signal...
2025-07-31 15:25:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:ae0fd570 caught stopped signal...
2025-07-31 15:25:23 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:600f8bd1 caught stopped signal...
2025-07-31 15:25:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:600f8bd1 All dispatchers stopped
2025-07-31 15:25:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:600f8bd1 successfully reported itself as stopped in 1.8818 ms
2025-07-31 15:25:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:600f8bd1 has been stopped in total 557.5153 ms
2025-07-31 15:25:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:ae0fd570 All dispatchers stopped
2025-07-31 15:25:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:ae0fd570 successfully reported itself as stopped in 0.7317 ms
2025-07-31 15:25:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10216:ae0fd570 has been stopped in total 611.1923 ms
2025-07-31 15:25:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 15:25:38 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 15:25:39 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 15:25:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 15:25:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 15:25:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 15:25:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 15:25:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 15:25:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 15:25:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 15:25:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 15:25:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 15:25:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 15:25:39 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 15:25:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 15:25:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 15:25:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 15:25:39 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 15:25:39 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 15:25:39 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 15:25:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:7a61c08a successfully announced in 105.1081 ms
2025-07-31 15:25:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:e75ffd8e successfully announced in 105.1418 ms
2025-07-31 15:25:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:7a61c08a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 15:25:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:e75ffd8e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 15:25:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:7a61c08a all the dispatchers started
2025-07-31 15:25:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:e75ffd8e all the dispatchers started
2025-07-31 15:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:e75ffd8e caught stopping signal...
2025-07-31 15:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:7a61c08a caught stopping signal...
2025-07-31 15:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:7a61c08a caught stopped signal...
2025-07-31 15:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:e75ffd8e caught stopped signal...
2025-07-31 15:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:e75ffd8e All dispatchers stopped
2025-07-31 15:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:e75ffd8e successfully reported itself as stopped in 1.6257 ms
2025-07-31 15:29:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:e75ffd8e has been stopped in total 579.3528 ms
2025-07-31 15:29:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:7a61c08a All dispatchers stopped
2025-07-31 15:29:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:7a61c08a successfully reported itself as stopped in 0.732 ms
2025-07-31 15:29:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13756:7a61c08a has been stopped in total 751.22 ms
2025-07-31 15:30:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 15:30:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 15:30:09 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 15:30:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 15:30:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 15:30:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 15:30:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 15:30:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 15:30:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 15:30:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 15:30:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 15:30:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 15:30:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 15:30:09 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 15:30:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 15:30:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 15:30:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 15:30:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 15:30:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 15:30:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 15:30:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:dc8a5445 successfully announced in 102.2652 ms
2025-07-31 15:30:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:fa2388b3 successfully announced in 102.2736 ms
2025-07-31 15:30:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:fa2388b3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 15:30:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:dc8a5445 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 15:30:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:fa2388b3 all the dispatchers started
2025-07-31 15:30:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:dc8a5445 all the dispatchers started
2025-07-31 15:53:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:dc8a5445 caught stopping signal...
2025-07-31 15:54:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:fa2388b3 caught stopping signal...
2025-07-31 15:54:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:fa2388b3 caught stopped signal...
2025-07-31 15:54:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:dc8a5445 caught stopped signal...
2025-07-31 15:54:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:dc8a5445 All dispatchers stopped
2025-07-31 15:54:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:fa2388b3 All dispatchers stopped
2025-07-31 15:54:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:fa2388b3 successfully reported itself as stopped in 2.272 ms
2025-07-31 15:54:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:fa2388b3 has been stopped in total 837.8296 ms
2025-07-31 15:54:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:dc8a5445 successfully reported itself as stopped in 39.2639 ms
2025-07-31 15:54:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:12240:dc8a5445 has been stopped in total 874.7958 ms
2025-07-31 15:54:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 15:54:21 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 15:54:21 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 15:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 15:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 15:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 15:54:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 15:54:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 15:54:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 15:54:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 15:54:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 15:54:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 15:54:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 15:54:22 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 15:54:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 15:54:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 15:54:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 15:54:22 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 15:54:22 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 15:54:22 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 15:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:cd2d894b successfully announced in 133.7423 ms
2025-07-31 15:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:e041e15a successfully announced in 106.2857 ms
2025-07-31 15:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:e041e15a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 15:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:cd2d894b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 15:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:cd2d894b all the dispatchers started
2025-07-31 15:54:22 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:e041e15a all the dispatchers started
2025-07-31 16:03:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:cd2d894b caught stopping signal...
2025-07-31 16:03:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:e041e15a caught stopping signal...
2025-07-31 16:03:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:e041e15a caught stopped signal...
2025-07-31 16:03:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:cd2d894b caught stopped signal...
2025-07-31 16:03:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:cd2d894b All dispatchers stopped
2025-07-31 16:03:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:cd2d894b successfully reported itself as stopped in 1.7924 ms
2025-07-31 16:03:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:cd2d894b has been stopped in total 632.6942 ms
2025-07-31 16:03:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:e041e15a All dispatchers stopped
2025-07-31 16:03:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:e041e15a successfully reported itself as stopped in 0.7595 ms
2025-07-31 16:03:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13624:e041e15a has been stopped in total 699.1397 ms
2025-07-31 16:03:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-07-31 16:03:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-07-31 16:03:20 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-07-31 16:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-07-31 16:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-07-31 16:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-07-31 16:03:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-07-31 16:03:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-07-31 16:03:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-07-31 16:03:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-07-31 16:03:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-07-31 16:03:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-07-31 16:03:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-07-31 16:03:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-07-31 16:03:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 16:03:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 16:03:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 16:03:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-07-31 16:03:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-07-31 16:03:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-07-31 16:03:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:8d7d6c36 successfully announced in 103.9107 ms
2025-07-31 16:03:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:dbdfb8a5 successfully announced in 103.8901 ms
2025-07-31 16:03:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:8d7d6c36 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 16:03:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:dbdfb8a5 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-07-31 16:03:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:8d7d6c36 all the dispatchers started
2025-07-31 16:03:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:dbdfb8a5 all the dispatchers started
2025-07-31 16:48:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:dbdfb8a5 caught stopping signal...
2025-07-31 16:48:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:8d7d6c36 caught stopping signal...
2025-07-31 16:48:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:dbdfb8a5 All dispatchers stopped
2025-07-31 16:48:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:dbdfb8a5 successfully reported itself as stopped in 4.8258 ms
2025-07-31 16:48:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:dbdfb8a5 has been stopped in total 281.4402 ms
2025-07-31 16:48:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:8d7d6c36 All dispatchers stopped
2025-07-31 16:48:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:8d7d6c36 successfully reported itself as stopped in 0.9247 ms
2025-07-31 16:48:15 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21908:8d7d6c36 has been stopped in total 577.7069 ms
