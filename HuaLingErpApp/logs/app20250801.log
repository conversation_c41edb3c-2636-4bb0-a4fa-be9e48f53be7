2025-08-01 08:05:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 08:05:56 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 08:05:57 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 08:05:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 08:05:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 08:05:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 08:05:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 08:05:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 08:05:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 08:05:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 08:05:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 08:05:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 08:05:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 08:05:57 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 08:05:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 08:05:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 08:05:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 08:05:58 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 08:05:58 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 08:05:58 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 08:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:dddad2ab successfully announced in 105.3722 ms
2025-08-01 08:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:26fa7e71 successfully announced in 105.3851 ms
2025-08-01 08:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:dddad2ab is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 08:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:26fa7e71 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 08:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:26fa7e71 all the dispatchers started
2025-08-01 08:05:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:dddad2ab all the dispatchers started
2025-08-01 08:23:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:26fa7e71 caught stopping signal...
2025-08-01 08:23:59 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:dddad2ab caught stopping signal...
2025-08-01 08:24:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:dddad2ab All dispatchers stopped
2025-08-01 08:24:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:26fa7e71 All dispatchers stopped
2025-08-01 08:24:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:dddad2ab successfully reported itself as stopped in 2.5131 ms
2025-08-01 08:24:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:dddad2ab has been stopped in total 464.3474 ms
2025-08-01 08:24:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:26fa7e71 successfully reported itself as stopped in 1.4608 ms
2025-08-01 08:24:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:20096:26fa7e71 has been stopped in total 465.813 ms
2025-08-01 08:24:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 08:24:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 08:24:23 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 08:24:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 08:24:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 08:24:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 08:24:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 08:24:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 08:24:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 08:24:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 08:24:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 08:24:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 08:24:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 08:24:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 08:24:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 08:24:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 08:24:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 08:24:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 08:24:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 08:24:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 08:24:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:ab361422 successfully announced in 106.9921 ms
2025-08-01 08:24:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:d9fac0d4 successfully announced in 106.8173 ms
2025-08-01 08:24:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:ab361422 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 08:24:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:d9fac0d4 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 08:24:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:d9fac0d4 all the dispatchers started
2025-08-01 08:24:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:ab361422 all the dispatchers started
2025-08-01 08:25:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:ab361422 caught stopping signal...
2025-08-01 08:25:13 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:d9fac0d4 caught stopping signal...
2025-08-01 08:25:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:ab361422 All dispatchers stopped
2025-08-01 08:25:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:ab361422 successfully reported itself as stopped in 1.5978 ms
2025-08-01 08:25:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:ab361422 has been stopped in total 441.2396 ms
2025-08-01 08:25:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:d9fac0d4 All dispatchers stopped
2025-08-01 08:25:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:d9fac0d4 successfully reported itself as stopped in 0.7884 ms
2025-08-01 08:25:14 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19556:d9fac0d4 has been stopped in total 453.2854 ms
2025-08-01 08:25:27 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 08:25:28 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 08:25:28 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 08:25:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 08:25:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 08:25:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 08:25:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 08:25:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 08:25:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 08:25:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 08:25:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 08:25:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 08:25:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 08:25:28 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 08:25:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 08:25:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 08:25:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 08:25:28 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 08:25:28 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 08:25:28 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 08:25:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:cd84fe0d successfully announced in 100.0616 ms
2025-08-01 08:25:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:5745efaf successfully announced in 100.4778 ms
2025-08-01 08:25:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:cd84fe0d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 08:25:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:5745efaf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 08:25:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:5745efaf all the dispatchers started
2025-08-01 08:25:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:cd84fe0d all the dispatchers started
2025-08-01 08:44:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:cd84fe0d caught stopping signal...
2025-08-01 08:44:45 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:5745efaf caught stopping signal...
2025-08-01 08:44:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:5745efaf caught stopped signal...
2025-08-01 08:44:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:cd84fe0d caught stopped signal...
2025-08-01 08:44:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:cd84fe0d All dispatchers stopped
2025-08-01 08:44:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:cd84fe0d successfully reported itself as stopped in 1.8059 ms
2025-08-01 08:44:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:cd84fe0d has been stopped in total 868.1255 ms
2025-08-01 08:44:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:5745efaf All dispatchers stopped
2025-08-01 08:44:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:5745efaf successfully reported itself as stopped in 0.7734 ms
2025-08-01 08:44:46 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18108:5745efaf has been stopped in total 877.8641 ms
2025-08-01 08:45:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 08:45:37 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 08:45:37 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 08:45:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 08:45:37 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 08:45:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 08:45:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 08:45:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 08:45:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 08:45:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 08:45:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 08:45:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 08:45:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 08:45:38 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 08:45:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 08:45:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 08:45:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 08:45:38 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 08:45:38 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 08:45:38 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 08:45:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:5927b66b successfully announced in 101.983 ms
2025-08-01 08:45:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:9d7841e3 successfully announced in 101.9904 ms
2025-08-01 08:45:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:9d7841e3 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 08:45:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:5927b66b is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 08:45:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:5927b66b all the dispatchers started
2025-08-01 08:45:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:9d7841e3 all the dispatchers started
2025-08-01 08:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:9d7841e3 caught stopping signal...
2025-08-01 08:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:5927b66b caught stopping signal...
2025-08-01 08:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:5927b66b All dispatchers stopped
2025-08-01 08:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:9d7841e3 All dispatchers stopped
2025-08-01 08:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:5927b66b successfully reported itself as stopped in 1.5806 ms
2025-08-01 08:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:5927b66b has been stopped in total 490.0703 ms
2025-08-01 08:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:9d7841e3 successfully reported itself as stopped in 0.9977 ms
2025-08-01 08:48:51 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16692:9d7841e3 has been stopped in total 490.7518 ms
2025-08-01 08:49:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 08:49:05 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 08:49:06 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 08:49:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 08:49:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 08:49:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 08:49:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 08:49:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 08:49:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 08:49:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 08:49:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 08:49:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 08:49:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 08:49:06 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 08:49:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 08:49:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 08:49:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 08:49:06 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 08:49:06 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 08:49:06 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 08:49:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:4c7c1d90 successfully announced in 104.3394 ms
2025-08-01 08:49:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:e89cb039 successfully announced in 104.2664 ms
2025-08-01 08:49:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:e89cb039 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 08:49:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:4c7c1d90 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 08:49:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:4c7c1d90 all the dispatchers started
2025-08-01 08:49:06 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:e89cb039 all the dispatchers started
2025-08-01 08:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:4c7c1d90 caught stopping signal...
2025-08-01 08:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:e89cb039 caught stopping signal...
2025-08-01 08:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:4c7c1d90 All dispatchers stopped
2025-08-01 08:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:e89cb039 All dispatchers stopped
2025-08-01 08:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:e89cb039 successfully reported itself as stopped in 1.2494 ms
2025-08-01 08:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:e89cb039 has been stopped in total 159.3373 ms
2025-08-01 08:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:4c7c1d90 successfully reported itself as stopped in 3.565 ms
2025-08-01 08:49:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:9960:4c7c1d90 has been stopped in total 159.8803 ms
2025-08-01 08:49:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 08:49:24 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 08:49:24 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 08:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 08:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 08:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 08:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 08:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 08:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 08:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 08:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 08:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 08:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 08:49:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 08:49:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 08:49:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 08:49:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 08:49:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 08:49:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 08:49:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 08:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:b1c317a8 successfully announced in 104.0986 ms
2025-08-01 08:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:ac6177bf successfully announced in 104.5592 ms
2025-08-01 08:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:ac6177bf is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 08:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:b1c317a8 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 08:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:b1c317a8 all the dispatchers started
2025-08-01 08:49:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:ac6177bf all the dispatchers started
2025-08-01 09:00:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:ac6177bf caught stopping signal...
2025-08-01 09:00:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:b1c317a8 caught stopping signal...
2025-08-01 09:00:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:b1c317a8 All dispatchers stopped
2025-08-01 09:00:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:b1c317a8 successfully reported itself as stopped in 2.3647 ms
2025-08-01 09:00:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:b1c317a8 has been stopped in total 159.5073 ms
2025-08-01 09:00:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:ac6177bf caught stopped signal...
2025-08-01 09:00:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:ac6177bf All dispatchers stopped
2025-08-01 09:00:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:ac6177bf successfully reported itself as stopped in 0.9716 ms
2025-08-01 09:00:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4284:ac6177bf has been stopped in total 856.5791 ms
2025-08-01 09:00:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 09:00:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 09:00:29 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 09:00:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 09:00:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 09:00:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 09:00:29 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 09:00:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 09:00:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 09:00:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 09:00:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 09:00:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 09:00:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 09:00:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 09:00:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 09:00:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 09:00:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 09:00:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 09:00:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 09:00:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 09:00:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:120ee638 successfully announced in 103.2511 ms
2025-08-01 09:00:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:078e7be7 successfully announced in 103.283 ms
2025-08-01 09:00:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:078e7be7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 09:00:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:120ee638 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 09:00:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:078e7be7 all the dispatchers started
2025-08-01 09:00:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:120ee638 all the dispatchers started
2025-08-01 09:15:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:120ee638 caught stopping signal...
2025-08-01 09:15:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:078e7be7 caught stopping signal...
2025-08-01 09:15:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:078e7be7 caught stopped signal...
2025-08-01 09:15:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:120ee638 caught stopped signal...
2025-08-01 09:15:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:120ee638 All dispatchers stopped
2025-08-01 09:15:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:120ee638 successfully reported itself as stopped in 2.0137 ms
2025-08-01 09:15:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:120ee638 has been stopped in total 590.2225 ms
2025-08-01 09:15:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:078e7be7 All dispatchers stopped
2025-08-01 09:15:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:078e7be7 successfully reported itself as stopped in 1.9316 ms
2025-08-01 09:15:44 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18988:078e7be7 has been stopped in total 701.1146 ms
2025-08-01 09:16:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 09:16:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 09:16:26 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 09:16:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 09:16:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 09:16:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 09:16:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 09:16:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 09:16:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 09:16:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 09:16:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 09:16:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 09:16:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 09:16:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 09:16:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 09:16:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 09:16:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 09:16:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 09:16:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 09:16:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 09:16:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:e46b11ce successfully announced in 101.743 ms
2025-08-01 09:16:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:19fd25dd successfully announced in 102.2161 ms
2025-08-01 09:16:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:19fd25dd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 09:16:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:e46b11ce is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 09:16:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:19fd25dd all the dispatchers started
2025-08-01 09:16:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:e46b11ce all the dispatchers started
2025-08-01 09:28:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:19fd25dd caught stopping signal...
2025-08-01 09:28:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:e46b11ce caught stopping signal...
2025-08-01 09:28:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:19fd25dd All dispatchers stopped
2025-08-01 09:28:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:19fd25dd successfully reported itself as stopped in 2.0163 ms
2025-08-01 09:28:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:19fd25dd has been stopped in total 105.1169 ms
2025-08-01 09:28:41 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:e46b11ce caught stopped signal...
2025-08-01 09:28:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:e46b11ce All dispatchers stopped
2025-08-01 09:28:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:e46b11ce successfully reported itself as stopped in 0.9409 ms
2025-08-01 09:28:42 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:13220:e46b11ce has been stopped in total 972.0047 ms
2025-08-01 09:29:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 09:29:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 09:29:03 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 09:29:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 09:29:03 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 09:29:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 09:29:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 09:29:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 09:29:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 09:29:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 09:29:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 09:29:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 09:29:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 09:29:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 09:29:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 09:29:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 09:29:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 09:29:04 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 09:29:04 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 09:29:04 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 09:29:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:3261c311 successfully announced in 103.1914 ms
2025-08-01 09:29:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:7a5359ad successfully announced in 103.1654 ms
2025-08-01 09:29:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:3261c311 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 09:29:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:7a5359ad is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 09:29:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:3261c311 all the dispatchers started
2025-08-01 09:29:04 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:7a5359ad all the dispatchers started
2025-08-01 09:49:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:7a5359ad caught stopping signal...
2025-08-01 09:49:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:3261c311 caught stopping signal...
2025-08-01 09:49:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:3261c311 caught stopped signal...
2025-08-01 09:49:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:7a5359ad caught stopped signal...
2025-08-01 09:49:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:3261c311 All dispatchers stopped
2025-08-01 09:49:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:3261c311 successfully reported itself as stopped in 2.4413 ms
2025-08-01 09:49:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:3261c311 has been stopped in total 572.1486 ms
2025-08-01 09:49:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:7a5359ad All dispatchers stopped
2025-08-01 09:49:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:7a5359ad successfully reported itself as stopped in 1.1109 ms
2025-08-01 09:49:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:15604:7a5359ad has been stopped in total 885.5838 ms
2025-08-01 09:49:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 09:49:53 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 09:49:54 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 09:49:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 09:49:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 09:49:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 09:49:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 09:49:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 09:49:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 09:49:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 09:49:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 09:49:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 09:49:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 09:49:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 09:49:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 09:49:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 09:49:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 09:49:54 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 09:49:54 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 09:49:54 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 09:49:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dca539a7 successfully announced in 105.0557 ms
2025-08-01 09:49:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dcd459ac successfully announced in 105.0685 ms
2025-08-01 09:49:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dcd459ac is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 09:49:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dca539a7 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 09:49:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dca539a7 all the dispatchers started
2025-08-01 09:49:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dcd459ac all the dispatchers started
2025-08-01 09:52:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dcd459ac caught stopping signal...
2025-08-01 09:52:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dca539a7 caught stopping signal...
2025-08-01 09:52:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dca539a7 caught stopped signal...
2025-08-01 09:52:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dcd459ac caught stopped signal...
2025-08-01 09:52:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dca539a7 All dispatchers stopped
2025-08-01 09:52:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dcd459ac All dispatchers stopped
2025-08-01 09:52:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dca539a7 successfully reported itself as stopped in 1.7799 ms
2025-08-01 09:52:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dcd459ac successfully reported itself as stopped in 1.773 ms
2025-08-01 09:52:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dca539a7 has been stopped in total 838.089 ms
2025-08-01 09:52:28 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17784:dcd459ac has been stopped in total 838.3506 ms
2025-08-01 09:54:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 09:54:42 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 09:54:42 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 09:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 09:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 09:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 09:54:42 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 09:54:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 09:54:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 09:54:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 09:54:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 09:54:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 09:54:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 09:54:43 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 09:54:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 09:54:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 09:54:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 09:54:43 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 09:54:43 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 09:54:43 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 09:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:d17e8b0a successfully announced in 104.7222 ms
2025-08-01 09:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:602e4f34 successfully announced in 104.358 ms
2025-08-01 09:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:602e4f34 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 09:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:d17e8b0a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 09:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:602e4f34 all the dispatchers started
2025-08-01 09:54:43 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:d17e8b0a all the dispatchers started
2025-08-01 09:56:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:d17e8b0a caught stopping signal...
2025-08-01 09:56:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:602e4f34 caught stopping signal...
2025-08-01 09:56:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:d17e8b0a All dispatchers stopped
2025-08-01 09:56:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:d17e8b0a successfully reported itself as stopped in 2.1603 ms
2025-08-01 09:56:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:d17e8b0a has been stopped in total 278.646 ms
2025-08-01 09:56:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:602e4f34 All dispatchers stopped
2025-08-01 09:56:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:602e4f34 successfully reported itself as stopped in 1.6359 ms
2025-08-01 09:56:11 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:22396:602e4f34 has been stopped in total 286.306 ms
2025-08-01 09:58:16 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 09:58:17 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 09:58:17 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 09:58:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 09:58:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 09:58:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 09:58:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 09:58:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 09:58:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 09:58:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 09:58:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 09:58:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 09:58:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 09:58:17 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 09:58:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 09:58:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 09:58:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 09:58:17 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 09:58:17 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 09:58:17 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 09:58:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:144f9330 successfully announced in 102.3022 ms
2025-08-01 09:58:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:02e781b9 successfully announced in 102.3329 ms
2025-08-01 09:58:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:02e781b9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 09:58:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:144f9330 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 09:58:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:144f9330 all the dispatchers started
2025-08-01 09:58:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:02e781b9 all the dispatchers started
2025-08-01 10:03:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:144f9330 caught stopping signal...
2025-08-01 10:03:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:02e781b9 caught stopping signal...
2025-08-01 10:03:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:02e781b9 caught stopped signal...
2025-08-01 10:03:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:144f9330 caught stopped signal...
2025-08-01 10:03:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:144f9330 All dispatchers stopped
2025-08-01 10:03:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:02e781b9 All dispatchers stopped
2025-08-01 10:03:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:02e781b9 successfully reported itself as stopped in 0.9328 ms
2025-08-01 10:03:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:02e781b9 has been stopped in total 805.1294 ms
2025-08-01 10:03:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:144f9330 successfully reported itself as stopped in 5.1343 ms
2025-08-01 10:03:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:10092:144f9330 has been stopped in total 808.1221 ms
2025-08-01 10:04:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 10:04:26 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 10:04:26 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 10:04:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 10:04:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 10:04:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 10:04:26 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 10:04:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 10:04:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 10:04:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 10:04:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 10:04:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 10:04:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 10:04:27 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 10:04:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 10:04:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 10:04:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 10:04:27 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 10:04:27 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 10:04:27 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 10:04:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:abc20e88 successfully announced in 103.3743 ms
2025-08-01 10:04:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:50376a83 successfully announced in 103.3786 ms
2025-08-01 10:04:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:abc20e88 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 10:04:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:50376a83 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 10:04:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:abc20e88 all the dispatchers started
2025-08-01 10:04:27 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:50376a83 all the dispatchers started
2025-08-01 10:05:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:abc20e88 caught stopping signal...
2025-08-01 10:05:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:50376a83 caught stopping signal...
2025-08-01 10:05:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:abc20e88 All dispatchers stopped
2025-08-01 10:05:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:abc20e88 successfully reported itself as stopped in 2.4369 ms
2025-08-01 10:05:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:abc20e88 has been stopped in total 246.639 ms
2025-08-01 10:05:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:50376a83 All dispatchers stopped
2025-08-01 10:05:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:50376a83 successfully reported itself as stopped in 1.7059 ms
2025-08-01 10:05:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:7488:50376a83 has been stopped in total 274.324 ms
2025-08-01 10:06:09 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 10:06:10 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 10:06:10 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 10:06:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 10:06:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 10:06:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 10:06:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 10:06:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 10:06:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 10:06:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 10:06:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 10:06:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 10:06:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 10:06:10 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 10:06:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 10:06:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 10:06:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 10:06:10 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 10:06:10 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 10:06:10 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 10:06:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:ddc829a9 successfully announced in 104.9798 ms
2025-08-01 10:06:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:d3176482 successfully announced in 104.9853 ms
2025-08-01 10:06:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:d3176482 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 10:06:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:ddc829a9 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 10:06:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:ddc829a9 all the dispatchers started
2025-08-01 10:06:10 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:d3176482 all the dispatchers started
2025-08-01 10:57:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:ddc829a9 caught stopping signal...
2025-08-01 10:57:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:d3176482 caught stopping signal...
2025-08-01 10:57:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:d3176482 All dispatchers stopped
2025-08-01 10:57:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:d3176482 successfully reported itself as stopped in 12.3975 ms
2025-08-01 10:57:52 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:d3176482 has been stopped in total 209.7377 ms
2025-08-01 10:57:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:ddc829a9 caught stopped signal...
2025-08-01 10:57:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:ddc829a9 All dispatchers stopped
2025-08-01 10:57:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:ddc829a9 successfully reported itself as stopped in 0.8731 ms
2025-08-01 10:57:53 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:1416:ddc829a9 has been stopped in total 985.4644 ms
2025-08-01 11:17:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 11:17:29 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 11:17:30 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 11:17:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 11:17:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 11:17:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 11:17:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 11:17:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 11:17:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 11:17:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 11:17:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 11:17:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 11:17:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 11:17:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 11:17:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 11:17:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 11:17:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 11:17:30 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 11:17:30 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 11:17:30 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 11:17:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b864a305 successfully announced in 108.5519 ms
2025-08-01 11:17:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b3ff97bb successfully announced in 108.8021 ms
2025-08-01 11:17:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b3ff97bb is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 11:17:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b864a305 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 11:17:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b3ff97bb all the dispatchers started
2025-08-01 11:17:30 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b864a305 all the dispatchers started
2025-08-01 11:25:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b864a305 caught stopping signal...
2025-08-01 11:25:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b3ff97bb caught stopping signal...
2025-08-01 11:25:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b3ff97bb caught stopped signal...
2025-08-01 11:25:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b864a305 caught stopped signal...
2025-08-01 11:25:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b3ff97bb All dispatchers stopped
2025-08-01 11:25:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b864a305 All dispatchers stopped
2025-08-01 11:25:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b864a305 successfully reported itself as stopped in 2.3013 ms
2025-08-01 11:25:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b864a305 has been stopped in total 762.2051 ms
2025-08-01 11:25:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b3ff97bb successfully reported itself as stopped in 1.1882 ms
2025-08-01 11:25:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16832:b3ff97bb has been stopped in total 761.9548 ms
2025-08-01 11:25:19 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 11:25:20 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 11:25:20 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 11:25:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 11:25:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 11:25:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 11:25:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 11:25:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 11:25:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 11:25:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 11:25:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 11:25:20 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 11:25:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 11:25:21 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 11:25:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 11:25:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 11:25:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 11:25:21 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 11:25:21 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 11:25:21 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 11:25:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:7ec3c707 successfully announced in 105.8306 ms
2025-08-01 11:25:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:622adbba successfully announced in 106.3077 ms
2025-08-01 11:25:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:7ec3c707 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 11:25:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:622adbba is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 11:25:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:622adbba all the dispatchers started
2025-08-01 11:25:21 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:7ec3c707 all the dispatchers started
2025-08-01 11:29:58 [Information] HuaLingErpApp.Controller.ItemLocationController: Starting Excel import for file: "ItemLocationTemplate.xlsx"
2025-08-01 11:29:58 [Information] HuaLingErpApp.Controller.ItemLocationController: Read 20 rows from Excel file
2025-08-01 11:29:58 [Information] HuaLingErpApp.Controller.ItemLocationController: Inserting 19 item locations
2025-08-01 11:29:58 [Error] HuaLingErpApp.Controller.ItemLocationController: Failed to import Excel file
Microsoft.Data.SqlClient.SqlException (0x80131904): 违反了 UNIQUE KEY 约束“itemloc_pk”。不能在对象“dbo.itemloc”中插入重复键。重复键值为 (091-13100-161, MAIN1)。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__193_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at HuaLingErpApp.Controller.ItemLocationController.ImportExcelFile(IFormFile file, ImportMode mode) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemLocationController.cs:line 675
ClientConnectionId:bf1d5eda-7d3c-4a17-bf0d-ad844d494295
Error Number:2627,State:1,Class:14
2025-08-01 11:31:07 [Information] HuaLingErpApp.Controller.ItemLocationController: Starting Excel import for file: "ItemLocationTemplate.xlsx"
2025-08-01 11:31:07 [Information] HuaLingErpApp.Controller.ItemLocationController: Read 20 rows from Excel file
2025-08-01 11:31:07 [Information] HuaLingErpApp.Controller.ItemLocationController: Inserting 2 item locations
2025-08-01 11:31:07 [Error] HuaLingErpApp.Controller.ItemLocationController: Failed to import Excel file
Microsoft.Data.SqlClient.SqlException (0x80131904): 违反了 UNIQUE KEY 约束“itemloc_pk”。不能在对象“dbo.itemloc”中插入重复键。重复键值为 (091-13100-161, MAIN1)。
语句已终止。
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteNonQuery(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteNonQueryAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteNonQueryAsync>b__193_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SqlSugar.AdoProvider.ExecuteCommandAsync(String sql, SugarParameter[] parameters)
   at SqlSugar.InsertableProvider`1.ExecuteCommandAsync()
   at HuaLingErpApp.Controller.ItemLocationController.ImportExcelFile(IFormFile file, ImportMode mode) in C:\HuaLingErpApp\HuaLingErpApp\Controller\ItemLocationController.cs:line 675
ClientConnectionId:bf1d5eda-7d3c-4a17-bf0d-ad844d494295
Error Number:2627,State:1,Class:14
2025-08-01 11:32:32 [Information] HuaLingErpApp.Controller.ItemLocationController: Starting Excel import for file: "ItemLocationTemplate.xlsx"
2025-08-01 11:32:32 [Information] HuaLingErpApp.Controller.ItemLocationController: Read 1 rows from Excel file
2025-08-01 11:32:32 [Information] HuaLingErpApp.Controller.ItemLocationController: Inserting 1 item locations
2025-08-01 11:32:32 [Information] HuaLingErpApp.Controller.ItemLocationController: Successfully inserted 1 item locations
2025-08-01 11:32:32 [Information] HuaLingErpApp.Controller.ItemLocationController: Import completed. Success: 1, Skipped: 0, Errors: 0
2025-08-01 11:36:24 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate.xlsx"
2025-08-01 11:36:24 [Information] HuaLingErpApp.Controller.ItemController: Read 17 rows from Excel file
2025-08-01 11:36:24 [Information] HuaLingErpApp.Controller.ItemController: Successfully imported 1 items
2025-08-01 11:36:24 [Information] HuaLingErpApp.Controller.ItemController: "Import" completed: 1 processed, 16 skipped, 0 errors
2025-08-01 11:39:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:622adbba caught stopping signal...
2025-08-01 11:39:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:7ec3c707 caught stopping signal...
2025-08-01 11:39:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:7ec3c707 All dispatchers stopped
2025-08-01 11:39:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:7ec3c707 successfully reported itself as stopped in 1.9276 ms
2025-08-01 11:39:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:7ec3c707 has been stopped in total 150.9049 ms
2025-08-01 11:39:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:622adbba All dispatchers stopped
2025-08-01 11:39:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:622adbba successfully reported itself as stopped in 0.7987 ms
2025-08-01 11:39:09 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:17156:622adbba has been stopped in total 385.6279 ms
2025-08-01 11:39:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 11:39:30 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 11:39:30 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 11:39:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 11:39:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 11:39:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 11:39:30 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 11:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 11:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 11:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 11:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 11:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 11:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 11:39:31 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 11:39:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 11:39:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 11:39:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 11:39:31 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 11:39:31 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 11:39:31 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 11:39:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:90fa2943 successfully announced in 105.1766 ms
2025-08-01 11:39:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:845b8a7e successfully announced in 104.9098 ms
2025-08-01 11:39:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:845b8a7e is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 11:39:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:90fa2943 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 11:39:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:90fa2943 all the dispatchers started
2025-08-01 11:39:31 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:845b8a7e all the dispatchers started
2025-08-01 11:40:46 [Information] HuaLingErpApp.Controller.ItemLocationController: Starting Excel import for file: "ItemLocationTemplate.xlsx"
2025-08-01 11:40:46 [Information] HuaLingErpApp.Controller.ItemLocationController: Read 4 rows from Excel file
2025-08-01 11:40:46 [Information] HuaLingErpApp.Controller.ItemLocationController: Processing 1 valid rows out of 4 total rows
2025-08-01 11:40:46 [Information] HuaLingErpApp.Controller.ItemLocationController: Inserting 1 item locations
2025-08-01 11:40:46 [Information] HuaLingErpApp.Controller.ItemLocationController: Successfully inserted 1 item locations
2025-08-01 11:40:46 [Information] HuaLingErpApp.Controller.ItemLocationController: Import completed. Success: 1, Skipped: 3, Errors: 0
2025-08-01 11:46:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:845b8a7e caught stopping signal...
2025-08-01 11:46:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:90fa2943 caught stopping signal...
2025-08-01 11:46:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:845b8a7e All dispatchers stopped
2025-08-01 11:46:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:845b8a7e successfully reported itself as stopped in 1.9983 ms
2025-08-01 11:46:17 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:845b8a7e has been stopped in total 148.4995 ms
2025-08-01 11:46:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:90fa2943 All dispatchers stopped
2025-08-01 11:46:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:90fa2943 successfully reported itself as stopped in 1.0402 ms
2025-08-01 11:46:18 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:21152:90fa2943 has been stopped in total 227.5253 ms
2025-08-01 11:46:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 11:46:33 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 11:46:33 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 11:46:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 11:46:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 11:46:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 11:46:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 11:46:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 11:46:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 11:46:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 11:46:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 11:46:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 11:46:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 11:46:33 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 11:46:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 11:46:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 11:46:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 11:46:33 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 11:46:33 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 11:46:33 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 11:46:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:7244a1ee successfully announced in 170.6228 ms
2025-08-01 11:46:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:d2382258 successfully announced in 170.8727 ms
2025-08-01 11:46:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:d2382258 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 11:46:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:7244a1ee is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 11:46:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:d2382258 all the dispatchers started
2025-08-01 11:46:34 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:7244a1ee all the dispatchers started
2025-08-01 11:47:03 [Information] HuaLingErpApp.Controller.ItemLocationController: Starting Excel import for file: "ItemLocationTemplate.xlsx"
2025-08-01 11:47:03 [Information] HuaLingErpApp.Controller.ItemLocationController: Read 4 rows from Excel file
2025-08-01 11:47:03 [Information] HuaLingErpApp.Controller.ItemLocationController: Processing 1 valid rows out of 4 total rows
2025-08-01 11:47:03 [Information] HuaLingErpApp.Controller.ItemLocationController: Inserting 1 item locations
2025-08-01 11:47:03 [Information] HuaLingErpApp.Controller.ItemLocationController: Successfully inserted 1 item locations
2025-08-01 11:47:03 [Information] HuaLingErpApp.Controller.ItemLocationController: Import completed. Success: 1, Skipped: 3, Errors: 0
2025-08-01 11:48:15 [Information] HuaLingErpApp.Controller.ItemLocationController: Starting Excel import for file: "ItemLocationTemplate.xlsx"
2025-08-01 11:48:15 [Information] HuaLingErpApp.Controller.ItemLocationController: Read 4 rows from Excel file
2025-08-01 11:48:15 [Information] HuaLingErpApp.Controller.ItemLocationController: Processing 1 valid rows out of 4 total rows
2025-08-01 11:48:15 [Information] HuaLingErpApp.Controller.ItemLocationController: Import completed. Success: 0, Skipped: 4, Errors: 0
2025-08-01 11:48:38 [Information] HuaLingErpApp.Controller.ItemLocationController: Starting Excel import for file: "ItemLocationTemplate.xlsx"
2025-08-01 11:48:38 [Information] HuaLingErpApp.Controller.ItemLocationController: Read 4 rows from Excel file
2025-08-01 11:48:38 [Information] HuaLingErpApp.Controller.ItemLocationController: Processing 1 valid rows out of 4 total rows
2025-08-01 11:48:38 [Information] HuaLingErpApp.Controller.ItemLocationController: Import completed. Success: 0, Skipped: 4, Errors: 0
2025-08-01 11:49:33 [Information] HuaLingErpApp.Controller.ItemLocationController: Starting Excel import for file: "ItemLocationTemplate.xlsx"
2025-08-01 11:49:33 [Information] HuaLingErpApp.Controller.ItemLocationController: Read 4 rows from Excel file
2025-08-01 11:49:33 [Information] HuaLingErpApp.Controller.ItemLocationController: Processing 1 valid rows out of 4 total rows
2025-08-01 11:49:33 [Information] HuaLingErpApp.Controller.ItemLocationController: Import completed. Success: 0, Skipped: 4, Errors: 0
2025-08-01 11:49:48 [Information] HuaLingErpApp.Controller.ItemLocationController: Starting Excel import for file: "ItemLocationTemplate.xlsx"
2025-08-01 11:49:48 [Information] HuaLingErpApp.Controller.ItemLocationController: Read 4 rows from Excel file
2025-08-01 11:49:48 [Information] HuaLingErpApp.Controller.ItemLocationController: Processing 1 valid rows out of 4 total rows
2025-08-01 11:49:48 [Information] HuaLingErpApp.Controller.ItemLocationController: Import completed. Success: 0, Skipped: 4, Errors: 0
2025-08-01 11:56:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:7244a1ee caught stopping signal...
2025-08-01 11:56:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:d2382258 caught stopping signal...
2025-08-01 11:56:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:7244a1ee caught stopped signal...
2025-08-01 11:56:38 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:d2382258 caught stopped signal...
2025-08-01 11:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:7244a1ee All dispatchers stopped
2025-08-01 11:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:7244a1ee successfully reported itself as stopped in 1.7594 ms
2025-08-01 11:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:7244a1ee has been stopped in total 757.0146 ms
2025-08-01 11:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:d2382258 All dispatchers stopped
2025-08-01 11:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:d2382258 successfully reported itself as stopped in 0.8665 ms
2025-08-01 11:56:39 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:2860:d2382258 has been stopped in total 865.4823 ms
2025-08-01 11:56:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 11:56:59 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 11:57:00 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 11:57:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 11:57:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 11:57:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 11:57:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 11:57:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 11:57:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 11:57:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 11:57:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 11:57:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 11:57:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 11:57:00 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 11:57:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 11:57:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 11:57:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 11:57:00 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 11:57:00 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 11:57:00 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 11:57:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:e99e2d59 successfully announced in 101.0978 ms
2025-08-01 11:57:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:2b020199 successfully announced in 101.1212 ms
2025-08-01 11:57:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:e99e2d59 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 11:57:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:2b020199 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 11:57:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:2b020199 all the dispatchers started
2025-08-01 11:57:00 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:e99e2d59 all the dispatchers started
2025-08-01 13:06:01 [Information] HuaLingErpApp.Controller.ItemController: Starting Excel import for file: "ItemTemplate.xlsx"
2025-08-01 13:06:01 [Information] HuaLingErpApp.Controller.ItemController: Read 17 rows from Excel file
2025-08-01 13:06:01 [Information] HuaLingErpApp.Controller.ItemController: Successfully updated 17 items
2025-08-01 13:06:01 [Information] HuaLingErpApp.Controller.ItemController: "Update" completed: 17 processed, 0 skipped, 0 errors
2025-08-01 13:06:43 [Information] HuaLingErpApp.Controller.ItemLocationController: Starting Excel import for file: "ItemLocationTemplate.xlsx"
2025-08-01 13:06:43 [Information] HuaLingErpApp.Controller.ItemLocationController: Read 4 rows from Excel file
2025-08-01 13:06:43 [Information] HuaLingErpApp.Controller.ItemLocationController: Processing 1 valid rows out of 4 total rows
2025-08-01 13:06:43 [Information] HuaLingErpApp.Controller.ItemLocationController: Updating 1 item locations
2025-08-01 13:06:43 [Information] HuaLingErpApp.Controller.ItemLocationController: Successfully updated 1 item locations
2025-08-01 13:06:43 [Information] HuaLingErpApp.Controller.ItemLocationController: Import completed. Success: 1, Skipped: 3, Errors: 0
2025-08-01 13:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:2b020199 caught stopping signal...
2025-08-01 13:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:e99e2d59 caught stopping signal...
2025-08-01 13:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:e99e2d59 All dispatchers stopped
2025-08-01 13:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:e99e2d59 successfully reported itself as stopped in 2.6322 ms
2025-08-01 13:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:e99e2d59 has been stopped in total 376.7321 ms
2025-08-01 13:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:2b020199 All dispatchers stopped
2025-08-01 13:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:2b020199 successfully reported itself as stopped in 0.8097 ms
2025-08-01 13:23:32 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16336:2b020199 has been stopped in total 389.4872 ms
2025-08-01 13:23:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 13:23:54 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 13:23:54 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 13:23:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 13:23:54 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 13:23:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 13:23:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 13:23:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 13:23:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 13:23:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 13:23:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 13:23:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 13:23:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 13:23:55 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 13:23:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 13:23:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 13:23:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 13:23:55 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 13:23:55 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 13:23:55 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 13:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:994be008 successfully announced in 101.7833 ms
2025-08-01 13:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:8d1b6d30 successfully announced in 101.7723 ms
2025-08-01 13:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:8d1b6d30 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 13:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:994be008 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 13:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:8d1b6d30 all the dispatchers started
2025-08-01 13:23:55 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:994be008 all the dispatchers started
2025-08-01 13:28:59 [Information] HuaLingErpApp.Controller.ItemLocationController: Starting Excel import for file: "ItemLocationTemplate.xlsx"
2025-08-01 13:28:59 [Information] HuaLingErpApp.Controller.ItemLocationController: Read 4 rows from Excel file
2025-08-01 13:28:59 [Information] HuaLingErpApp.Controller.ItemLocationController: Processing 1 valid rows out of 4 total rows
2025-08-01 13:28:59 [Information] HuaLingErpApp.Controller.ItemLocationController: Updating 1 item locations
2025-08-01 13:28:59 [Information] HuaLingErpApp.Controller.ItemLocationController: Successfully updated 1 item locations
2025-08-01 13:28:59 [Information] HuaLingErpApp.Controller.ItemLocationController: Import completed. Success: 1, Skipped: 3, Errors: 0
2025-08-01 13:29:33 [Information] HuaLingErpApp.Controller.ItemLocationController: Starting Excel import for file: "ItemLocationTemplate.xlsx"
2025-08-01 13:29:33 [Information] HuaLingErpApp.Controller.ItemLocationController: Read 4 rows from Excel file
2025-08-01 13:29:33 [Information] HuaLingErpApp.Controller.ItemLocationController: Processing 1 valid rows out of 4 total rows
2025-08-01 13:29:33 [Information] HuaLingErpApp.Controller.ItemLocationController: Updating 1 item locations
2025-08-01 13:29:33 [Information] HuaLingErpApp.Controller.ItemLocationController: Successfully updated 1 item locations
2025-08-01 13:29:33 [Information] HuaLingErpApp.Controller.ItemLocationController: Import completed. Success: 1, Skipped: 3, Errors: 0
2025-08-01 13:57:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:8d1b6d30 caught stopping signal...
2025-08-01 13:57:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:994be008 caught stopping signal...
2025-08-01 13:57:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:8d1b6d30 All dispatchers stopped
2025-08-01 13:57:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:994be008 All dispatchers stopped
2025-08-01 13:57:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:994be008 successfully reported itself as stopped in 23.3383 ms
2025-08-01 13:57:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:8d1b6d30 successfully reported itself as stopped in 23.2848 ms
2025-08-01 13:57:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:994be008 has been stopped in total 384.1367 ms
2025-08-01 13:57:33 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:18524:8d1b6d30 has been stopped in total 384.8841 ms
2025-08-01 14:37:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 14:37:35 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 14:37:35 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 14:37:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 14:37:35 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 14:37:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 14:37:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 14:37:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 14:37:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 14:37:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 14:37:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 14:37:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 14:37:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 14:37:36 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 14:37:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 14:37:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 14:37:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 14:37:36 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 14:37:36 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 14:37:36 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 14:37:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:a44225dd successfully announced in 115.8169 ms
2025-08-01 14:37:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:b8aa1d1a successfully announced in 115.7953 ms
2025-08-01 14:37:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:a44225dd is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 14:37:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:b8aa1d1a is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 14:37:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:a44225dd all the dispatchers started
2025-08-01 14:37:36 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:b8aa1d1a all the dispatchers started
2025-08-01 15:06:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:a44225dd caught stopping signal...
2025-08-01 15:06:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:b8aa1d1a caught stopping signal...
2025-08-01 15:06:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:b8aa1d1a All dispatchers stopped
2025-08-01 15:06:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:b8aa1d1a successfully reported itself as stopped in 2.5292 ms
2025-08-01 15:06:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:b8aa1d1a has been stopped in total 67.2361 ms
2025-08-01 15:06:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:a44225dd All dispatchers stopped
2025-08-01 15:06:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:a44225dd successfully reported itself as stopped in 0.8423 ms
2025-08-01 15:06:50 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:19244:a44225dd has been stopped in total 112.7087 ms
2025-08-01 15:07:03 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-01 15:07:04 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-01 15:07:04 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-01 15:07:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-01 15:07:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-01 15:07:04 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-01 15:07:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-01 15:07:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-01 15:07:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-01 15:07:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-01 15:07:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-01 15:07:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-01 15:07:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-01 15:07:05 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-01 15:07:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 15:07:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 15:07:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 15:07:05 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-01 15:07:05 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-01 15:07:05 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-01 15:07:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:3cb1b278 successfully announced in 116.6182 ms
2025-08-01 15:07:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:14288e1d successfully announced in 116.6387 ms
2025-08-01 15:07:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:3cb1b278 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 15:07:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:14288e1d is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-01 15:07:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:14288e1d all the dispatchers started
2025-08-01 15:07:05 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:3cb1b278 all the dispatchers started
2025-08-01 15:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:3cb1b278 caught stopping signal...
2025-08-01 15:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:14288e1d caught stopping signal...
2025-08-01 15:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:14288e1d caught stopped signal...
2025-08-01 15:43:57 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:3cb1b278 caught stopped signal...
2025-08-01 15:43:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:3cb1b278 All dispatchers stopped
2025-08-01 15:43:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:14288e1d All dispatchers stopped
2025-08-01 15:43:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:14288e1d successfully reported itself as stopped in 4.1223 ms
2025-08-01 15:43:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:3cb1b278 successfully reported itself as stopped in 4.1212 ms
2025-08-01 15:43:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:14288e1d has been stopped in total 692.2839 ms
2025-08-01 15:43:58 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:16868:3cb1b278 has been stopped in total 692.8863 ms
