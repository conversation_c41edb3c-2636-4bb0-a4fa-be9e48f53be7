2025-08-04 08:17:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Start installing Hangfire SQL objects...
2025-08-04 08:17:23 [Information] Hangfire.SqlServer.SqlServerObjectsInstaller: Hangfire SQL objects installed.
2025-08-04 08:17:23 [Warning] Microsoft.AspNetCore.Components.WebAssembly.Server.WebAssemblyComponentsEndpointOptions: Mapped static asset endpoints not found. Ensure 'MapStaticAssets' is called before 'AddInteractiveWebAssemblyRenderMode'.
2025-08-04 08:17:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting database seeding...
2025-08-04 08:17:23 [Information] HuaLingErpApp.Services.DatabaseSeeder: Creating database tables...
2025-08-04 08:17:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database tables created successfully
2025-08-04 08:17:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create system roles...
2025-08-04 08:17:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Administrator"
2025-08-04 08:17:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Manager"
2025-08-04 08:17:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Operator"
2025-08-04 08:17:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Role already exists: "Viewer"
2025-08-04 08:17:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Starting to create admin user...
2025-08-04 08:17:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Admin user already exists
2025-08-04 08:17:24 [Information] HuaLingErpApp.Services.DatabaseSeeder: Database seeding completed successfully
2025-08-04 08:17:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-04 08:17:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-04 08:17:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'default'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-04 08:17:24 [Information] Hangfire.BackgroundJobServer: Starting Hangfire Server using job storage: 'SQL Server: 10.168.1.120\OA@0412'
2025-08-04 08:17:24 [Information] Hangfire.BackgroundJobServer: Using the following options for SQL Server job storage: Queue poll interval: 00:00:00.
2025-08-04 08:17:24 [Information] Hangfire.BackgroundJobServer: Using the following options for Hangfire Server:
    Worker count: 3
    Listening queues: 'alpha'
    Shutdown timeout: 00:00:15
    Schedule polling interval: 00:00:15
2025-08-04 08:17:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4288:877fe9e6 successfully announced in 118.4447 ms
2025-08-04 08:17:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4288:3f765c9c successfully announced in 118.5315 ms
2025-08-04 08:17:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4288:3f765c9c is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-04 08:17:24 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4288:877fe9e6 is starting the registered dispatchers: ServerWatchdog, ServerJobCancellationWatcher, SqlServerHeartbeatProcess, ExpirationManager, CountersAggregator, Worker, DelayedJobScheduler, RecurringJobScheduler...
2025-08-04 08:17:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4288:3f765c9c all the dispatchers started
2025-08-04 08:17:25 [Information] Hangfire.Server.BackgroundServerProcess: Server win-0rkbtmn7b7u:4288:877fe9e6 all the dispatchers started
